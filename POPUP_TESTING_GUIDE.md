# 🧪 ตัวอย่างการทดสอบ Popup News - พร้อมใช้งาน

## 📱 วิธีการเรียกใช้ในแอป

### 1. เพิ่ม Testing Widget ใน Route
```dart
// ใน routes หรือ main.dart
GetPage(
  name: '/popup-test',
  page: () => PopupTestingWidget(),
),
```

### 2. เรียกใช้ใน Debug Mode
```dart
// ใน main.dart หรือ home page
if (kDebugMode) {
  FloatingActionButton(
    onPressed: () => Get.to(() => PopupTestingWidget()),
    child: Icon(Icons.bug_report),
  ),
}
```

## 🎯 ตัวอย่างการทดสอบแบบต่างๆ

### การทดสอบพื้นฐาน

#### 1. ทดสอบแต่ละกลุ่มผู้ใช้
```dart
// ทดสอบ MR
PopupNewsTestService.quickTestMR();

// ทดสอบ MA
PopupNewsTestService.quickTestMA(); 

// ทดสอบ SS
PopupNewsTestService.quickTestSS();

// ทดสอบลูกค้าทั่วไป
PopupNewsTestService.quickTestGeneral();
```

#### 2. เปลี่ยนกลุ่มผู้ใช้แบบ Manual
```dart
// ใน popup_news_controller.dart บรรทัด ~53
userType = 'mr';    // เปลี่ยนเป็น 'mr', 'ma', 'ss', 'general'
```

### การทดสอบขั้นสูง

#### 3. ทดสอบอัตราส่วนต่างๆ
```dart
// เพิ่มข้อมูลทดสอบอัตราส่วน
PopupNewsTestService.testDifferentAspectRatios();

// แสดงผล
PopupNewsTestService.testPopupForUserGroup('general');
```

#### 4. ทดสอบ Multi-Target Groups
```dart
// เพิ่มข้อมูลทดสอบหลายกลุ่ม
PopupNewsTestService.testMultiTargetGroups();

// ทดสอบกับกลุ่มต่างๆ
PopupNewsTestService.testPopupForUserGroup('mr');
PopupNewsTestService.testPopupForUserGroup('ma');
PopupNewsTestService.testPopupForUserGroup('ss');
```

#### 5. ทดสอบ Popup หมดอายุ
```dart
// เพิ่มข้อมูลทดสอบวันหมดอายุ
PopupNewsTestService.testExpiredPopups();

// ควรแสดงเฉพาะ popup ที่ยังไม่หมดอายุ
PopupNewsTestService.testPopupForUserGroup('general');
```

## 🛠️ การทดสอบแบบกำหนดเอง

### การสร้าง Popup ทดสอบเอง
```dart
List<PopupNews> customPopups = [
  PopupNews(
    id: 9999,
    title: "Popup ทดสอบของฉัน",
    content: "นี่คือเนื้อหาที่ฉันต้องการทดสอบ",
    targetGroups: ['mr', 'ma'], // กลุ่มเป้าหมาย
    aspectRatio: 16.0 / 9.0,    // อัตราส่วน 16:9
    widthPercent: 0.85,         // ความกว้าง 85%
    buttonText: "รับทราบ",
    actionType: "internal",
    actionValue: "/my-page",
    isActive: true,
    createdDate: DateTime.now(),
    expiryDate: DateTime.now().add(Duration(days: 7)),
  ),
];

// เพิ่มและทดสอบ
PopupNewsTestService.addTestPopupNews(customPopups);
PopupNewsTestService.testPopupForUserGroup('mr');
```

### การทดสอบ Scenarios เฉพาะ

#### Scenario 1: ผู้ใช้ MR เห็นข่าวอะไรบ้าง
```dart
// 1. ตั้งค่าเป็น MR
final controller = Get.find<PopupNewsController>();
controller.currentUserGroup.value = 'mr';

// 2. เพิ่มข้อมูลทดสอบ
PopupNewsTestService.addTestPopupNews([
  PopupNews(
    id: 100,
    title: "ข่าวเฉพาะ MR",
    targetGroups: ['mr'],
    // ... properties อื่นๆ
  ),
  PopupNews(
    id: 101, 
    title: "ข่าวสำหรับ MR และ MA",
    targetGroups: ['mr', 'ma'],
    // ... properties อื่นๆ
  ),
  PopupNews(
    id: 102,
    title: "ข่าวทั่วไป",
    targetGroups: ['all'],
    // ... properties อื่นๆ
  ),
]);

// 3. ทดสอบ
PopupNewsTestService.testPopupForUserGroup('mr');
// ผลลัพธ์: ควรเห็น popup ทั้ง 3 อัน
```

#### Scenario 2: ลูกค้าทั่วไปเห็นข่าวอะไรบ้าง
```dart
// ใช้ข้อมูลเดียวกับด้านบน
PopupNewsTestService.testPopupForUserGroup('general');
// ผลลัพธ์: ควรเห็นเฉพาะข่าวทั่วไป (id: 102)
```

## 🔍 การตรวจสอบและ Debug

### 1. ตรวจสอบสถานะปัจจุบัน
```dart
PopupNewsTestService.showCurrentStatus();
```

### 2. ตรวจสอบข้อมูลที่บันทึก
```dart
PopupNewsTestService.checkStoredData();
```

### 3. ดู Console Output
เมื่อใช้ debug mode จะเห็น output แบบนี้:
```
=== MOCKUP USER DATA ===
userType: mr
mrCode: MR001
rankCurrent: platinum
========================
Current user group: mr

=== TESTING POPUP FOR USER GROUP: mr ===
Found 2 active popups for user group: mr
Popup 1: ข้อมูลใหม่สำหรับ MR
  - Target Groups: [mr]
  - Aspect Ratio: 0.75
  - Width Percent: 0.8
Popup 2: ข่าวสำหรับทุกกลุ่ม
  - Target Groups: [all]
  - Aspect Ratio: 1.3333333333333333
  - Width Percent: 0.9
===============================
```

## 🧹 การล้างข้อมูลทดสอบ

### 1. ล้างข้อมูล Popup ที่แสดงไปแล้ว
```dart
PopupNewsTestService.clearShownPopups();
```

### 2. ล้างข้อมูลทดสอบทั้งหมด
```dart
PopupNewsTestService.clearAllTestData();
```

### 3. รีเซ็ตระบบทั้งหมด
```dart
PopupNewsTestService.clearAllTestData();
PopupNewsTestService.clearShownPopups();
PopupNewsController.to.refreshPopupNews();
```

## 📋 Checklist การทดสอบ

### ✅ การทดสอบพื้นฐาน
- [ ] Popup แสดงถูกกลุ่ม (MR, MA, SS, General)
- [ ] ปุ่มปิดทำงาน
- [ ] ปุ่ม action นำทางถูกต้อง
- [ ] Popup ไม่แสดงซ้ำ

### ✅ การทดสอบอัตราส่วน
- [ ] 16:9 (Widescreen) แสดงถูกต้อง
- [ ] 4:3 (Traditional) แสดงถูกต้อง  
- [ ] 1:1 (Square) แสดงถูกต้อง
- [ ] 3:4 (Portrait) แสดงถูกต้อง

### ✅ การทดสอบกลุ่มเป้าหมาย
- [ ] `['mr']` - แสดงเฉพาะ MR
- [ ] `['ma']` - แสดงเฉพาะ MA
- [ ] `['ss']` - แสดงเฉพาะ SS
- [ ] `['general']` - แสดงเฉพาะลูกค้าทั่วไป
- [ ] `['mr', 'ma']` - แสดงให้ MR และ MA
- [ ] `['all']` - แสดงให้ทุกกลุ่ม

### ✅ การทดสอบ Edge Cases
- [ ] Popup หมดอายุไม่แสดง
- [ ] Popup ที่แสดงแล้วไม่แสดงซ้ำ
- [ ] ไม่มีข้อมูลไม่ crash
- [ ] เปลี่ยนกลุ่มผู้ใช้แสดงข้อมูลถูกต้อง

## 🚀 การใช้งานจริง

### 1. ใน Development
```dart
// เพิ่มใน main.dart
if (kDebugMode) {
  Get.put(PopupNewsTestService());
  
  // เพิ่มข้อมูลทดสอบ
  Future.delayed(Duration(seconds: 2), () {
    PopupNewsTestService.addTestPopupNews();
  });
}
```

### 2. ใน Testing Environment
```dart
// สร้าง test file
void main() {
  testWidgets('Popup News should show for correct user group', (tester) async {
    // Setup
    Get.put(PopupNewsController());
    Get.put(PopupNewsTestService());
    
    // Test
    PopupNewsTestService.testPopupForUserGroup('mr');
    
    // Verify
    expect(Get.find<PopupNewsController>().shouldShowPopup.value, true);
  });
}
```

---

**หมายเหตุ**: ใช้ตัวอย่างเหล่านี้เพื่อทดสอบระบบ popup ให้ครอบคลุมและมั่นใจว่าทำงานถูกต้องทุกกรณี 🎯
