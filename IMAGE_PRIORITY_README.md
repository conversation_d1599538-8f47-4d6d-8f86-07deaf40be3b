# Image Priority Implementation for Popup News

## 📷 การจัดการรูปภาพในระบบ Popup

### การเลือกรูปภาพที่จะแสดง:

```dart
imageUrl: newsItem["information_pic_secondary"]?.toString().isNotEmpty == true
    ? newsItem["information_pic_secondary"]
    : (newsItem["information_pic"] ?? "")
```

### ลำดับความสำคัญ:
1. **🥇 First Priority:** `information_pic_secondary`
2. **🥈 Second Priority:** `information_pic`
3. **🥉 Fallback:** Empty string `""`

### เงื่อนไขการเลือกรูป:

#### ✅ ใช้ `information_pic_secondary` เมื่อ:
- มีค่าไม่เป็น `null`
- มีค่าไม่เป็น empty string `""`
- `.toString().isNotEmpty == true`

#### ✅ ใช้ `information_pic` เมื่อ:
- `information_pic_secondary` เป็น `null` หรือ empty
- `information_pic` มีค่า

#### ✅ ใช้ `""` เมื่อ:
- ทั้งสองรูปไม่มีค่าหรือเป็น `null`

## 🧪 การทดสอบ

### API Response ตัวอย่าง:
```json
{
    "information_pic": "https://mapp-app.s3.../primary-image.png",
    "information_pic_secondary": "https://mapp-app.s3.../secondary-image.png"
}
```

### Debug Logs ที่จะแสดง:
```
--- Popup 1 ---
- Primary Image: https://mapp-app.s3.../primary-image.png
- Secondary Image: https://mapp-app.s3.../secondary-image.png
- Selected Image URL: https://mapp-app.s3.../secondary-image.png
```

### กรณีต่างๆ:

#### Case 1: มีทั้งสองรูป
```json
{
    "information_pic": "primary.png",
    "information_pic_secondary": "secondary.png"
}
```
**Result:** ใช้ `secondary.png`

#### Case 2: มีแค่รูปหลัก
```json
{
    "information_pic": "primary.png", 
    "information_pic_secondary": null
}
```
**Result:** ใช้ `primary.png`

#### Case 3: มีแค่รูปรอง (empty)
```json
{
    "information_pic": "primary.png",
    "information_pic_secondary": ""
}
```
**Result:** ใช้ `primary.png`

#### Case 4: ไม่มีรูปทั้งคู่
```json
{
    "information_pic": null,
    "information_pic_secondary": null  
}
```
**Result:** ใช้ `""` (empty string)

## 🔧 Testing Commands

### ทดสอบ API และดูการเลือกรูป:
```dart
popupCtl.testApiDirectly(); // ดู API response
popupCtl.debugPopupStatus(); // ดูสถานะ popup
```

### Debug Output จะแสดง:
1. **API Response** - ข้อมูลรูปทั้งสองจาก API
2. **Selected Image** - รูปที่ถูกเลือกตาม logic
3. **Popup Info** - รูปที่จะแสดงใน popup

## ✅ Implementation Status

- ✅ ให้ความสำคัญ `information_pic_secondary` ก่อน
- ✅ Fallback ไป `information_pic` หากไม่มีรูปรอง
- ✅ Handle null และ empty string ได้ถูกต้อง
- ✅ Debug logs แสดงข้อมูลครบถ้วน
- ✅ Test methods พร้อมใช้งาน

---
*Updated: August 7, 2025 - Image priority implementation complete*
