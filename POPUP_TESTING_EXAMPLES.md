# 🧪 ตัวอย่างการทดสอบ Popup News System

## 📋 การทดสอบพื้นฐาน

### 1. ทดสอบกลุ่มผู้ใช้แต่ละประเภท

#### วิธีที่ 1: เปลี่ยนใน Controller
แก้ไขในไฟล์ `popup_news_controller.dart` บรรทัด ~53:
```dart
// === MOCKUP DATA สำหรับทดสอบ ===
if (kDebugMode) {
  userType = 'mr';    // เปลี่ยนค่านี้
  mrCode = 'MR001';
  rankCurrent = 'platinum';
}
```

**ค่าที่ใช้ทดสอบ:**
- `'mr'` - ทดสอบกลุ่ม MR
- `'ma'` - ทดสอบกลุ่ม MA
- `'ss'` - ทดสอบกลุ่ม SS
- `'general'` - ทดสอบลูกค้าทั่วไป
- `''` (ว่าง) - ทดสอบ default เป็น general

#### วิธีที่ 2: ใช้ Test Service
```dart
// ใน debug console หรือ floating button
PopupNewsTestService.to.testPopupForUserGroup('mr');
PopupNewsTestService.to.testPopupForUserGroup('ma');
PopupNewsTestService.to.testPopupForUserGroup('ss');
PopupNewsTestService.to.testPopupForUserGroup('general');
```

### 2. ทดสอบอัตราส่วนต่างๆ

```dart
// ในไฟล์ popup_news_test_service.dart
PopupNewsTestService.to.addTestPopupNews([
  PopupNews(
    id: 100,
    title: "ทดสอบ 16:9 Widescreen",
    content: "ทดสอบอัตราส่วน 16:9",
    aspectRatio: 1.78,
    widthPercent: 0.9,
    targetGroups: ['mr'],
  ),
  PopupNews(
    id: 101,
    title: "ทดสอบ 4:3 Traditional",
    content: "ทดสอบอัตราส่วน 4:3",
    aspectRatio: 1.33,
    widthPercent: 0.8,
    targetGroups: ['ma'],
  ),
  PopupNews(
    id: 102,
    title: "ทดสอบ 1:1 Square",
    content: "ทดสอบอัตราส่วน 1:1",
    aspectRatio: 1.0,
    widthPercent: 0.7,
    targetGroups: ['ss'],
  ),
]);
```

## 🔄 การทดสอบ Scenarios

### Scenario 1: ทดสอบ MR User
```dart
// 1. ตั้งค่าผู้ใช้
userType = 'mr';

// 2. เพิ่มข้อมูลทดสอบ
PopupNewsTestService.to.addTestPopupNews([
  PopupNews(
    id: 201,
    title: "ข่าวพิเศษสำหรับ MR",
    content: "เนื้อหาข่าวพิเศษ",
    targetGroups: ['mr'],
    aspectRatio: 1.78,
    widthPercent: 0.9,
  ),
  PopupNews(
    id: 202,
    title: "ข่าวทั่วไป",
    content: "ข่าวสำหรับทุกคน",
    targetGroups: ['all'],
    aspectRatio: 1.33,
    widthPercent: 0.85,
  ),
]);

// 3. ทดสอบ
PopupNewsTestService.to.testPopupForUserGroup('mr');

// ผลลัพธ์ที่คาดหวัง: แสดง popup ทั้ง 2 อัน
```

### Scenario 2: ทดสอบ General User
```dart
// 1. ตั้งค่าผู้ใช้
userType = 'general';

// 2. เพิ่มข้อมูลทดสอบ (ใช้ข้อมูลเดียวกับด้านบน)

// 3. ทดสอบ
PopupNewsTestService.to.testPopupForUserGroup('general');

// ผลลัพธ์ที่คาดหวัง: แสดงเฉพาะข่าวทั่วไป (id: 202)
```

### Scenario 3: ทดสอบ Multi-Target Groups
```dart
PopupNewsTestService.to.addTestPopupNews([
  PopupNews(
    id: 301,
    title: "ข่าวสำหรับ MR และ MA",
    content: "ข่าวสำหรับ 2 กลุ่ม",
    targetGroups: ['mr', 'ma'],
    aspectRatio: 1.5,
    widthPercent: 0.8,
  ),
]);

// ทดสอบด้วย MR
userType = 'mr';
PopupNewsTestService.to.testPopupForUserGroup('mr');
// ควรเห็น popup

// ทดสอบด้วย MA
userType = 'ma';
PopupNewsTestService.to.testPopupForUserGroup('ma');
// ควรเห็น popup

// ทดสอบด้วย SS
userType = 'ss';
PopupNewsTestService.to.testPopupForUserGroup('ss');
// ไม่ควรเห็น popup
```

## 🐛 การทดสอบ Edge Cases

### Case 1: ไม่มีข้อมูล Popup
```dart
// 1. เคลียร์ข้อมูลทั้งหมด
PopupNewsTestService.to.clearAllTestData();

// 2. ทดสอบ
PopupNewsTestService.to.testPopupForUserGroup('mr');

// ผลลัพธ์ที่คาดหวัง: ไม่แสดง popup ใดๆ
```

### Case 2: Popup หมดอายุ
```dart
PopupNewsTestService.to.addTestPopupNews([
  PopupNews(
    id: 401,
    title: "ข่าวหมดอายุ",
    content: "ข่าวนี้หมดอายุแล้ว",
    targetGroups: ['mr'],
    expiryDate: DateTime.now().subtract(Duration(days: 1)), // หมดอายุเมื่อวาน
  ),
]);

// ผลลัพธ์ที่คาดหวัง: ไม่แสดง popup
```

### Case 3: Popup ที่แสดงไปแล้ว
```dart
// 1. เพิ่ม popup
PopupNewsTestService.to.addTestPopupNews([
  PopupNews(id: 501, title: "ข่าวทดสอบ", targetGroups: ['mr']),
]);

// 2. แสดงครั้งแรก
PopupNewsTestService.to.testPopupForUserGroup('mr');
// กด "ปิด" หรือ "ดูเพิ่มเติม"

// 3. ทดสอบแสดงอีกครั้ง
PopupNewsTestService.to.testPopupForUserGroup('mr');

// ผลลัพธ์ที่คาดหวัง: ไม่แสดง popup (เพราะแสดงไปแล้ว)
```

## 📱 วิธีการทดสอบใน App

### 1. Debug Mode Testing
```dart
// ใน main.dart หรือที่อื่นๆ
if (kDebugMode) {
  // เพิ่ม popup ทดสอบ
  PopupNewsTestService.to.addTestPopupNews([
    PopupNews(
      id: 999,
      title: "Debug Test Popup",
      content: "นี่คือ popup สำหรับทดสอบ",
      targetGroups: ['mr'],
      aspectRatio: 1.78,
      widthPercent: 0.9,
    ),
  ]);
  
  // แสดง popup ทันที
  Future.delayed(Duration(seconds: 2), () {
    PopupNewsTestService.to.testPopupForUserGroup('mr');
  });
}
```

### 2. Manual Testing
```dart
// เพิ่มปุ่มทดสอบใน UI
ElevatedButton(
  onPressed: () {
    PopupNewsTestService.to.showCurrentStatus();
  },
  child: Text("ดูสถานะปัจจุบัน"),
),

ElevatedButton(
  onPressed: () {
    PopupNewsTestService.to.clearShownPopups();
    PopupNewsController.to.refreshPopupNews();
  },
  child: Text("รีเซ็ต Popup"),
),

ElevatedButton(
  onPressed: () {
    PopupNewsTestService.to.testPopupForUserGroup('mr');
  },
  child: Text("ทดสอบ MR"),
),
```

## 📊 ตรวจสอบผลลัพธ์

### Debug Console Output
```
=== MOCKUP USER DATA ===
userType: mr
mrCode: MR001
rankCurrent: platinum
========================
Current user group: mr

=== POPUP TEST SERVICE ===
Testing popup for user group: mr
Found 2 active popups for user group: mr
Showing popup: ข่าวพิเศษสำหรับ MR
Target groups: [mr]
===============================
```

### SharedPreferences Checking
```dart
// ตรวจสอบข้อมูลที่บันทึก
PopupNewsTestService.to.checkStoredData();

// Output:
// Last shown date: 2025-08-06
// Shown popup IDs: [201, 202]
```

## 🧹 การเคลียร์ข้อมูลทดสอบ

```dart
// เคลียร์ popup ที่แสดงไปแล้ว
PopupNewsTestService.to.clearShownPopups();

// เคลียร์ข้อมูลทดสอบทั้งหมด
PopupNewsTestService.to.clearAllTestData();

// รีเซ็ตระบบ popup ทั้งหมด
PopupNewsController.to.refreshPopupNews();
```

## 🔍 การ Debug ปัญหา

### ถ้า Popup ไม่แสดง
1. ตรวจสอบ user group: `PopupNewsTestService.to.showCurrentStatus()`
2. ตรวจสอบว่ามี popup สำหรับกลุ่มนั้นหรือไม่
3. ตรวจสอบว่า popup หมดอายุหรือไม่
4. ตรวจสอบว่า popup แสดงไปแล้วหรือไม่

### ถ้า Popup แสดงผิดขนาด
1. ตรวจสอบ `aspectRatio` และ `widthPercent`
2. ดู console output สำหรับการคำนวณขนาด
3. ทดสอบกับอัตราส่วนต่างๆ

### ถ้าข้อมูลไม่อัปเดต
1. เรียก `PopupNewsController.to.refreshPopupNews()`
2. ตรวจสอบ `isLoading.value`
3. ดู error messages ใน console

---

**หมายเหตุ**: ใช้ตัวอย่างเหล่านี้เพื่อทดสอบระบบ popup ให้ครอบคลุมทุกกรณี
