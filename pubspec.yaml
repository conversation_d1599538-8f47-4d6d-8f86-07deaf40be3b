name: mapp_prachakij_v3
description: Flutter V3 App Prachakij

publish_to: 'none' # Remove this line if you wish to publish to pub.dev


# use shorebird for build versiony
# command: shorebird releaseexport PATH="/Users/<USER>/.shorebird/bin:$PATH"
# shorebird release android
# shorebird release ios
version: 2.5.10+2508 # ANDROID VERSION CODE
#version: 2.5.9+9 # IOS VERSION CODE

# flutter build appbundle --build-name=2.4.37 --build-number=2437

environment:
  sdk: '>=3.3.1 <4.0.0'

dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  flutter_lints: ^4.0.0
  flutter_secure_storage: ^8.0.0
  http: ^0.13.5
  shared_preferences: ^2.0.15
  pin_input_text_field: ^4.2.0
  timer_count_down: ^2.2.1
  rive: ^0.13.7
  loading_animations: ^2.2.0
#  firebase_messaging: ^15.0.2
  firebase_messaging: ^15.1.0
#  firebase_core: ^3.1.1
  firebase_core: ^3.4.0
  firebase_analytics: ^11.1.0
#  flutter_line_sdk: ^2.3.1
  flutter_line_sdk: ^2.6.1
  flutter_svg: ^1.1.6
  simple_gradient_text: ^1.2.4
  google_maps_flutter: ^2.2.3
  html: ^0.15.0
  geocoder2: ^1.4.0
  url_launcher: ^6.3.1
  flutter_switch: ^0.3.2
  cached_network_image: ^3.2.3
  geolocator: ^12.0.0
  table_calendar: ^3.0.8
  jiffy: ^6.3.1
  permission_handler: ^10.4.3
  cloud_firestore: ^5.0.2
  image_picker: ^1.0.7
  crop: ^0.5.5
  carousel_slider: ^4.2.1
  youtube_player_flutter: ^8.1.2
  flutter_launcher_icons: ^0.13.1
  loading_indicator: ^3.1.0
  get: ^4.6.5
  smooth_page_indicator: ^1.0.1
  qr_code_scanner: ^1.0.1
#  flutter_vibrate: ^1.3.0
  #  flutter_hms_gms_availability: ^3.10.0
  sign_in_with_apple: ^6.1.1
  dart_extensions: ^2.2.0
  #  flutter_facebook_auth: ^5.0.8
  ags_authrest2: ^1.0.1
  dotted_line: ^3.2.2
  get_storage: ^2.1.1
  image_gallery_saver: ^2.0.3
  path_provider: ^2.0.15
  qr_flutter: ^4.0.0
  #  screenshot: ^1.3.0
  zoom_widget: ^2.0.1
  flutter_screenutil: ^5.9.3
  fluttertoast: ^8.0.8
  intl: ^0.18.0
#  mobile_scanner: ^5.1.1
#  mobile_scanner: ^5.2.3
  #  webview_flutter: ^4.8.0
  syncfusion_flutter_signaturepad: ^26.1.40
  flutter_cached_pdfview: ^0.4.2
  dotted_border: ^2.1.0
  local_auth: ^2.2.0
  screenshot: ^3.0.0
  package_info_plus: ^8.0.0
  version: ^3.0.2
  shorebird_code_push: ^1.1.3
  restart_app: ^1.2.1
  percent_indicator: ^4.2.3
  loading_animation_widget: ^1.2.1
  webview_flutter: 4.7.0
  flutter_inappwebview: ^5.4.3+7
  skeletonizer: ^1.4.1+1
  colorful_safe_area: ^1.0.0
  animated_rotation: ^2.0.0
  pretty_qr_code: ^3.3.0
  photo_view: ^0.15.0
  lottie: ^3.0.0
  pinput: ^5.0.1
  share_plus: ^10.1.4
  uni_links: ^0.5.1
  animated_text_kit: ^4.2.3
  widget_and_text_animator: ^1.1.5
  device_info_plus: ^11.3.0
  universal_html: ^2.2.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
dependency_overrides:
  intl: ^0.17.0
  webview_flutter_android: 3.16.1

#flutter_icons:
#  android: true
#  ios: true
#  remove_alpha_ios: true
#  image_path: "assets/icon/logo_app_songkan.png"
#  web:
#    generate: true
#    image_path: "assets/icon/logo_app_songkan.png"
#    background_color: "#hexcode"
#    theme_color: "#hexcode"

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 24

  web:
    generate: true
    image_path: "assets/icon/icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"

# run 'flutter pub run flutter_launcher_icons' when change Icon
# The following section is specific to Flutter packages.
flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/cert/
    - assets/icon/
    - assets/icon/sparePart/
    - assets/icon/sparePart/statusOrder/
    - assets/image/
    - assets/image/profile/
    - assets/image/home/
    - assets/image/MR/
    - assets/image/login/
    - assets/image/notification/
    - assets/image/service/
    - assets/image/service/insurance/
    - assets/image/service/appointment/
    - assets/image/service/home_service/
    - assets/image/service/pmg/
    - assets/image/car_detail/
    - assets/image/news/
    - assets/image/intro/
    - assets/image/minilike/
    - assets/image/drawer/
    - assets/image/drawer/contactus/
    - assets/image/psiIcon/
    - assets/animatedlogo/
    - assets/image/eSign/
    - assets/image/ticket/
    - assets/translate/
    - shorebird.yaml
    - assets/image/refCode/
    - assets/image/tutorial/
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  fonts:
    - family: Prompt
      fonts:
        - asset: assets/font/Prompt-Regular.ttf
    - family: Prompt-Medium
      fonts:
        - asset: assets/font/Prompt-Medium.ttf

  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700

