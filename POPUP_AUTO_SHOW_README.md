# การแก้ไขปัญหา Popup ไม่แสดงอัตโนมัติ

## ปัญหาเดิม
- Popup News ไม่แสดงทันทีที่เข้าแอปพลิเคชัน
- ต้องกดปุ่ม "เทส" ก่อน popup จึงจะแสดง
- ผู้ใช้ต้องมีการกระทำเพิ่มเติมเพื่อดู popup

## การแก้ไขที่ทำ

### 1. ปรับปรุง PopupNewsController
**ไฟล์:** `lib/controller/setting_controller/popup_news_controller.dart`

#### เพิ่มฟังก์ชัน `_addTestPopupData()`
```dart
Future<void> _addTestPopupData() async {
  // สร้างข้อมูล popup ทดสอบสำหรับ Debug Mode
  List<PopupNews> testPopups = [
    PopupNews(
      id: 9001,
      title: "ยินดีต้อนรับสู่แอปพลิเคชันใหม่!",
      content: "สวัสดีครับ! ขอบคุณที่เลือกใช้แอปพลิเคชันของเรา...",
      // ... พารามิเตอร์อื่นๆ
    ),
    // ... popup อื่นๆ
  ];
}
```

#### ปรับปรุงฟังก์ชัน `_initializePopupNews()`
- เพิ่มการเรียก `_addTestPopupData()` ในโหมด Debug
- ให้ popup แสดงทันทีที่มีข้อมูล

#### ปรับปรุงฟังก์ชัน `checkAndShowPopup()`
- แยกลอจิกสำหรับ Debug Mode และ Production Mode
- ในโหมด Debug: แสดง popup ทันทีโดยไม่เช็คว่าเคยแสดงแล้ว
- ในโหมด Production: ใช้ลอจิกเดิมที่เช็ค SharedPreferences

#### เพิ่มฟังก์ชันใหม่
- `showPopupAgain()`: สำหรับแสดง popup ใหม่ในโหมดทดสอบ
- `testPopupForUserGroup()`: เปลี่ยนกลุ่มผู้ใช้และแสดง popup

### 2. ปรับปรุงปุ่มทดสอบใน HomeMobile
**ไฟล์:** `lib/view/mobile/home_mobile.dart`

#### เปลี่ยนฟังก์ชันปุ่ม FloatingActionButton
```dart
// เดิม
PopupNewsTestService.addTestPopupNews();

// ใหม่
final PopupNewsController popupCtl = Get.find<PopupNewsController>();
popupCtl.showPopupAgain();
```

#### เปลี่ยน Icon ปุ่ม
- จาก `Icons.announcement` เป็น `Icons.refresh`
- เพื่อให้เข้าใจว่าเป็นการแสดง popup ใหม่อีกครั้ง

## ผลลัพธ์ที่ได้

### ในโหมด Debug (kDebugMode = true)
1. **แสดงอัตโนมัติ**: Popup จะแสดงทันทีที่เข้าแอป โดยไม่ต้องกดปุ่มใดๆ
2. **ข้อมูลทดสอบ**: มีข้อมูل popup ทดสอบ 2 รายการ
   - Popup ต้อนรับสำหรับผู้ใช้ทั่วไป
   - Popup ข้อมูลสำหรับ MR
3. **ไม่บันทึกสถานะ**: ไม่บันทึกว่า popup แสดงแล้ว เพื่อให้แสดงได้ทุกครั้ง
4. **ปุ่มรีเฟรช**: สามารถกดปุ่มเพื่อแสดง popup ใหม่ได้

### ในโหมด Production (kDebugMode = false)
1. **ทำงานเหมือนเดิม**: ใช้ลอจิกเดิมที่เช็ค SharedPreferences
2. **ไม่มีข้อมูลทดสอบ**: ใช้เฉพาะข้อมูลจาก API
3. **บันทึกสถานะ**: บันทึกว่า popup แสดงแล้วเพื่อไม่ให้แสดงซ้ำ

## การทดสอบ

### ขั้นตอนการทดสอบ
1. เปิดแอปในโหมด Debug
2. Popup ควรแสดงทันทีหลังจาก loading เสร็จ
3. ปิด popup
4. กดปุ่ม refresh (FloatingActionButton) เพื่อแสดง popup ใหม่
5. ตรวจสอบ console logs สำหรับข้อมูลการทำงาน

### Log ที่คาดว่าจะเห็น
```
=== MOCKUP USER DATA ===
userType: mr
mrCode: MR001
rankCurrent: platinum
========================

=== TEST POPUP DATA ADDED AUTOMATICALLY ===
Added 2 test popups automatically
Current user group: mr
===========================================

=== AUTO SHOWING POPUP (DEBUG MODE) ===
User group: mr
Popup title: ยินดีต้อนรับสู่แอปพลิเคชันใหม่!
Target groups: [general, mr, ma, ss]
======================================
```

## หมายเหตุสำคัญ

1. **โหมด Debug เท่านั้น**: การแสดง popup อัตโนมัติจะทำงานเฉพาะใน Debug Mode
2. **ไม่กระทบ Production**: การเปลี่ยนแปลงนี้ไม่กระทบการทำงานในโหมด Production
3. **ข้อมูลทดสอบ**: Popup ทดสอบจะแสดงให้กลุ่มผู้ใช้ทั้งหมด (general, mr, ma, ss)
4. **การนำไปใช้จริง**: หากต้องการให้แสดง popup อัตโนมัติในโหมด Production ให้ปรับค่า `kDebugMode` ใน `checkAndShowPopup()`

## ไฟล์ที่เกี่ยวข้อง
- `lib/controller/setting_controller/popup_news_controller.dart` (แก้ไขหลัก)
- `lib/view/mobile/home_mobile.dart` (ปรับปุ่มทดสอบ)
- `lib/component/popup_news_widget.dart` (ไม่เปลี่ยนแปลง)
- `lib/component/popup_news_test_service.dart` (ไม่เปลี่ยนแปลง)
