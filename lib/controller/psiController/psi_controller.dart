import 'package:dart_extensions/dart_extensions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/coupon_PSI.dart';

class PsiController extends GetxController {
  RxBool showTab = true.obs;
  RxList<Map<String, dynamic>> psiList = <Map<String, dynamic>>[].obs;
  CarOwnerModel carOwnerInfo = CarOwnerModel();
  List<String> keyList = [];
  RxInt index = 0.obs;
  RxString carRegSelect = "".obs;


  RxInt indexTicket = 0.obs;
  var isLoading = false.obs;
  // RxInt carSelectIndex = 0.obs;
  RxList carPic = [].obs;
  RxList carPicLoader = [].obs;

  PageController pageDetailController = PageController();
  PageController pageDetailController2 = PageController();
  PageController pageDetailController3 = PageController();
  PageController pageDetailController4 = PageController();

  var storage = GetStorage();

  // @override
  // void onInit() {
  //   // TODO: implement onInit
  //   getCarOwner();
  //   super.onInit();
  // }

  void changeShowTab (input) {
    showTab.value = input;
  }

  void changeIndexTicket(input){
    indexTicket.value = input;
  }

  getCarOwner() async {
    try {
      isLoading(true);
      print("getCarOwner");

      var usedPhone = await getLocalPhone();

      Map data = {
        //อย่าลืมเปลี่ยน
        "phone" : usedPhone,
        ///เบอร์ลูกค้า
        // "phone" : "0898098146",
      };
      print(data);
      final response = await AppApi.callAPIjwt("POST", AppUrl.getCarOwner, data);
      // final response = await AppApi.callAPIjwt("POST", "https://4a2a-125-27-69-21.ngrok-free.app/PMS/psi/get-car-owner", data);
      print(response);

      if(response['status'] == 200){
        carOwnerInfo = CarOwnerModel.fromJson(response["result"]);
        print("carOwnerInfo");
        // print(carOwnerInfo.carList!.length);
        // print(carOwnerInfo.carList![2].rustproof![0].rustDate);
        if(carOwnerInfo.carList!.length > 0){
          carPicLoader = List<bool>.filled(carOwnerInfo.carList!.length, true).obs;
          getCarImg();
        }
      }
      isLoading(false);
      update();
      // print(carOwnerInfo.carList![2].rustproof![0].rustDate);
    } catch (e) {
      print("Error getCarOwner");
      print(e);
      isLoading(false);
    }
  }

  getCouponPSI() async {
    try {
      psiList.clear();

      var usedPhone = await getLocalPhone();

      Map data = {
        "phone": usedPhone,
        // "phone": "0898098146",
      };

      final response = await AppApi.callAPIjwt("POST", AppUrl.getCouponPSI, data);

      print(response);

      // Check if the overall response indicates success
      if (response['status'] == 200) {
        // Ensure 'result' exists
        if (response['result'] != null) {
          response['result'].forEach((key, value) {
            print(key);
            // Ensure 'statusCode' exists within the value object
            print(value['statusCode']);
            if (value['statusCode'] == 200) {
              /// มี model แต่ไม่สามารถใช้เป็น model ได้ เนื่องจากตัวแปลต้องรับ variable type Json 3 level ทำให้ pointer คลาดเคลื่อน
              // DataCoupon dataCoupon = DataCoupon.fromJson(value['dataCoupon']);
              // DataCustomer dataCustomer = DataCustomer.fromJson(value['dataCoupon']);
              // Add to psiList
              psiList.add({
                'engine': key,
                'checkPSI': true,
                'dataCoupon': value['dataCoupon'],
                'dataCustomer': value['dataCustomer'],
              });
              print("inside add");
              print(value);
              print("inside add end");

            } else {
              psiList.add({
                'engine': key,
                'checkPSI': false,
                'dataCoupon': null,
                'dataCustomer': null,
              });
              print("null");
            }
          });
        } else {
          print("Response result is null");
        }
        // Print or use the psiList as needed
        print('Psi List: ${psiList}');
      } else {
        print("Response status is not 200: ${response['status']}");
      }

      update();
    } catch (e) {
      print("Exception occurred: $e");
    }
  }

  Future<String?> getLocalPhone() async {
    var usedPhone = storage.read("phone_profile");
    if (usedPhone != null && usedPhone != "") {
      usedPhone;
    } else {
      final profileCtl = await Get.find<ProfileController>();
      usedPhone = profileCtl.profile.value.mobile;
    }
    return usedPhone;
  }

  getCarImg() async {
    for(int i = 0; i < carOwnerInfo.carList!.length; i++){
      if(carOwnerInfo.carList![i].colorCode != "" && carOwnerInfo.carList![i].carModel != ""){
        try {
          // isLoading(true);

          Map data = {
            "colorCode": carOwnerInfo.carList![i].colorCode,
            "car_model": carOwnerInfo.carList![i].carModel
          };

          final response = await AppApi.callAPIjwt("POST", AppUrl.getCarImg, data);

          if(response['status'] == 200){
            carPic.add(response["result"]["image_url"]);
            carPicLoader[i] = false;

          }
        } catch (e) {
          print("Error getCarImg");
          print(e);
          carPic.add("");
          carPicLoader[i] = false;

        }
      }else{
        carPic.add("");
        carPicLoader[i] = false;
      }
    }
    update();
  }

  void scrollToPage(int pageIndex) {
    pageDetailController.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 600), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
    );
  }

  void scrollToPage2(int pageIndex) {
    pageDetailController2.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 700), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
    );
  }

  void scrollToPage3(int pageIndex) {
    pageDetailController3.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 800), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
    );
  }

  void scrollToPage4(int pageIndex) {
    pageDetailController4.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 900), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
    );
  }

  dateEngToThaiMiniPlus6M(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate.add(const Duration(days: 30 * 6)); // Add 5 months (approx. 150 days)

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else{
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }

  }

  dateEngToThaiMiniPlus1Y(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate.add(const Duration(days: 30 * 12));

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else{
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }

  }

  dateEngToThaiMini(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else{
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }

  }

}