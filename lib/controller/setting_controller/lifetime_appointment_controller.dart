import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/lifetime_appointment_model.dart';

class LifetimeAppointmentController extends GetxController {
  RxBool isLoading = true.obs;
  RxInt status = 0.obs;
  final pageController = PageController();
  final profileCtl = Get.put(ProfileController());

  RxString productText = "".obs;
  RxString productDate = "".obs;

  Timer? timer;

  RxInt carSelectIndex = 0.obs;
  PageController pageDetailController = PageController();
  PageController pageDetailController2 = PageController();
  PageController pageDetailController3 = PageController();
  PageController pageDetailController4 = PageController();

  LifetimeAppointment lifetimeAppointment = LifetimeAppointment();

  @override
  void onInit() async {
    // TODO: implement onInit
    await getLifetimeAppointment();

    update();
    super.onInit();
  }

  getLifetimeAppointment() async {
    try {
      Map data = {
        //อย่าลืมเปลี่ยน
        "phone": profileCtl.profile.value.mobile,
        // "phone" : "0614979362",
      };
      print(data);
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.lifetimeAppointment, data);
      status.value = response['status'];
      if (status.value == 200) {
        lifetimeAppointment = LifetimeAppointment.fromJson(response["result"]);
      }
      isLoading.value = false;
      update();
    } catch (e) {
      print(e);
    }
  }

  static String convertToThaiDate(String dateStr) {
    DateTime date = DateTime.parse(dateStr);
    String thaiFormattedDate = DateFormat('dd/MM/yyyy', 'th_TH').format(date);
    String thaiYear = (date.year + 543).toString();
    String formattedDate =
        thaiFormattedDate.replaceAll(RegExp(r'[0-9]{4}'), thaiYear);

    return formattedDate;
  }

  void scrollToPage(int pageIndex) {
    pageDetailController.animateToPage(pageIndex,
        duration: const Duration(
            milliseconds: 600), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
        );
  }

  void scrollToPage2(int pageIndex) {
    pageDetailController2.animateToPage(pageIndex,
        duration: const Duration(
            milliseconds: 700), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
        );
  }

  void scrollToPage3(int pageIndex) {
    pageDetailController3.animateToPage(pageIndex,
        duration: const Duration(
            milliseconds: 800), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
        );
  }

  void scrollToPage4(int pageIndex) {
    pageDetailController4.animateToPage(pageIndex,
        duration: const Duration(
            milliseconds: 900), // ความเร็วในการเลื่อน (มิลลิวินาที)
        curve: Curves.easeInOut // เอซที่ใช้ในการเลื่อน
        );
  }

  dateEngToThaiMiniPlus4M(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate
        .add(const Duration(days: 30 * 4)); // Add 5 months (approx. 150 days)

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }

  // dateEngToThaiMiniPlus6M(String date) {
  //   List<String> months_th_mini = [
  //     "",
  //     "ม.ค.",
  //     "ก.พ.",
  //     "มี.ค.",
  //     "เม.ย.",
  //     "พ.ค.",
  //     "มิ.ย.",
  //     "ก.ค.",
  //     "ส.ค.",
  //     "ก.ย.",
  //     "ต.ค.",
  //     "พ.ย.",
  //     "ธ.ค.",
  //   ];
  //
  //   DateTime parsedDate = DateTime.parse(date);
  //   DateTime currentDate = DateTime.now();
  //   parsedDate = parsedDate.add(const Duration(days: 30 * 6)); // Add 5 months (approx. 150 days)
  //
  //   int day = parsedDate.day;
  //   int month = parsedDate.month; // เพิ่ม 6 เดือน
  //   int year = parsedDate.year + 543;
  //
  //   if (month > 12) {
  //     month -= 12;
  //     year += 1;
  //   }
  //
  //   DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
  //   int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;
  //
  //   if (daysDifference > 0) {
  //     if (daysDifference <= 30) {
  //       return "อีก $daysDifference วัน";
  //     } else{
  //       return "$day ${months_th_mini[month]} $year";
  //     }
  //   } else if (daysDifference == 0) {
  //     return "วันนี้";
  //   } else {
  //     return "$day ${months_th_mini[month]} $year";
  //   }
  //
  // }

  dateEngToThaiMiniPlus6M(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);

    // เพิ่ม 6 เดือน
    int newMonth = parsedDate.month + 6;
    int newYear = parsedDate.year;
    if (newMonth > 12) {
      newMonth -= 12;
      newYear += 1;
    }

    int newDay = parsedDate.day;
    int lastDayOfNewMonth = DateTime(newYear, newMonth + 1, 0).day;

    // ถ้าวันที่เกินจำนวนวันของเดือนเป้าหมาย → ใช้วันสุดท้ายของเดือนแทน
    if (newDay > lastDayOfNewMonth) {
      newDay = lastDayOfNewMonth;
    }

    DateTime finalDate = DateTime(newYear, newMonth, newDay);

    int day = finalDate.day;
    int month = finalDate.month;
    int year = finalDate.year + 543;

    return "$day ${months_th_mini[month]} $year";
  }

  dateEngToThaiMiniPlus1Y(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();
    parsedDate = parsedDate.add(const Duration(days: 30 * 12));

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }

  dateEngToThaiMini(String date) {
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    DateTime parsedDate = DateTime.parse(date);
    DateTime currentDate = DateTime.now();

    int day = parsedDate.day;
    int month = parsedDate.month; // เพิ่ม 6 เดือน
    int year = parsedDate.year + 543;

    if (month > 12) {
      month -= 12;
      year += 1;
    }

    DateTime parsedDatePlus6M = DateTime(parsedDate.year, month, day);
    int daysDifference = currentDate.difference(parsedDatePlus6M).inDays;

    if (daysDifference > 0) {
      if (daysDifference <= 30) {
        return "อีก $daysDifference วัน";
      } else {
        return "$day ${months_th_mini[month]} $year";
      }
    } else if (daysDifference == 0) {
      return "วันนี้";
    } else {
      return "$day ${months_th_mini[month]} $year";
    }
  }

  calculateNearestDate(String? milesStr, String? rustproofStr,
      String? insuranceStr, String? pmgStr) {
    DateTime currentDate = DateTime.now();
    DateTime? milesDate;
    DateTime? rustproofDate;
    DateTime? insuranceDate;
    DateTime? pmgDate;
    List<String> months_th_mini = [
      "",
      "ม.ค.",
      "ก.พ.",
      "มี.ค.",
      "เม.ย.",
      "พ.ค.",
      "มิ.ย.",
      "ก.ค.",
      "ส.ค.",
      "ก.ย.",
      "ต.ค.",
      "พ.ย.",
      "ธ.ค.",
    ];

    if (milesStr != null && milesStr.isNotEmpty) {
      DateTime milesDateParse = DateTime.parse(milesStr);
      milesDate = milesDateParse.add(const Duration(days: 30 * 6));
    }

    if (rustproofStr != null && rustproofStr.isNotEmpty) {
      DateTime rustproofDateParse = DateTime.parse(rustproofStr);
      rustproofDate = rustproofDateParse.add(const Duration(days: 30 * 12));
    }

    if (insuranceStr != null && insuranceStr.isNotEmpty) {
      DateTime insuranceDateParse = DateTime.parse(insuranceStr);
      insuranceDate = insuranceDateParse;
    }

    if (pmgStr != null && pmgStr.isNotEmpty) {
      DateTime pmgDateParse = DateTime.parse(pmgStr);
      pmgDate = pmgDateParse.add(const Duration(days: 30 * 4));
    }

    List<DateTime> dates = [];
    if (milesDate != null) {
      dates.add(milesDate);
    }

    if (rustproofDate != null) {
      dates.add(rustproofDate);
    }

    if (insuranceDate != null) {
      dates.add(insuranceDate);
    }

    if (pmgDate != null) {
      dates.add(pmgDate);
    }

    if (dates.isNotEmpty) {
      DateTime nearestDate = dates.reduce((a, b) =>
          (a.difference(currentDate).abs() < b.difference(currentDate).abs())
              ? a
              : b);
      int daysDifference = nearestDate.difference(currentDate).inDays;

      int day = nearestDate.day;
      int month = nearestDate.month;
      int year = nearestDate.year + 543;

      String formattedDate;
      if (daysDifference <= 30 && daysDifference >= 0) {
        formattedDate = '$daysDifference วัน';
      } else {
        int thaiYear = nearestDate.year + 543;
        String shortThaiYear =
            thaiYear.toString().substring(2); // Get last 2 digits
        formattedDate = "$day ${months_th_mini[month]} $shortThaiYear";
        // formattedDate = DateFormat('dd/MM/yy').format(nearestDate);
        // int thaiYear = nearestDate.year + 543;
        // formattedDate = formattedDate.replaceRange(6, 8, year.toString().substring(2));
      }

      return formattedDate;
    } else {
      print("ไม่มีวันที่ที่คำนวณได้");
      return "";
    }
  }

  calculateNearestProduct(String? milesStr, String? rustproofStr,
      String? insuranceStr, String? pmgStr) {
    DateTime currentDate = DateTime.now();
    DateTime? milesDate;
    DateTime? rustproofDate;
    DateTime? insuranceDate;
    DateTime? pmgDate;

    if (milesStr != null && milesStr.isNotEmpty) {
      DateTime milesDateParse = DateTime.parse(milesStr);
      milesDate = milesDateParse
          .add(const Duration(days: 30 * 6)); // Add 5 months (approx. 150 days)
    }

    if (rustproofStr != null && rustproofStr.isNotEmpty) {
      DateTime rustproofDateParse = DateTime.parse(rustproofStr);
      rustproofDate = rustproofDateParse.add(
          const Duration(days: 30 * 12)); // Add 11 months (approx. 330 days)
    }

    if (insuranceStr != null && insuranceStr.isNotEmpty) {
      DateTime insuranceDateParse = DateTime.parse(insuranceStr);
      insuranceDate = insuranceDateParse;
    }

    if (pmgStr != null && pmgStr.isNotEmpty) {
      DateTime pmgDateParse = DateTime.parse(pmgStr);
      pmgDate = pmgDateParse.add(
          const Duration(days: 30 * 4)); // Add 11 months (approx. 330 days)
    }

    List<DateTime> dates = [];
    List<String> descriptions = [];

    if (milesDate != null) {
      dates.add(milesDate);
      descriptions.add("ครบกำหนดเช็กระยะ");
    }

    if (rustproofDate != null) {
      dates.add(rustproofDate);
      descriptions.add("ครบกำหนดพ่นกันสนิม");
    }

    if (insuranceDate != null) {
      dates.add(insuranceDate);
      descriptions.add("ครบกำหนดต่อทะเบียน/ประกัน");
    }

    if (pmgDate != null) {
      dates.add(pmgDate);
      descriptions.add("ครบกำหนดเคลือบแก้ว");
    }

    if (dates.isNotEmpty) {
      DateTime nearestDate = dates.reduce((a, b) =>
          (a.difference(currentDate).abs() < b.difference(currentDate).abs())
              ? a
              : b);

      int indexNearestDate = dates.indexOf(nearestDate);
      String nearestDescription = descriptions[indexNearestDate];

      productText.value = nearestDescription;
      return productText.value;
    } else {
      print("ไม่มีวันที่ที่คำนวณได้");
      return ""; // หรือค่าที่คุณต้องการให้ในกรณีที่ไม่มีวันที่คำนวณได้
    }
  }
}
