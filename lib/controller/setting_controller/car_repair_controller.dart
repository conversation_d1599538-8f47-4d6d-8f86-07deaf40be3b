import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';

import '../../component/firestore.dart';
import '../../component/alert.dart';
import '../../component/api.dart';
import '../../component/url.dart';
import '../../model/car_repair_status.dart';
import 'profile_controller.dart';

class CarRepairController extends GetxController {
  RxBool isLoading = true.obs;
  RxInt status = 0.obs;
  var profilePhone = "";
  var storage = GetStorage();

  RxInt currentIndex = 0.obs;

  RxBool isShow = false.obs;

  final profileCtl = Get.put(ProfileController());

  Rx<ResCarRepairStatus> carRepairStatus = ResCarRepairStatus().obs;
  RxString carRepairStatusStr = "".obs;
  RxDouble widthProcess = 0.1.obs;
  RxString percent = "0%".obs;

  RxMap payment = {}.obs;

  RxString datePayment = "".obs;
  RxString timePayment = "".obs;
  RxString timeToPay = "".obs;

  @override
  void onInit() async {
    super.onInit();
    await getLocalPhone();
    getCarRepairStatus();
  }

  saveImage(context, Uint8List image) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = File('${directory.path}/screenshot.png');
      await imagePath.writeAsBytes(image);

      final result = await ImageGallerySaver.saveImage(image);
      if (result['isSuccess']) {
        AppAlert.showNewAccept(context, "บันทึกรูปภาพสำเร็จ",
            "กรุณาเปิดแอปธนาคาร\nเพื่อทำการจ่ายเงิน", "ตกลง");
      } else {
        AppAlert.showNewAccept(context, "บันทึกรูปภาพไม่สำเร็จ",
            "กรุณาแคปหน้าจอและ\nเปิดแอปธนาคาร\nเพื่อทำการจ่ายเงิน", "ตกลง");
      }
    } catch (e) {
      print(e);
      AppAlert.showNewAccept(context, "บันทึกรูปภาพไม่สำเร็จ",
          "กรุณาแคปหน้าจอและ\nเปิดแอปธนาคาร\nเพื่อทำการจ่ายเงิน", "ตกลง");
    }
  }

  Future<void> getCarRepairStatus() async {
    try {
      Map data = {
        "phone": profilePhone,
      };
      final response =
          await AppApi.callAPIjwt("POST", AppUrl.getCarRepairStatus, data);

      print("getCarRepairStatus ==> ${response}");
      if (response["status"] == 200) {
        carRepairStatus =
            ResCarRepairStatus.fromJson(response["result"][0]).obs;
        String timeString = carRepairStatus.value.timeRepair == ""
            ? "00:00:00"
            : carRepairStatus.value.timeRepair!;
        DateTime? dateTime = DateFormat("HH:mm:ss").parse(timeString);
        String formattedTime = DateFormat("hh:mm a").format(dateTime);
        var arr = formattedTime.split(":");
        var time = "${arr[0]} : ${arr[1]}";
        carRepairStatus.value.timeRepair = time;
        status.value = response["status"];
        await changeStatus();
        if (response["result"][0]["type_pay"] == "QR") {
          await getPayment();
        }
        update();
      } else {
        status.value = 0;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  changeStatus() async {
    try {
      if (carRepairStatus.value.status == "pending") {
        carRepairStatusStr.value = "รอชำระเงิน";
        Future.delayed(const Duration(seconds: 2), () async {
          widthProcess.value = 0.8;
          percent.value = "80%";
        });
      } else if (carRepairStatus.value.status == "PAID") {
        status.value = 0;
      } else if (carRepairStatus.value.status == "ซ่อมเสร็จแล้ว") {
        carRepairStatusStr.value = "ดำเนินการซ่อมเสร็จเรียบร้อย";
        Future.delayed(const Duration(seconds: 2), () async {
          widthProcess.value = 0.6;
          percent.value = "60%";
        });
      } else if (carRepairStatus.value.status == "อยู่ระหว่างซ่อม") {
        carRepairStatusStr.value = "อยู่ในระหว่างการซ่อม";
        Future.delayed(const Duration(seconds: 2), () async {
          widthProcess.value = 0.4;
          percent.value = "40%";
        });
      } else if (carRepairStatus.value.status == "รอเข้าซ่อม") {
        carRepairStatusStr.value = "รับแจ้งซ่อมเรียบร้อย กำลังรอเข้าซ่อม";
        Future.delayed(const Duration(seconds: 2), () async {
          widthProcess.value = 0.2;
          percent.value = "20%";
        });
      } else {
        carRepairStatusStr.value = "";
        widthProcess.value = 0.1;
        percent.value = "0%";
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  getPayment() async {
    Map data = await FireStore.getNotificationPayment(
        profileCtl.profile.value.mobile.toString(),
        carRepairStatus.value.partnerTxnUid!);
    payment = RxMap(data["data"][0]);
    final dateTime = payment["create"].toDate();
    final dateFormat = DateFormat('dd/MM/yy HH:mm');

    final tenMinutesLater = dateTime.add(const Duration(minutes: 10));

    final formattedDate = dateFormat.format(tenMinutesLater);
    datePayment.value = formattedDate.split(" ")[0];
    timePayment.value = formattedDate.split(" ")[1];

    DateTime now = DateTime.now();
    timeToPay.value = tenMinutesLater.difference(now).toString().split(".")[0];
    hours.value = int.parse(timeToPay.value.split(":")[0]);
    minutes.value = int.parse(timeToPay.value.split(":")[1]);
    seconds.value = int.parse(timeToPay.value.split(":")[2]);
  }

  countDownTimeToPay() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timeToPay.value.startsWith("-")) {
        timer.cancel();
      } else {
        countDown();
      }
    });
  }

  RxInt hours = 0.obs;
  RxInt minutes = 0.obs;
  RxInt seconds = 0.obs;

  void countDown() {
    if (hours.value > 0 || minutes.value > 0 || seconds.value > 0) {
      seconds.value--;

      if (seconds.value < 0) {
        seconds.value = 59;
        minutes.value--;
      }

      if (minutes.value < 0) {
        minutes.value = 59;
        hours.value--;
      }

      update();
    }
  }

  Future<void> getLocalPhone() async {
    var usedPhone = storage.read("phone_profile");
    if (usedPhone != null && usedPhone != "") {
      usedPhone;
    } else {
      final profileCtl = await Get.find<ProfileController>();
      usedPhone = profileCtl.profile.value.mobile;
    }
    profilePhone = usedPhone;
    update();
  }

  // formatTime() {
  //   String timeString = carRepairStatus.value.timeRepair == "" ? "00:00:00" : carRepairStatus.value.timeRepair!;
  //   DateTime? dateTime = DateFormat("HH:mm:ss").parse(timeString);
  //   String formattedTime = DateFormat("hh:mm a").format(dateTime);
  //   var arr = formattedTime.split(":");
  //   var time = "${arr[0]} : ${arr[1]}";
  //   carRepairStatus.value.timeRepair = time;
  // }
}
