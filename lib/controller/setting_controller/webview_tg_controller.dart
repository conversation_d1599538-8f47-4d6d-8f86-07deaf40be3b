import 'dart:math';

import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/model/member_likepoint_model.dart';

import '../../component/firestore.dart';

class WebViewLikePointController extends GetxController {
  RxString phoneEncode = "".obs;
  RxString merchantID = "".obs;
  RxString urlLikePointAPI = "".obs;
  RxString urlMiniLikePoint = "".obs;
  RxString namePoint = "".obs;
  RxBool isOpen = false.obs;
  RxString firstName = "".obs;
  RxString lastName = "".obs;
  RxString balanceLikePoint = "0".obs;
  RxInt balanceLikePointAvailable = 0.obs;
  RxString balanceLikePointTHB = "0".obs;
  RxBool statusMigrate = false.obs;
  RxBool isChecked = false.obs;
  RxBool isUpgrading = false.obs;
  RxBool isSuccess = false.obs;
  RxBool useOnlyLikePoint = false.obs;
  RxBool readyForUse = false.obs;
  RxString pocketID = "".obs;
  RxString exchangeRate = "0".obs;

  // FOR MIGRATE
  RxString amount = "0".obs;
  RxString address = "".obs;
  RxString displayName = "".obs;
  RxString likewalletUID = "".obs;
  RxString memberID = "".obs;

  // FOR MIGRATE
  final SecureStorage secureStorage = SecureStorage();
  ProfileController profileCtl = Get.find<ProfileController>();
  RxMap notiPoint = {
    "title": "",
    "dec": "",
  }.obs;

  @override
  onInit() async {
    super.onInit();
    await getPocketBalance();
    await checkMigrate();
    getRateExchange();
  }

  // getMerchantID() async {
  //   var res = await FireStore.getMerchantID();
  //   merchantID.value = res[1]['merchantID'];
  //   isOpen.value = res[1]['status'];
  // }

  getRateExchange() async {
    var res = await FireStore.getMerchantID();
    exchangeRate.value = res[1]['valuePoint'];
  }

  Future<dynamic> checkMigrate() async {
    try {
      if (profileCtl.profile.value.phoneFirebase == null) {
        return;
      }else {
        String phoneNumber = profileCtl.profile.value.mobile.toString();
        String phoneWithCountryCode = addCountryCode(phoneNumber);
        profileCtl.profile.value.phoneFirebase = phoneWithCountryCode;
        update();
      }

      Map requestBody = {
        'phone': profileCtl.profile.value.phoneFirebase,
      };
      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/member/info-likewallet', requestBody);

      if (response['data'].length > 0) {
        var migratable = response['data']['migratable'];
        var detail = response['data']['detail'];

        if (migratable == true && detail == 'พบข้อมูลผู้ใช้งาน') {
          Map requestCheckMigrate = {'uid': response['data']['data']['uid']};

          final responseMigrate = await AppApi.postLikePoint(
              '${urlLikePointAPI.value}/member/check-migrate',
              requestCheckMigrate);
          var migrateStatus = responseMigrate['data']['status'];

          if (migrateStatus == "already migrated") {
            useOnlyLikePoint.value = true;
          } else {
            statusMigrate.value = true;
            address.value = response['data']['data']['address'];
            displayName.value = response['data']['data']['displayName'];
            likewalletUID.value = response['data']['data']['uid'];
          }
          readyForUse.value = true;
          update();
        } else {
          useOnlyLikePoint.value = true;
          readyForUse.value = true;
          update();
        }
      }
    } catch (e) {
      print('error Get Phone Encode : $e');
    }
  }

  Future<bool> upgradeLikePoint() async {
    try {
      print("upgradeLikePoint");
      isUpgrading.value = true;
      update();
      Map requestBodyLikeWallet = {
        'phone': profileCtl.profile.value.phoneFirebase,
      };

      final responseBalanceLikeWallet = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/member/balance-likewallet',
          requestBodyLikeWallet);
      amount.value =
          responseBalanceLikeWallet['data']['totalBalance'].toString();
      Map requestBody = {
        'phone': profileCtl.profile.value.phoneFirebase,
        'merchantID': merchantID.value,
        'amount': amount.value,
        'address': address.value,
        'displayName': displayName.value,
        'likewalletUID': likewalletUID.value,
        'memberID': memberID.value,
      };

      final response = await AppApi.postLikePoint('${urlLikePointAPI.value}/mint-point/mint-point-member-migrate',requestBody);
      if(response['data']['status'] == "Success") {
        // print(response);
        isSuccess.value = true;
        useOnlyLikePoint.value = true;
        await getPocketBalance();
        update();
        return true;
      }else {
        return false;
      }
    } catch (e) {
      print('error Migrate Like Point : $e');
      return false;
    }
  }

  String addCountryCode(String phoneNumber) {
    if (phoneNumber.startsWith('0')) {
      // Remove the leading zero
      phoneNumber = phoneNumber.substring(1);
    }
    // Add the +66 country code
    return '+66' + phoneNumber;
  }

  Future<dynamic> getPocketBalance() async {
    try {

      // print('profileCtl.profile.value.phoneFirebase ${profileCtl.profile.value.phoneFirebase}');
      if (profileCtl.profile.value.phoneFirebase == null) {
        return;
      }else {
        String phoneNumber = profileCtl.profile.value.mobile.toString();
        String phoneWithCountryCode = addCountryCode(phoneNumber);
        profileCtl.profile.value.phoneFirebase = phoneWithCountryCode;
        update();
      }
      var infoLikePoint = await FireStore.getInfoLikePoint();
      merchantID.value = infoLikePoint['merchantID'];
      urlLikePointAPI.value = infoLikePoint['urlAPI'];
      urlMiniLikePoint.value = infoLikePoint['url'];
      namePoint.value = infoLikePoint['namePoint'];
      isOpen.value = infoLikePoint['status'];
      update();

      Map requestBody = {
        'phone': profileCtl.profile.value.phoneFirebase,
        'merchantID': merchantID.value,
      };
      // print("requestBody: $requestBody");

      final responseWallet = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/wallet/findWalletByPhone', requestBody);

      // print("responseWallet");
      // print(responseWallet);

      if (responseWallet['data'][0]['memberInfo'] == null) {
        Map<String, dynamic> requestBody = {
          'phone': profileCtl.profile.value.phoneFirebase,
          'firstName': (profileCtl.profile.value.firstname?.toString()?.trim()?.isEmpty ?? true) ? "-" : profileCtl.profile.value.firstname.toString().trim(),
          'lastName': (profileCtl.profile.value.lastname?.isEmpty ?? true) ? "-" : profileCtl.profile.value.lastname,
          'merchantID': merchantID.value,
        };

        final response = await AppApi.postLikePoint(
            '${urlLikePointAPI.value}/member/regis-partners', requestBody);

        if (response['data'].length > 0) {
          var memberInfo =
          ResponseMemberLikePoint.fromJson(response['data'][0]);
          memberID.value = memberInfo.id!;
          Map dataCreatePocket = {
            "memberID": memberID.value,
            "merchantID": merchantID.value,
          };

          await AppApi.postLikePoint(
            "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
            dataCreatePocket,
          );
          update();
        } else {
          if (responseWallet['data'][0]['walletInfo'] == null) {
            Map dataCreatePocket = {
              "memberID": responseWallet['data'][0]['memberInfo']['id'],
              "merchantID": merchantID.value,
            };

            await AppApi.postLikePoint(
              "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
              dataCreatePocket,
            );

            balanceLikePoint.value = AppService.numberFormatNon0(0);
            balanceLikePointAvailable.value = int.parse(infoLikePoint['valuePoint']);
            balanceLikePointTHB.value = AppService.numberFormat((0) *double.parse(infoLikePoint['valuePoint'].toString()));
            memberID.value = responseWallet['data'][0]['memberInfo']['id'];
            pocketID.value = responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

          }else {
            final pocketBalance = responseWallet['data'][0]['walletInfo']['pocket'][0]['pocketBalance'];
            balanceLikePoint.value = AppService.numberFormatNon0(pocketBalance);
            balanceLikePointAvailable.value = int.parse(infoLikePoint['valuePoint']);
            balanceLikePointTHB.value = AppService.numberFormat(
                int.parse(pocketBalance.toString()) *
                    double.parse(infoLikePoint['valuePoint'].toString()));
            memberID.value = responseWallet['data'][0]['memberInfo']['id'];
            pocketID.value = responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

            // print(pocketBalance);
            update();
          }
        }
      } else {
        if (responseWallet['data'][0]['walletInfo'] == null) {
          Map dataCreatePocket = {
            "memberID": responseWallet['data'][0]['memberInfo']['id'],
            "merchantID": merchantID.value,
          };

          await AppApi.postLikePoint(
            "${urlLikePointAPI.value}/pocket/create-pocket-merchant",
            dataCreatePocket,
          );

          balanceLikePoint.value = AppService.numberFormatNon0(0);
          balanceLikePointTHB.value = AppService.numberFormat((0) *double.parse(infoLikePoint['valuePoint'].toString()));
          balanceLikePointAvailable.value = int.parse(infoLikePoint['valuePoint']);
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          pocketID.value = responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

        }else {
          final pocketBalance = responseWallet['data'][0]['walletInfo']['pocket'][0]['pocketBalance'];
          balanceLikePoint.value = AppService.numberFormatNon0(pocketBalance);
          balanceLikePointAvailable.value = int.parse(pocketBalance.toString());
          balanceLikePointTHB.value = AppService.numberFormat(
              int.parse(pocketBalance.toString()) *
                  double.parse(infoLikePoint['valuePoint'].toString()));
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          pocketID.value = responseWallet['data'][0]['walletInfo']['pocket'][0]['id'];

          update();
        }

      }
    } catch (e) {
      print('error Get Pocket Balance : $e');
    }
  }

  Future<dynamic> payPOI(String activityID) async {
    try {
      Map requestBody = {
        'activityID': activityID,
        'merchantID': merchantID.value,
        "phone": profileCtl.profile.value.phoneFirebase,
        "firstName": profileCtl.profile.value.firstname,
        "lastName": profileCtl.profile.value.lastname,
      };

      final response = await AppApi.postLikePoint(
          '${urlLikePointAPI.value}/transactions-activity/pay-poi-in-app',
          requestBody);
      // print(response);

      if (response["status"] == 500) {
        return false;
      } else if (response['data'].length > 0) {
        await getPocketBalance();

        notiPoint.value['title'] =
        'คุณได้รับ $namePoint จากกิจกรรม ${response['data'][0]['activityName']}';
        notiPoint.value['dec'] =
        'จำนวน ${response['data'][0]['activityAmount']} $namePoint';
        update();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('error Pay POI : $e');
      return false;
    }
  }

  Future checkAccept(value) async {
    try {
      isChecked.value = value;
      update();
    } catch (e) {
      print('error Pay POI : $e');
    }
  }
}