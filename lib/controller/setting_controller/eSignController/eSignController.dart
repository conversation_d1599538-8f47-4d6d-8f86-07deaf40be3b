import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:image/image.dart' as img;
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';

import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification_controller.dart';

class ESignatureController extends GetxController {
  final box = GetStorage();
  // https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/template_up/1716198616.pdf
  // https://linebotkeep-file.s3.ap-southeast-1.amazonaws.com/template_up/1717746536.pdf
  var usePDF = "";
  var showPDF = "";
  var searchWord = "";
  // var bookNum = "";
  // var seriesCar = "";
  // var colorCar = "";
  // var finance = "";
  // var money = "";
  // var saleCompany = "";
  // var bookDate = "";
  var typeDoc = "";
  bool checkStatuSign = false;

  int showIndexNow = 0;

  // ESignModelList? esignModel;

  bool checkPutSign = false;
  var alreadySign;

  bool showPopUp = false;

  bool usedToSign = false;

  var detailText = [];
  var margeArrayShow = [];

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    switchCheckSignature();
  }

  Future<void> margeArrayFunction() async {

    final notificationCtl = Get.find<NotificationController>();

    List<Map<String, dynamic>> array1 = showDetailEsign.cast<Map<String, dynamic>>().toList();

    List<Map<String, dynamic>> array2 = notificationCtl.notificationItems.cast<Map<String, dynamic>>().toList();

    List<Map<String, dynamic>> filteredArray2 =
    array2.where((item) => item["type"] == "workjob").toList();

    List<Map<String, dynamic>> array3 =
    array2.where((item) => item["type"] != "workjob").toList();

    List<Map<String, dynamic>> mergedArray = [...array1, ...filteredArray2];

    mergedArray.sort((a, b) {
      String dateAStr = a.containsKey("create_time")
          ? (a["create_time"] is Timestamp
          ? (a["create_time"] as Timestamp).toDate().toIso8601String()
          : a["create_time"])
          : (a["create"] is Timestamp
          ? (a["create"] as Timestamp).toDate().toIso8601String()
          : a["create"]);

      String dateBStr = b.containsKey("create_time")
          ? (b["create_time"] is Timestamp
          ? (b["create_time"] as Timestamp).toDate().toIso8601String()
          : b["create_time"])
          : (b["create"] is Timestamp
          ? (b["create"] as Timestamp).toDate().toIso8601String()
          : b["create"]);

      DateTime dateA = DateTime.parse(dateAStr);
      DateTime dateB = DateTime.parse(dateBStr);

      return dateA.compareTo(dateB);
    });

    margeArrayShow = mergedArray;
    update();
  }

  Future<void> changeUsePDF(input) async {
    usePDF = input;
    update();
  }

  Future<void> changeShowPDF(input) async {
    print("input => ${input}");
    if(input != null && input != "" && input != "null"){
      // print("do");
      showPDF = input;
    }
    // print("showPDF => ${showPDF}");
    update();
  }

  Future<void> showPopUpFunc() async{
    showPopUp = true;
    update();
  }

  Future<void> notShowPopUpFunc() async{
    showPopUp = false;
    update();
  }

  Future<void> deleteAlreadySign() async {
    // box.remove('signature');
    alreadySign = "";
    update();
  }

  Future<void> changeIndexNow(index) async{
    showIndexNow = index;
    update();
  }

  Future<void> resetPutSign() async{
    checkPutSign = false;
    if(showDetailEsign[showIndexNow]["status_sign"] != null) {
      checkStatuSign = true;
    }
    update();
  }

  Future<void> getCurrentNotiByRunning(phone) async {
    await getCurrentNoti(phone);

    await setVariableForshow(showDetailEsign[0], showDetailEsign[0]["url"], false);

    update();
  }

  var showDetailEsign;
  Future<void> getCurrentNoti(phone) async {
    try{
      var data = {
        "phone": phone
      };

      // print(data);

      var response = await AppApi.callAPIjwt("POST",AppUrl.getEsignNoti, data);
      if(response["status"] == 200){

        showDetailEsign = response["result"];
        print(showDetailEsign);
        margeArrayFunction();
      }else{
        showDetailEsign = [];
      }
      update();
    }catch(e){
      print(e);
    }
  }

  Future<void> setVariableForshow(inputJson, url, checkStatus) async{

    Map<String, dynamic> decodedJson = {};
    if(inputJson["body"] is String){
      String properJsonString = inputJson["body"].replaceAll("'", '"');

      decodedJson = jsonDecode(properJsonString);
      // print(decodedJson);
    }

    // print(decodedJson["detailText"]);

    // var splitRecieve = decodedJson["detailText"].split('-n');
    // print(splitRecieve);
    // bookNum = decodedJson["bookNum"];
    // bookDate = decodedJson["bookDate"];
    // seriesCar = decodedJson["seriesCar"];
    // colorCar = decodedJson["colorCar"];
    // finance = decodedJson["finance"];
    // money = decodedJson["money"];
    // saleCompany = decodedJson["saleCompany"];
    detailText = decodedJson["detailText"].split('-n');
    // print(detailText);
    searchWord = decodedJson["search_word"];
    typeDoc = decodedJson["typeDoc"];

    checkStatuSign = checkStatus;

    changeUsePDF(url);

    changeShowPDF(url);
    update();

  }

  void switchCheckSignature(){
    if(box.read('signature') == null){
      alreadySign = "";

    }else{
      alreadySign = box.read('signature');
      usedToSign = true;
    }

    update();
  }

  void changeAlreadySign(input){
    alreadySign = input;
    update();
  }

  Future<String> saveSignature(ui.Image image) async {
    // Convert the ui.Image to ByteData
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    // Convert ByteData to Uint8List
    Uint8List imageBytes = byteData!.buffer.asUint8List();

    // Decode the image to an image package's Image object
    img.Image originalImage = img.decodeImage(imageBytes)!;

    // Resize the image
    img.Image resizedImage = img.copyResize(originalImage, width: 325, height: 270);

    // Encode the resized image to Uint8List
    Uint8List resizedImageBytes = Uint8List.fromList(img.encodePng(resizedImage));

    // Encode the Uint8List to a base64 string
    String base64String = base64Encode(resizedImageBytes);

    var imgUrl = await AppService.uploadPhotoToS3(base64String, "MappPMS/ESignImages");

    // print(imgUrl);
    box.write('signature', imgUrl);
    switchCheckSignature();
    return imgUrl;
  }

  changeLinkPDF(link) {
    usePDF = link;
    update();
  }

  Future<void> putESignPDF(sign) async {

    var data = {
      "link": usePDF,
      "signLink": sign,
      "searchWord": searchWord,
    };
    var response;
    // if(typeDoc == "ใบสรุปยอด"){
      response = await AppApi.postNormal(AppUrl.eSignOnDockerMidAndEndPage, data);
    // } else {
    //   response = await AppApi.postNormal(AppUrl.eSignOnDocker, data);
    // }

    checkPutSign = true;
    checkStatuSign = true;
    update();

    changeAlreadySign(sign);
    saveSignToDB(response["link"]);
    update();
  }

  Future<void> saveSignToDB(signed_url) async {
    try{
      var data = {
        "running": showDetailEsign[showIndexNow]["running"],
        "title": showDetailEsign[showIndexNow]["title"],
        "detail": showDetailEsign[showIndexNow]["detail"],
        "url": showDetailEsign[showIndexNow]["link_url"],
        "body": showDetailEsign[showIndexNow]["body"],
        "status_sign": signed_url,
        "phone": showDetailEsign[showIndexNow]["phone"],
      };

      // print(data);
      var response = await AppApi.callAPIjwt("POST", AppUrl.updateNotiEsign, data);
      // print(response);

      changeShowPDF(signed_url);
      // print("success");

      showDetailEsign[showIndexNow]["status_sign"] = signed_url;
      update();
    }catch(e){
      print(e);
    }
  }

  deleteNotiEsign() async {
    try{
      var data = {
        "running": showDetailEsign[showIndexNow]["running"],
        "title": showDetailEsign[showIndexNow]["title"],
        "detail": showDetailEsign[showIndexNow]["detail"],
        "url": showDetailEsign[showIndexNow]["link_url"],
        "body": showDetailEsign[showIndexNow]["body"],
        "status_sign": showDetailEsign[showIndexNow]["status_sign"],
        "phone": showDetailEsign[showIndexNow]["phone"],
        "status_show": "N"
      };

      print(data);
      var response = await AppApi.callAPIjwt("POST",AppUrl.deleteNotiEsign, data);
      print(response);

      showDetailEsign.removeAt(showIndexNow);
      update();
    }catch(e){
      print(e);
    }
  }

  deleteAllNotiEsign() async {
    try{
      var data = {
        "phone": showDetailEsign[showIndexNow]["phone"],
      };

      print(data);
      var response = await AppApi.callAPIjwt("POST",AppUrl.deleteAllNotiEsign, data);
      print(response);

      showDetailEsign = [];
      update();
    }catch(e){
      print(e);
    }
  }
}

/// below is the model
// class ESignModel {
//   ESignModel({
//     bookNum,
//     seriesCar,
//     colorCar,
//     finance,
//     money,
//     saleCompany,
//   });
//
//   String? bookNum;
//   String? seriesCar;
//   String? colorCar;
//   String? finance;
//   String? money;
//   String? saleCompany;
//
//   factory ESignModel.fromJson(Map<String, dynamic> json) => ESignModel(
//       bookNum: json["bookNum"],
//       seriesCar: json["seriesCar"],
//       colorCar: json["colorCar"],
//       finance: json["finance"],
//       money: json["money"],
//       saleCompany: json["saleCompany"]
//   );
//
//   Map<String, dynamic> toJson() => {
//     "bookNum": bookNum,
//     "seriesCar": seriesCar,
//     "colorCar": colorCar,
//     "finance": finance,
//     "money": money,
//     "saleCompany": saleCompany
//   };
// }
//
// class ESignModelList {
//   final List<ESignModel>? data;
//
//   ESignModelList({this.data});
//
//   factory ESignModelList.fromJson(List<dynamic> parsedJson){
//     List<ESignModel>? data = <ESignModel>[];
//     data = parsedJson.map((i) => ESignModel.fromJson(i)).toList();
//     return ESignModelList(data: data);
//   }
// }