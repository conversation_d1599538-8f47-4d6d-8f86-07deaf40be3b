import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/popup_news_model.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';

class PopupNewsController extends GetxController {
  var isLoading = true.obs;
  var popupNewsList = PopupNewsList().obs;
  var currentPopupIndex = 0.obs;
  var shouldShowPopup = false.obs;
  var currentUserGroup = 'general'.obs; // กลุ่มผู้ใช้ปัจจุบัน
  var testInformationId = 1633; // ID สำหรับทดสอบ API

  @override
  void onInit() {
    super.onInit();
    _initializePopupNews();
  }

  // แยกการ initialize ออกมาเป็น async function
  Future<void> _initializePopupNews() async {
    try {
      await _detectUserGroup();

      // === เรียกใช้ API จริงแทน mock data ===
      await getPopupNews();
      await checkAndShowPopup();

      // === แสดง popup ทันทีหลังจากโหลดจาก API สำเร็จ ===
      if (popupNewsList.value.data != null &&
          popupNewsList.value.data!.isNotEmpty) {
        // บังคับให้แสดง popup ทันที
        shouldShowPopup.value = true;
        currentPopupIndex.value = 0;

        if (kDebugMode) {
          print("=== FORCE SHOW POPUP AFTER API LOAD ===");
          print("Total popups loaded: ${popupNewsList.value.data!.length}");
          print("Should show popup: ${shouldShowPopup.value}");
          print("Current popup index: ${currentPopupIndex.value}");
          print("Current popup title: ${currentPopup?.title ?? 'null'}");
          print("======================================");
        }
      } else {
        if (kDebugMode) {
          print("=== NO POPUPS LOADED FROM API ===");
          print("popupNewsList.data is null or empty");
          print("=================================");
        }
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("Error initializing popup news: $e");
      }
      isLoading(false);
    }
  }

  // ตรวจสอบกลุ่มผู้ใช้จากเบอร์โทรศัพท์
  Future<void> _detectUserGroup() async {
    try {
      String phoneNumber = "";
      String mrCode = "";
      String rankCurrent = "";

      // ดึงข้อมูลจริงจาก ProfileController
      try {
        final ProfileController profileCtl = Get.find<ProfileController>();
        phoneNumber = profileCtl.profile.value.mobile ?? '';
        mrCode = profileCtl.profile.value.mrCode ?? '';
        rankCurrent = profileCtl.rankMR.rankCurrent ?? '';
      } catch (e) {
        if (kDebugMode) {
          print("ProfileController not found: $e");
        }
      }

      // === MOCKUP DATA สำหรับทดสอบ ===
      if (kDebugMode) {
        // สามารถเปลี่ยนค่าเหล่านี้เพื่อทดสอบกลุ่มต่างๆ
        phoneNumber = '0864366509'; // เปลี่ยนเป็นเบอร์ที่ต้องการทดสอบ (MR)
        mrCode = 'MR001'; // mock MR code
        rankCurrent = 'platinum'; // mock rank

        print("=== MOCKUP USER DATA ===");
        print("phoneNumber: $phoneNumber");
        print("mrCode: $mrCode");
        print("rankCurrent: $rankCurrent");
        print("========================");
      }

      // กำหนดกลุ่มตามเบอร์โทรศัพท์
      currentUserGroup.value = _classifyUserGroupByPhone(phoneNumber);

      if (kDebugMode) {
        print("Phone-based user group classification:");
        print("Phone: $phoneNumber");
        print("Classified as: ${currentUserGroup.value}");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error detecting user group: $e");
      }
      currentUserGroup.value = 'general'; // default
    }
  }

  // ฟังก์ชันจำแนกกลุ่มผู้ใช้จากเบอร์โทรศัพท์
  String _classifyUserGroupByPhone(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      return 'general';
    }

    // ลบ prefix และ format เบอร์ให้เป็นรูปแบบมาตรฐาน
    String cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // ถ้าขึ้นต้นด้วย 66 ให้เอาออก
    if (cleanPhone.startsWith('66') && cleanPhone.length == 11) {
      cleanPhone = '0' + cleanPhone.substring(2);
    }

    // ตรวจสอบว่าเป็นเบอร์ไทยที่ถูกต้อง (10 หลัก ขึ้นต้นด้วย 0)
    if (cleanPhone.length != 10 || !cleanPhone.startsWith('0')) {
      return 'general';
    }

    // กฎการจำแนกกลุ่มตามเบอร์โทรศัพท์
    // ตัวอย่าง: 0864366509=MR, 0898098146=MA, 0918143589=SS

    // MR Group - เบอร์ที่ขึ้นต้นด้วย 086
    if (cleanPhone.startsWith('086')) {
      return 'mr';
    }

    // MA Group - เบอร์ที่ขึ้นต้นด้วย 089
    if (cleanPhone.startsWith('089')) {
      return 'ma';
    }

    // SS Group - เบอร์ที่ขึ้นต้นด้วย 091
    if (cleanPhone.startsWith('091')) {
      return 'ss';
    }

    // ถ้าไม่ตรงกฎใดๆ ให้เป็น general
    return 'general';
  }

  // ดึงข้อมูล popup news จาก API
  Future<dynamic> getPopupNews() async {
    try {
      isLoading(true);

      // === สำหรับการทดสอบ: ใช้ข้อมูลจาก getInformation API ===
      // ใช้ testInformationId property แทนการ hard code

      // Map data = {"id": testInformationId};

      // เรียก API getInformation
      final response =
          await AppApi.callAPIjwt("GET", AppUrl.getAllInformation,{});

      if (response['status'] == 200) {
        var result = response["result"];
        List<PopupNews> popupList = [];

        // ตรวจสอบว่า result เป็น array และมีข้อมูล
        if (result != null && result is List && result.isNotEmpty) {
          // วนลูปผ่านทุก item ใน result array
          for (var newsItem in result) {
            // แปลงข้อมูลข่าวสารเป็น popup news
            String title = newsItem["information_name"]?.toString() ?? "";
            String informationRole =
                newsItem["information_role"]?.toString() ?? "";

            // === สำหรับการทดสอบ: แสดง popup ทุกข่าวที่ได้รับ ===
            // ไม่กรองเงื่อนไข เพื่อให้ง่ายต่อการทดสอบ

            // กำหนด target groups จาก information_role หรือใช้ default
            List<String> targetGroups = [
              'general',
              'mr',
              'ma',
              'ss'
            ]; // default แสดงให้ทุกกลุ่ม

            if (informationRole.isNotEmpty) {
              // แปลง information_role เป็น target groups
              if (informationRole.toLowerCase().contains('mr')) {
                targetGroups = ['mr'];
              } else if (informationRole.toLowerCase().contains('ma')) {
                targetGroups = ['ma'];
              } else if (informationRole.toLowerCase().contains('ss')) {
                targetGroups = ['ss'];
              } else if (informationRole.toLowerCase().contains('all')) {
                targetGroups = ['general', 'mr', 'ma', 'ss'];
              }
            } else {
              // ถ้า information_role เป็น null หรือ empty แสดงให้ทุกกลุ่ม
              targetGroups = ['general', 'mr', 'ma', 'ss'];
            }

            // จัดการวันหมดอายุ
            DateTime? expiryDate;
            if (newsItem["information_date_end"] != null &&
                newsItem["information_date_end"] != "0000-00-00 00:00:00") {
              try {
                expiryDate = DateTime.parse(newsItem["information_date_end"]);
              } catch (e) {
                if (kDebugMode) {
                  print("Error parsing expiry date: $e");
                }
              }
            }

            // จัดการวันที่สร้าง
            DateTime createdDate = DateTime.now();
            if (newsItem["information_date"] != null) {
              try {
                createdDate = DateTime.parse(newsItem["information_date"]);
              } catch (e) {
                if (kDebugMode) {
                  print("Error parsing created date: $e");
                }
              }
            }

            // สร้าง PopupNews object
            PopupNews newPopup = PopupNews(
              id: newsItem["information_id"],
              title: title.replaceAll("[POPUP]", "").trim(),
              content: newsItem["information_body"] ?? "",
              imageUrl: newsItem["information_pic_secondary"]
                          ?.toString()
                          .isNotEmpty ==
                      true
                  ? newsItem["information_pic_secondary"]
                  : (newsItem["information_pic"] ?? ""),
              actionType: newsItem["information_link"] != null &&
                      newsItem["information_link"] != "-" &&
                      newsItem["information_link"].toString().isNotEmpty
                  ? "external"
                  : "none",
              actionValue: newsItem["information_link"] != "-"
                  ? (newsItem["information_link"] ?? "")
                  : "",
              buttonText: newsItem["information_link"] != null &&
                      newsItem["information_link"] != "-" &&
                      newsItem["information_link"].toString().isNotEmpty
                  ? "อ่านเพิ่มเติม"
                  : "ดูเพิ่มเติม",
              isActive: newsItem["information_flag"] == 1, // เช็คจาก flag
              createdDate: createdDate,
              expiryDate: expiryDate,
              targetGroups: targetGroups,
              aspectRatio: null, // ให้ popup ปรับขนาดตามรูปจริง
              widthPercent: null, // ไม่กำหนดความกว้าง ให้ปรับอัตโนมัติ
            );

            popupList.add(newPopup);
          }
        }

        popupNewsList.value = PopupNewsList(data: popupList);

        if (kDebugMode) {
          print("=== POPUP NEWS LOADED FROM getInformation API ===");
          print("Information ID: $testInformationId");
          print("API Response status: 200");
          print("Result is array: ${result is List}");
          print("Result length: ${result?.length ?? 0}");
          print("Popup news found: ${popupList.length}");
          if (popupList.isNotEmpty) {
            for (int i = 0; i < popupList.length; i++) {
              var popup = popupList[i];
              var originalItem = result[i]; // ใช้ข้อมูลจาก result array
              print("--- Popup ${i + 1} ---");
              print("- ID: ${popup.id}");
              print("- Title: ${popup.title}");
              print("- Target Groups: ${popup.targetGroups}");
              print(
                  "- Primary Image: ${originalItem["information_pic"] ?? 'null'}");
              print(
                  "- Secondary Image: ${originalItem["information_pic_secondary"] ?? 'null'}");
              print("- Selected Image URL: ${popup.imageUrl}");
              print("- Action Type: ${popup.actionType}");
              print("- Action Value: ${popup.actionValue}");
              print("- Is Active: ${popup.isActive}");
              print("- Created: ${popup.createdDate}");
              print("- Expires: ${popup.expiryDate}");
            }
          }
          print("================================================");
        }
      } else {
        popupNewsList.value = PopupNewsList(data: []);
        if (kDebugMode) {
          print("API Error: ${response['status']}");
          print("Response: ${response}");
        }
      }

      update();
    } catch (e) {
      if (kDebugMode) {
        print("error getPopupNews => $e");
      }
      popupNewsList.value = PopupNewsList(data: []);
    } finally {
      isLoading(false);
    }
  }

  // ตรวจสอบและกำหนดว่าควรแสดง popup หรือไม่
  Future<void> checkAndShowPopup() async {
    try {
      // ใช้ฟังก์ชันใหม่ที่กรองตามกลุ่มผู้ใช้
      List<PopupNews> activePopups =
          popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);

      if (kDebugMode) {
        print("=== checkAndShowPopup DEBUG INFO ===");
        print("User group: ${currentUserGroup.value}");
        print("Total popups in list: ${popupNewsList.value.data?.length ?? 0}");
        print("Active popups for user: ${activePopups.length}");
        print("Should show popup currently: ${shouldShowPopup.value}");
        print("====================================");
      }

      if (activePopups.isNotEmpty) {
        // === แสดง popup ทันทีในทุกโหมด ===
        PopupNews popupToShow = activePopups.first;
        currentPopupIndex.value = 0;
        shouldShowPopup.value = true;

        if (kDebugMode) {
          print("=== AUTO SHOWING POPUP ===");
          print("User group: ${currentUserGroup.value}");
          print("Popup title: ${popupToShow.title}");
          print("Target groups: ${popupToShow.targetGroups}");
          print("Image URL: ${popupToShow.imageUrl}");
          print("Action type: ${popupToShow.actionType}");
          print("Action value: ${popupToShow.actionValue}");
          print("Should show popup: ${shouldShowPopup.value}");
          print("==============================");
        } else {
          print("=== AUTO SHOWING POPUP (PRODUCTION) ===");
          print("Popup title: ${popupToShow.title}");
          print("Should show popup: ${shouldShowPopup.value}");
          print("=======================================");
        }

        // บันทึกลง SharedPreferences (สำหรับไม่แสดงซ้ำ)
        try {
          final prefs = await SharedPreferences.getInstance();
          final today = DateTime.now().toString().substring(0, 10);
          await prefs.setString('popup_last_shown_date', today);

          String popupId = popupToShow.id.toString();
          final shownPopupIds = prefs.getStringList('shown_popup_ids') ?? [];
          if (!shownPopupIds.contains(popupId)) {
            shownPopupIds.add(popupId);
            await prefs.setStringList('shown_popup_ids', shownPopupIds);
          }
        } catch (e) {
          if (kDebugMode) {
            print("Error saving popup state: $e");
          }
        }
      } else {
        if (kDebugMode) {
          print("=== NO POPUP TO SHOW ===");
          print("User group: ${currentUserGroup.value}");
          print("Total popups: ${popupNewsList.value.data?.length ?? 0}");
          print("========================");
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("error checkAndShowPopup => $e");
      }
    }
  }

  // บันทึกว่าแสดง popup แล้ว
  Future<void> markPopupAsShown(PopupNews popup) async {
    try {
      if (kDebugMode) {
        print("=== POPUP CLOSED ===");
        print("Popup shown: ${popup.title}");
        print("ID: ${popup.id}");
        print("====================");
      }

      // บันทึกลง SharedPreferences สำหรับทุกโหมด
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toString().substring(0, 10);
      final shownPopupIds = prefs.getStringList('shown_popup_ids') ?? [];

      String popupId = popup.id.toString();
      if (!shownPopupIds.contains(popupId)) {
        shownPopupIds.add(popupId);
        await prefs.setStringList('shown_popup_ids', shownPopupIds);
      }

      await prefs.setString('popup_last_shown_date', today);

      if (kDebugMode) {
        print("Popup marked as shown and saved to SharedPreferences");
      }
    } catch (e) {
      if (kDebugMode) {
        print("error markPopupAsShown => $e");
      }
    }
  }

  // ปิด popup
  void closePopup() {
    shouldShowPopup.value = false;
    List<PopupNews> activePopups =
        popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);
    if (activePopups.isNotEmpty &&
        currentPopupIndex.value < activePopups.length) {
      markPopupAsShown(activePopups[currentPopupIndex.value]);
    }
  }

  // ไปยัง popup ถัดไป (ถ้ามีหลายอัน)
  void nextPopup() {
    List<PopupNews> activePopups =
        popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);
    if (activePopups.isNotEmpty) {
      markPopupAsShown(activePopups[currentPopupIndex.value]);

      if (currentPopupIndex.value < activePopups.length - 1) {
        currentPopupIndex.value++;
      } else {
        closePopup();
      }
    }
  }

  // ได้ popup ปัจจุบันที่ควรแสดง (กรองตามกลุ่มผู้ใช้)
  PopupNews? get currentPopup {
    List<PopupNews> activePopups =
        popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);
    if (activePopups.isNotEmpty &&
        currentPopupIndex.value < activePopups.length) {
      return activePopups[currentPopupIndex.value];
    }
    return null;
  }

  // รีเฟรช popup news
  Future<void> refreshPopupNews() async {
    await getPopupNews();
    await checkAndShowPopup();
  }

  // สำหรับการทดสอบ: แสดง popup ใหม่อีกครั้ง
  void showPopupAgain() {
    if (kDebugMode && popupNewsList.value.data != null) {
      List<PopupNews> activePopups =
          popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);

      if (activePopups.isNotEmpty) {
        currentPopupIndex.value = 0;
        shouldShowPopup.value = true;

        print("=== SHOWING POPUP AGAIN (DEBUG MODE) ===");
        print("User group: ${currentUserGroup.value}");
        print("Active popups: ${activePopups.length}");
        print("========================================");
      }
    }
  }

  // สำหรับการทดสอบ: เปลี่ยนกลุ่มผู้ใช้และแสดง popup
  void testPopupForUserGroup(String userGroup) {
    if (kDebugMode) {
      currentUserGroup.value = userGroup;
      showPopupAgain();

      print("=== TESTING POPUP FOR USER GROUP ===");
      print("Changed user group to: $userGroup");
      print("====================================");
    }
  }

  // สำหรับการทดสอบ: ทดสอบด้วยเบอร์โทรศัพท์
  void testPopupWithPhoneNumber(String phoneNumber) {
    if (kDebugMode) {
      String detectedGroup = _classifyUserGroupByPhone(phoneNumber);
      currentUserGroup.value = detectedGroup;
      showPopupAgain();

      print("=== TESTING POPUP WITH PHONE NUMBER ===");
      print("Phone number: $phoneNumber");
      print("Detected group: $detectedGroup");
      print("=======================================");
    }
  }

  // สำหรับการทดสอบ: แสดงกฎการจำแนกกลุ่ม
  void showPhoneClassificationRules() {
    if (kDebugMode) {
      print("=== PHONE CLASSIFICATION RULES ===");
      print("MR Group: 086xxx (ตัวอย่าง: 0864366509)");
      print("MA Group: 089xxx (ตัวอย่าง: 0898098146)");
      print("SS Group: 091xxx (ตัวอย่าง: 0918143589)");
      print("General: อื่นๆ ทั้งหมด");
      print("==================================");

      // ทดสอบตัวอย่าง
      List<String> testPhones = [
        '0864366509', // MR (ตัวอย่างจริง)
        '0867890123', // MR (086xxx)
        '0898098146', // MA (ตัวอย่างจริง)
        '0895678901', // MA (089xxx)
        '0918143589', // SS (ตัวอย่างจริง)
        '0912345678', // SS (091xxx)
        '0812345678', // General (081xxx)
        '0889012345', // General (088xxx)
        '0909123456', // General (090xxx)
      ];

      print("\n=== CLASSIFICATION EXAMPLES ===");
      for (String phone in testPhones) {
        String group = _classifyUserGroupByPhone(phone);
        print("$phone -> $group");
      }
      print("===============================");
    }
  }

  // สำหรับการทดสอบ: ทดสอบ API โดยตรง
  Future<void> testApiDirectly({int? informationId}) async {
    if (kDebugMode) {
      print("=== TESTING API DIRECTLY ===");
      int testId = informationId ?? 1633;
      print("Testing with information ID: $testId");

      try {
        Map data = {"id": testId};
        final response =
            await AppApi.callAPIjwt("GET", AppUrl.getAllInformation, data);

        print("API Response Status: ${response['status']}");
        print("API Response: $response");

        if (response['status'] == 200) {
          var result = response["result"];
          print("Result type: ${result.runtimeType}");
          print("Result is List: ${result is List}");
          if (result is List) {
            print("Result length: ${result.length}");
            for (int i = 0; i < result.length; i++) {
              var item = result[i];
              print("--- Item $i ---");
              print("ID: ${item['information_id']}");
              print("Name: ${item['information_name']}");
              print("Role: ${item['information_role']}");
              print("Flag: ${item['information_flag']}");
              print("Link: ${item['information_link']}");
              print("Primary Image: ${item['information_pic']}");
              print("Secondary Image: ${item['information_pic_secondary']}");

              // แสดงรูปที่จะถูกเลือกใช้
              String selectedImage =
                  item['information_pic_secondary']?.toString().isNotEmpty ==
                          true
                      ? item['information_pic_secondary']
                      : (item['information_pic'] ?? '');
              print("Selected Image: $selectedImage");
            }
          }
        }
      } catch (e) {
        print("API Test Error: $e");
      }
      print("============================");
    }
  }

  // สำหรับการทดสอบ: เปลี่ยน Information ID และโหลดใหม่
  Future<void> changeInformationId(int newId) async {
    if (kDebugMode) {
      print("=== CHANGING INFORMATION ID ===");
      print("Old ID: $testInformationId");
      print("New ID: $newId");

      testInformationId = newId;
      await refreshPopupNews();

      print("Popup reloaded with new ID");
      print("===============================");
    }
  }

  // สำหรับการทดสอบ: โหลด popup จาก API ใหม่
  Future<void> reloadFromApi() async {
    if (kDebugMode) {
      print("=== RELOADING FROM API ===");
      print("Using Information ID: $testInformationId");

      await getPopupNews();
      await checkAndShowPopup();

      print("Popup reloaded from API");
      print("==========================");
    }
  }

  // สำหรับการทดสอบ: ตรวจสอบสถานะ popup ทั้งหมด
  void debugPopupStatus() {
    if (kDebugMode) {
      print("=== POPUP STATUS DEBUG ===");
      print("isLoading: ${isLoading.value}");
      print("shouldShowPopup: ${shouldShowPopup.value}");
      print("currentPopupIndex: ${currentPopupIndex.value}");
      print("currentUserGroup: ${currentUserGroup.value}");
      print("testInformationId: $testInformationId");
      print(
          "popupNewsList.data length: ${popupNewsList.value.data?.length ?? 0}");

      if (popupNewsList.value.data != null &&
          popupNewsList.value.data!.isNotEmpty) {
        print("--- Available Popups ---");
        for (int i = 0; i < popupNewsList.value.data!.length; i++) {
          var popup = popupNewsList.value.data![i];
          print("Popup $i: ${popup.title} (Active: ${popup.isActive})");
        }
      }

      List<PopupNews> activePopups =
          popupNewsList.value.getActivePopupsForUser(currentUserGroup.value);
      print("Active popups for current user: ${activePopups.length}");

      if (currentPopup != null) {
        print("Current popup to show: ${currentPopup!.title}");
      } else {
        print("Current popup: null");
      }

      print("==========================");
    }
  }

  // สำหรับการทดสอบ: บังคับแสดง popup ทันที
  void forceShowPopup() {
    if (kDebugMode &&
        popupNewsList.value.data != null &&
        popupNewsList.value.data!.isNotEmpty) {
      shouldShowPopup.value = true;
      currentPopupIndex.value = 0;

      print("=== FORCE SHOWING POPUP ===");
      print("Forced shouldShowPopup to: ${shouldShowPopup.value}");
      print("Set currentPopupIndex to: ${currentPopupIndex.value}");
      print("Current popup: ${currentPopup?.title ?? 'null'}");
      print("===========================");
    }
  }
}
