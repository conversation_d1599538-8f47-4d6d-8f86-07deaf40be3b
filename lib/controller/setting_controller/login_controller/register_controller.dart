import 'dart:convert';
import 'dart:math';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/view_get_likepoint_controller.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:http/http.dart' as http;

import '../../../component/alert.dart';
import '../../../component/api.dart';
import '../../../component/connect.dart';
import '../../../component/loader.dart';
import '../../../component/secureStorage.dart';
import '../../../component/url.dart';
import '../../../index.dart';
import '../../../model/check_phone_exist.dart';
import '../../../model/generator_token.dart';
import '../../../model/login_apple.dart';
import '../../../model/login_line.dart';
import '../../../model/member.dart';
import '../../../model/send_code.dart';
import '../../../model/verify_code.dart';
import '../../../view/home/<USER>';
import '../../service/service.dart';
import '../chat_in_app_controller.dart';
import '../profile_controller.dart';
// import '../webview_tg_controller.dart';
import 'agreement_controller.dart';
import 'login_controller.dart';

class RegisterController extends GetxController {
  final SecureStorage secureStorage = SecureStorage();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  final loginCtl = Get.put(LoginController());

  ResponseSendCode responseSendCode = ResponseSendCode();
  ResponseVerifyCode responseVerify = ResponseVerifyCode();
  ResponseGenToken responseGenToken = ResponseGenToken();
  ResponseCheckMember responseCheckMember = ResponseCheckMember();
  // Line line = Line();

  final profileCtl = Get.put(ProfileController());
  final agreementCtl = Get.put(AgreementController());
  // final webViewLikePointController = Get.put(WebViewLikePointController());

  //TODO :: new
  final TextEditingController firstNameTextController = TextEditingController();
  final TextEditingController lastNameTextController = TextEditingController();
  final TextEditingController refTextController = TextEditingController();

  RxBool isScanned = false.obs;

  Line line = Line();
  Apple apple = Apple();
  //TODO :: new

  dataRegisterSocial(context, phone, smsCode,registerType,id,refPhone,name) async {
    var statusCheckVerify = await checkVerify(context, phone, smsCode, loginCtl.responseSendCode.refCode.toString(), "Prachakij");
    if(statusCheckVerify == 200){
      var statusRegisSocial = await registerWithSocial(context, registerType, id, phone, refPhone, name);
      if(statusRegisSocial == 200){
        var statusCheckPhone = await checkPhone(context, phone);
        if(statusCheckPhone == 203){
          var statusGenToken = await genToken(context, responseCheckMember.id.toString(), responseCheckMember.roleId.toString());
          if(statusGenToken == 200){
            await AppService.setPref('bool', 'loginStatus', true);
            await secureStorage.writeSecureData("userId", responseCheckMember.id.toString());
            await secureStorage.writeSecureData("accessToken", responseGenToken.accessToken.toString());
            await profileCtl.getProfileAndMR();
            saveTokenMessage(responseCheckMember.mobile.toString());
            Get.offAll(() => const IndexPage());
            loginCtl.isLoading.value = false;
            update();
          } else {
            if (kDebugMode) {
              print('gen token ไม่สำเร็จ');
            }
            return false;
          }
        } else if(statusCheckPhone == 200){
          return statusCheckPhone;
        } else {
          if (kDebugMode) {
            print('statusCheckPhone ไม่สำเร็จ');
          }
          return false;
        }
      } else if(statusRegisSocial == 202){
        if (kDebugMode) {
          print("มีชื่อผู้ใช้อยู่แล้ว");
        }
        return false;
      } else {
        if (kDebugMode) {
          print("สมัครไม่สำเร็จ");
        }
        return false;
      }
    } else {
      if (kDebugMode) {
        print('ยืนยัน otp ไม่สำเร็จ');
      }
      return false;
    }
  }

  Future<dynamic> checkVerify(context, String phone, String smsCode, String refCode, String smsFrom) async {
    try{
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": smsCode,
          "refCode": refCode,
          "fromBU": smsFrom,

        });
        final resVerify = await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
        responseVerify = ResponseVerifyCode.fromJson(resVerify);

        var status = resVerify['statusCode'];

        update();
        return status;
      }
    }catch(e){
      print('$e => error checkVerify');
    }
  }

  Future<dynamic> checkPhone(context, phone) async {
    try {
      loginCtl.isLoading(true);
      CheckPhone valueCheckPhone = CheckPhone.fromJson({
        "phone": phone,
      });
      final responseCheckPhone = await AppApi.post(AppUrl.checkPhoneExist, valueCheckPhone.toJson());
      if(responseCheckPhone['status'] == 203){
        responseCheckMember = ResponseCheckMember.fromJson(responseCheckPhone['result'][0]);
      }
      var status = responseCheckPhone['status'];
      update();
      return status;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> genToken(context, String userId, String roleId) async {
    try {
      loginCtl.isLoading(true);
      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken = await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      update();
      return statusGen;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {
      loginCtl.isLoading(false);
    }
  }

  void saveTokenMessage(String phone) async {
    _firebaseMessaging.getToken().then((token) async {
      assert(token != null);

      Map data = {"phone": phone, "token": token};
      final response = await AppApi.post(AppUrl.saveNotifyToken, data);
      int statusCode = response["status"];

      if (statusCode == 200) {
        if (kDebugMode) {
          print("saveTokenMessage success");
        }
      } else {
        if (kDebugMode) {
          print('Unsuccessful save NotifyToken ');
        }
      }
    });
  }

  registerWithSocial(context, registerType,id, phone, refPhone,name) async {
    try{
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";
        Map member = {
          "typeConnect": registerType,
          "userID": id,
          "phone": phone,
          "phoneFirebase": phoneCode,
          "refcode": refPhone,
          "displayName": name,
          "filedLike_uid": "firebase_uid",
        };
        final resMemberRegisterWithSocial = await AppApi.post(AppUrl.memberRegisterWithSocial, member);
        if(resMemberRegisterWithSocial["status"] == 200){
          var idMember = "";
          if(resMemberRegisterWithSocial['action'] == "new"){
            if (refPhone.length == 10){
              AppService.getPOIReward(context, refPhone,
                  phone, "ชวนเพื่อนดาวโหลดแอพ PMS", 499);
            }
            idMember = resMemberRegisterWithSocial['result']['insertId'].toString();
            // AppLoader.dismiss(context);
          } else {
            idMember = resMemberRegisterWithSocial['result'][0]['id'].toString();
            // AppLoader.dismiss(context);
          }
          return resMemberRegisterWithSocial["status"];
        }  else if (resMemberRegisterWithSocial["status"] == 202) {
          AppLoader.dismiss(context);
          await AppAlert.showError(context, 'มีชื่อผู้ใช้นี้ในระบบแล้ว', 'ตกลง');
          return resMemberRegisterWithSocial["status"];
        } else if (resMemberRegisterWithSocial["status"] == 500) {
          AppLoader.dismiss(context);
          AppService.sendError(
              resMemberRegisterWithSocial["status"], 'registerWithSocial on login');
          AppAlert.showError(context, 'Internal server error', 'ตกลง');
          return resMemberRegisterWithSocial["status"];
        }
        update();
      }
    }catch(e){
      print('$e => error registerWithSocial');
    }
  }

  //TODO :: STEP 1 for register => Check Profile เช็คเพื่อทำการสมัครต่อหรือให้ไป login
  newCheckProfile(context) async {
    try {
      if(firstNameTextController.text == ""){
        AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "ท่านไม่ได้กรอกชื่อจริง", "ตกลง");
        return;
      }
      if(lastNameTextController.text == ""){
        AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "ท่านไม่ได้กรอกนามสกุล", "ตกลง");
        return;
      }
      if(loginCtl.phoneTextController.text == ""){
        AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "ท่านไม่ได้กรอกเบอร์โทรศัพท์", "ตกลง");
        return;
      }
      if(loginCtl.phoneTextController.text.length != 10){
        AppAlert.showNewAccept(context, "กรุณาตรวจสอบ", "เบอร์โทรของท่าน\nไม่ครบ 10 หลัก", "ตกลง");
        return;
      }

      AppLoader.loader(context);
      CheckPhone valueCheckPhone = CheckPhone.fromJson({
        "phone": loginCtl.phoneTextController.text,
      });
      final responseCheckProfile = await AppApi.post(AppUrl.checkPhoneExist, valueCheckPhone.toJson());
      var status = responseCheckProfile['status'];
      if(status == 200){
        //TODO :: STEP 2 for register => Send OTP
        await loginCtl.newSendOTP(context);
        AppLoader.dismiss(context);
      } else if(status == 203){
        AppLoader.dismiss(context);
        var res = await AppAlert.showNewAccept(context, "แจ้งเตือน", "เบอร์นี้เคยทำการสมัครสมาชิกแล้ว\nกรุณาล็อคอินเพื่อใช้งาน", "ตกลง");
        if(res){
          loginCtl.typeMenu.value = "login";
          loginCtl.page.value = "login";
        }
        return 200;
      } else {
        AppLoader.dismiss(context);
        return false;
      }
      update();
      return status;
    } catch (e) {
      if (kDebugMode) {
        print('$e => error checkProfile');
      }
    }
  }

  //TODO :: STEP 3 for register => Check OTP
  newCheckOTP(context) async {
    try {
      AppLoader.loader(context);
      var phoneCode = "+66${loginCtl.phoneTextController.text.substring(1)}";
      VerifyCode valueVerify = VerifyCode.fromJson({
        "phone": phoneCode,
        "otpCode": loginCtl.pinTextController.text,
        "refCode": loginCtl.responseSendCode.refCode.toString(),
        "fromBU": "Prachakij",
      });
      final resVerify = await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
      responseVerify = ResponseVerifyCode.fromJson(resVerify);
      var status = resVerify['statusCode'];
      if(status == 200){
        await register(context);
        return true;
      } else {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "แจ้งเตือน", "กรุณาตรวจสอบรหัส OTP ที่ท่านกรอก", "ตกลง");
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: STEP 4 for register => Register
  registerCenter(context) async {
    try {
      String phoneValue = "+66${loginCtl.phoneTextController.text.substring(1)}";
      Map data = {
        "reqPhone": loginCtl.phoneTextController.text,
        "phone_nationCode": phoneValue,
        "owner": "MappPMS",
      };
      final responseRegisterCenter = await AppApi.post(AppUrl.registerCenter, data);
      var status = responseRegisterCenter['statusCode'];
      update();
      return status;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  register(context) async {
    try {
      String phoneValue = "+66${loginCtl.phoneTextController.text.substring(1)}";
      String refCodeMr = "MR-${refTextController.text}";
      Map member = {
        "phone": loginCtl.phoneTextController.text,
        "phoneFirebase": phoneValue,
        "firstName": firstNameTextController.text,
        "lastName": lastNameTextController.text,
        "refcode": refCodeMr,
        "displayName": "${firstNameTextController.text} ${lastNameTextController.text}",
      };
      print("memberRegister : $member");
      final resMemberRegister = await AppApi.callAPIjwt("POST", AppUrl.registerWithRefCode, member);
      // final resMemberRegister = await AppApi.post('https://7cd9-134-236-98-140.ngrok-free.app/PMS/register/registerWithRefcode', member);
      if(resMemberRegister["status"] == 200){
        var idMember = resMemberRegister['result']['memberID'].toString();
        Get.snackbar(
            'คุณได้รับ ${AppService.numberFormatNon0(resMemberRegister['result']['resSaveActivity']['amountPay'])} PMSpoint',
            'จากกิจกรรม ${resMemberRegister['result']['resSaveActivity']['activityName']}',
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP
        );
        await newGenTokenWithSocial(context, idMember, "user");
        update();
        return resMemberRegister["status"];
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: STEP 5 for register => Gen Token
  newGenToken(context, userId, roleId) async {
    try {
      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken = await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      if(statusGen == 200){
        loginCtl.typeMenu.value = "login";
        loginCtl.page.value = "login";
        await AppService.setPref('bool', 'loginStatus', true);
        await secureStorage.writeSecureData("userId", userId);
        await secureStorage.writeSecureData("accessToken", responseGenToken.accessToken.toString());
        searchElasticAndSaveProfile(context, loginCtl.phoneTextController.text);
        searchElasticAndSaveCar(context, loginCtl.phoneTextController.text);
        saveTokenMessage(loginCtl.phoneTextController.text);
        //TODO :: STEP 6 for register => get Profile
        await profileCtl.getProfileAndMR();
        Future.delayed(Duration.zero, () {
          Get.offAll(() => const HomeNavigator());
        });
        AppLoader.dismiss(context);
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "สร้าง Token ไม่สำเร็จ\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: searchElasticAndSaveCar
  searchElasticAndSaveCar(context, phone) async {
    try {
      Map data = {
        "indexName": "cdm_carcenter_profile",
        "typeName": "default",
        "elasQuery": {
          "query": {
            "bool": {
              "must": [
                {
                  "wildcard": {"mobile.keyword": "${"*" + phone}*"}
                }
              ],
              "must_not": [],
              "should": []
            }
          },
          "from": 0,
          "size": 10,
          "sort": [],
          "aggs": {}
        }
      };

      final response = await AppApi.post(AppUrl.searchElastic, data);

      if (response['total'] > 0) {
        //เจอข้อมูลเบอร์นี้ใน elastic
        for (int i = 1; i <= response['data'].length; i++) {
          Map data = {
            "phone": phone,
            "create_user": "from cdm_carcenter_profile",
            "character": "",
            "number": "",
            "province": "",
            "car_license": response['data'][i - 1]['car_reg_no'],
            "car_engine_no": response['data'][i - 1]['car_engine_no'],
          };

          final responseData = await AppApi.post(AppUrl.saveCar, data);

          if (i == response['data'].length) {
            return true;
          }
        }
      } else {
        //ไม่เจอข้อมูลลูกค้าใน elastic
        return true;
      }
    } catch (e) {
      AppAlert.showError(context, 'Error : searchElasticAndSaveCar', 'ตกลง');
    }
  }

  //TODO :: searchElasticAndSaveProfile
  searchElasticAndSaveProfile(context, phone) async {
    try {
      Map data = {
        "indexName": "cdm_customercenter_profile",
        "typeName": "default",
        "elasQuery": {
          "query": {
            "bool": {
              "must": [
                {
                  "wildcard": {"mobile.keyword": "${"*" + phone}*"}
                }
              ],
              "must_not": [],
              "should": []
            }
          },
          "from": 0,
          "size": 10,
          "sort": [],
          "aggs": {}
        }
      };

      final response = await AppApi.post(AppUrl.searchElastic, data);
      if (response['total'] > 0) {
        String address = '';
        var bdArray;
        if (response['data'][0]['birthday'] != "") {
          bdArray = response['data'][0]['birthday'].split("T");
        } else {
          bdArray[0] = null;
        }

        if (response['data'][0]['ad_address'] != "") {
          address += response['data'][0]['ad_address'];
        }
        if (response['data'][0]['ad_moo'] != "") {
          address += " หมู่ " + response['data'][0]['ad_moo'];
        }
        if (response['data'][0]['ad_soi'] != "") {
          address += " ถนน " + response['data'][0]['ad_soi'];
        }
        if (response['data'][0]['ad_tumbol'] != "") {
          address += " " + response['data'][0]['ad_tumbol'];
        }
        if (response['data'][0]['ad_amp'] != "") {
          address += " " + response['data'][0]['ad_amp'];
        }
        if (response['data'][0]['ad_province'] != "") {
          address += " " + response['data'][0]['ad_province'];
        }
        if (response['data'][0]['ad_zipcode'] != "") {
          address += " " + response['data'][0]['ad_zipcode'];
        }

        Map dataUpdate = {
          "table": "customercenter",
          "phone": phone,
          "firstname": response['data'][0]['firstname'],
          "lastname": response['data'][0]['lastname'],
          "idcard": response['data'][0]['idcard'],
          "birthday": bdArray[0],
          "address": address,
          "number": response['data'][0]['ad_address'],
          "moo": response['data'][0]['ad_moo'],
          "road": response['data'][0]['ad_soi'],
          "tumbol": response['data'][0]['ad_tumbol'],
          "amphur": response['data'][0]['ad_amp'],
          "province": response['data'][0]['ad_province'],
          "zipcode": response['data'][0]['ad_zipcode'],
          "email": response['data'][0]['email'],
          "field_address": "address",
          "field_firstname": "firstname",
          "field_lastname": "lastname",
          "field_idcard": "idcard",
          "field_birthday": "birthday",
          "field_number": "address_number",
          "field_moo": "address_moo",
          "field_road": "address_road",
          "field_tumbol": "address_tumbol",
          "field_amphur": "address_amphur",
          "field_province": "address_province",
          "field_zipcode": "address_zipcode",
          "field_email": "email",
          "field_fullname": "fullname",
          "field_user_phone": "mobile"
        };

        final responseData =
        await AppApi.post(AppUrl.saveProfileNotReplace, dataUpdate);
        return true;
      } else {
        //ไม่เจอข้อมูลลูกค้าใน elastic
        return true;
      }
    } catch (e) {
      AppAlert.showError(
          context, 'Error : searchElasticAndSaveProfile', 'ตกลง');
    }
  }

  //TODO :: STEP 1 for register with line => เรียกข้อมูล Line
  lineLogin(context) async {
    try {
      await LineSDK.instance.login(scopes: ["profile"]);
      final profile = await LineSDK.instance.getProfile();
      line = Line.fromJson(profile.data);
      var status = await loginWithLine(context, line.userId);
      return status;
    } on PlatformException catch (e) {
      if (e.code != 'CANCEL') {
        AppAlert.showError(context, 'Error : lineLogin', 'ตกลง');
      }
    }
  }

  //TODO :: STEP 2 for register with line => เช็คว่ามีข้อมูลในระบบหรือยัง
  //TODO :: ถ้า return status 200 ให้ login ถ้า 204 login fail ถ้า 404 ให้ register
  loginWithLine(context, lineID) async {
    try {
      AppLoader.loader(context);
      Map data = {"lineID": lineID};
      final response = await AppApi.post(AppUrl.checkMemberWithLine, data);
      int status = response["status"];
      //TODO :: status 200 LOGIN
      if (status == 200) {
        var member = response['result'][0];
        //TODO :: Gen token
        newGenTokenWithSocial(context, member['id'].toString(), member['roleId'].toString());
      } else if (status == 204) {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "ไม่สามารถ login ได้\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
        return status;
      } else if (status == 404) {
        AppLoader.dismiss(context);
        loginCtl.typeMenu.value = "registerWithLine";
        loginCtl.page.value = "register";
        return status;
      }
    } catch (e) {
      AppAlert.showError(context, 'ERROR : loginWithLine', 'ตกลง');
    }
  }

  //TODO :: Gen Token for register with social
  newGenTokenWithSocial(context, userId, roleId) async {
    try {
      GenToken valueGenToken = GenToken.fromJson({
        "userId": userId,
        "appId": "pmsMobile",
        "roleId": roleId,
      });
      final resGenToken = await AppApi.post(AppUrl.generatorToken, valueGenToken.toJson());
      responseGenToken = ResponseGenToken.fromJson(resGenToken);
      var statusGen = resGenToken['status'];
      if(statusGen == 200){
        loginCtl.typeMenu.value = "login";
        loginCtl.page.value = "login";
        await AppService.setPref('bool', 'loginStatus', true);
        await secureStorage.writeSecureData("userId", userId);
        await secureStorage.writeSecureData("accessToken", responseGenToken.accessToken.toString());
        saveTokenMessage(loginCtl.phoneTextController.text);
        //TODO :: STEP 6 for register => get Profile
        await profileCtl.getProfileAndMR();
        Future.delayed(Duration.zero, () {
          Get.offAll(() => const IndexPage());
        });
        AppLoader.dismiss(context);
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "สร้าง Token ไม่สำเร็จ\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //TODO :: Register With Social => Check OTP
  newCheckOTPWithSocial(context) async {
    try{
      AppLoader.loader(context);
      if (loginCtl.phoneTextController.text.length == 10) {
        var phoneCode = "+66${loginCtl.phoneTextController.text.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": loginCtl.pinTextController.text,
          "refCode": loginCtl.responseSendCode.refCode,
          "fromBU": "Prachakij",
        });
        final resVerify = await AppApi.post(AppUrl.verifyOTP, valueVerify.toJson());
        responseVerify = ResponseVerifyCode.fromJson(resVerify);
        var status = resVerify['statusCode'];
        if(status == 200){
          //TODO :: ทำ Register ต่อ
          if(loginCtl.typeMenu.value == "registerWithLine"){
            newRegisterWithSocial(context, "line", line.userId, loginCtl.phoneTextController.text, refTextController.text, firstNameTextController.text, firstNameTextController.text, lastNameTextController.text);
          } else if (loginCtl.typeMenu.value == "registerWithApple"){
            newRegisterWithSocial(context, "apple", apple.userIdentifier, loginCtl.phoneTextController.text, refTextController.text, firstNameTextController.text, firstNameTextController.text, lastNameTextController.text);
          }
        } else {
          AppLoader.dismiss(context);
          await AppAlert.showNewAccept(context, "แจ้งเตือน", "กรุณาตรวจสอบรหัส OTP ที่ท่านกรอก", "ตกลง");
          return false;
        }
        update();
        return status;
      }
    }catch(e){
      if (kDebugMode) {
        print('$e => error newCheckOTPWithSocial');
      }
    }
  }

  //TODO :: Register With Social => Register
  newRegisterWithSocial(context, typeConnect ,userId, phone, refCode, name, firstname, lastname) async {
    try{
      var phoneCode = "+66${phone.substring(1)}";
      Map member = {
        "typeConnect": typeConnect,
        "userID": userId,
        "phone": phone,
        "phoneFirebase": phoneCode,
        "refcode": refCode,
        "displayName": name,
        "firstName": firstname,
        "lastName": lastname,
      };

      final resMemberRegisterWithSocial = await AppApi.callAPIjwt("POST", AppUrl.registerSocial, member);
      // print(resMemberRegisterWithSocial);
      if(resMemberRegisterWithSocial["status"] == 200){
        var idMember = resMemberRegisterWithSocial['result']['memberID'].toString();
        Get.snackbar(
            'คุณได้รับ ${AppService.numberFormatNon0(resMemberRegisterWithSocial['result']['resSaveActivity']['amountPay'])} PMSpoint',
            'จากกิจกรรม ${resMemberRegisterWithSocial['result']['resSaveActivity']['activityName']}',
            colorText: Colors.white,
            snackPosition: SnackPosition.TOP
        );
        await newGenTokenWithSocial(context, idMember, "user");
        return resMemberRegisterWithSocial["status"];
      }  else if (resMemberRegisterWithSocial["status"] == 202) {
        AppLoader.dismiss(context);
        await AppAlert.showNewAccept(context, "แจ้งเตือน",'มีชื่อผู้ใช้นี้ในระบบแล้ว', 'ตกลง');
        return resMemberRegisterWithSocial["status"];
      } else if (resMemberRegisterWithSocial["status"] == 500) {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด",'Internal server error', 'ตกลง');
        return resMemberRegisterWithSocial["status"];
      } else {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง", "ตกลง");
      }
      update();
    } catch (e){
      if (kDebugMode) {
        print('error newRegisterWithSocial => $e');
      }
    }
  }

  //TODO :: STEP 1 for register with apple => เรียกข้อมูล apple
  Future<int?> appleLogin(BuildContext context) async {
    try {
      if (kDebugMode) {
        print('join appleLogIn');
      }

      // ตรวจสอบว่า Sign in with Apple พร้อมใช้งานหรือไม่
      final isAvailable = await SignInWithApple.isAvailable();
      if (!isAvailable) {
        print('Sign in with Apple is not available on this device');
        return null;
      }

      // ดึงข้อมูลจาก Sign in with Apple
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // บันทึก userIdentifier
      apple.userIdentifier = credential.userIdentifier;

      // ดึงข้อมูลชื่อ, นามสกุล, และอีเมล
      final String? givenName = credential.givenName;
      final String? familyName = credential.familyName;
      final String? email = credential.email;

      if (kDebugMode) {
        print('User ID: ${credential.userIdentifier}');
        print('Name: $givenName');
        print('Lastname: $familyName');
        print('Email: $email');
      }

      // เรียก autoFillFullname เพื่อเติมข้อมูลใน TextController (ถ้ามี)
      autoFillFullname(givenName, familyName);

      // ส่งข้อมูลไปยัง loginWithApple
      var status = await loginWithApple(context, credential.userIdentifier, email: email, givenName: givenName, familyName: familyName);

      return status;
    } catch (e) {
      if (kDebugMode) {
        print('$e => error appleLogin');
      }
      AppAlert.showError(context, 'ERROR: appleLogin', 'ตกลง');
      return null;
    }
  }

  //TODO :: STEP 2 for register with apple => เช็คว่ามีข้อมูลในระบบหรือยัง
  //TODO :: ถ้า return status 200 ให้ login ถ้า 204 login fail ถ้า 404 ให้ register
  Future<int?> loginWithApple(
      BuildContext context,
      String? appleID, {
        String? email,
        String? givenName,
        String? familyName,
      }) async {
    try {
      AppLoader.loader(context);
      Map data = {
        "appleID": appleID,
        "email": email ?? 'No email provided', // ส่งอีเมลไปเซิร์ฟเวอร์ (อาจเป็น proxy email)
        "givenName": givenName ?? '', // ส่งชื่อ (ถ้ามี)
        "familyName": familyName ?? '', // ส่งนามสกุล (ถ้ามี)
      };
      final response = await AppApi.post(AppUrl.checkMemberWithApple, data);
      int status = response["status"];

      if (status == 200) {
        var member = response['result'][0];
        newGenTokenWithSocial(context, member['id'].toString(), member['roleId'].toString());
      } else if (status == 204) {
        AppLoader.dismiss(context);
        AppAlert.showNewAccept(context, "เกิดข้อผิดพลาด", "ไม่สามารถ login ได้\nกรุณาลองใหม่อีกครั้ง", "ตกลง");
      } else if (status == 404) {
        AppLoader.dismiss(context);
        loginCtl.typeMenu.value = "registerWithApple";
        loginCtl.page.value = "register";
        // ไม่ต้องขอชื่อ/อีเมลเพิ่มที่นี่ ใช้ข้อมูลจาก Sign in with Apple แทน
      }
      return status;
    } catch (e) {
      AppLoader.dismiss(context);
      AppAlert.showError(context, 'ERROR: loginWithApple', 'ตกลง');
      return null;
    }
  }

  void autoFillFullname(String? name, String? lastname) {
    if (name != null) {
      firstNameTextController.text = name;
    }
    if (lastname != null) {
      lastNameTextController.text = lastname;
    }
    update();
  }
}