import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/model/car_catalog.dart';
import 'package:mapp_prachakij_v3/model/promotion.dart';

class PromotionController extends GetxController {

  var isLoading = true.obs;

  PromotionList promotionList = PromotionList();
  CarCatalogList carCatalogList = CarCatalogList();
  PsiController psiCtl = Get.put(PsiController());

  RxString url = "".obs;
  RxString urlEvent = "".obs;

  @override
  void onInit() async {
    super.onInit();
    await getURL();
    await getPromotion();
    update();
  }

  getPromotion() async {
    try {
      isLoading.value = true;
      print("getPromotion");
      final responsePromotion = await AppApi.callAPIjwt("GET", AppUrl.getPromotions, {});
      // print(responsePromotion);
      var status = responsePromotion['status'];
      if(status == 200){
        promotionList = PromotionList.fromJson(responsePromotion["result"]);
      }
      else {
        promotionList = PromotionList(data: []);
      }
      await getCarCatalog();
      // await psiCtl.getCarOwner();
      isLoading.value = false;
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> getCarCatalog() async {
    try {
      print("getCarCatalog");
      final responseCarCatalog = await AppApi.callAPIjwt("GET", AppUrl.getCatalogs, {});
      // print(responseCarCatalog);
      if(responseCarCatalog['status'] == 200){
        carCatalogList = CarCatalogList.fromJson(responseCarCatalog["result"]);
        // print("carCatalogList");
        // print(responseCarCatalog["result"][0]['catalog_title']);
        // print(carCatalogList.data![0].catalogTitle);
      } else {
        carCatalogList = CarCatalogList(data: []);
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getFeelWell =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getURL() async {
    try{
      var res = await FireStore.getPictureURL();
      url.value = res[0]["URL_IMG"];
      urlEvent.value = res[0]["URL_EVENT"];
    }catch (e){
      if (kDebugMode) {
        print(e);
      }
    }
  }

}