import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/tutorial.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';



class TutorialController extends GetxController {
  RxBool showTutorial = false.obs;
  RxInt currentTutorialStep = 0.obs;
  final profileCtl = Get.put(ProfileController());
  final getCarOnwerCtl = Get.put(PsiController());
  ScrollController profileScrollController = ScrollController();
  bool _isProcessing = false;
  var tutorialStatus = <String, bool>{}.obs;
  var messageStore = <String, List<Map<String, dynamic>>>{}.obs;
  RxString tutorialType = "home".obs;
  RxString homeStatus = "not_login".obs;
  final RxDouble holeTop = 0.0.obs;
  final RxDouble holeLeft = 0.0.obs;
  final RxDouble holeWidth = 0.0.obs;
  final RxDouble holeHeight = 0.0.obs;
  final GetStorage _storage = GetStorage();
  RxString deviceBrand = ''.obs;
  final RxBool homeTutorialFinished = false.obs;


  bool hasNotch(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    return padding.top > 0;
  }

  bool hasNavigationBar(BuildContext context) {
    final viewPadding = WidgetsBinding.instance.window.viewPadding;
    final viewInsets = MediaQuery.of(context).viewInsets;
    return viewPadding.bottom / WidgetsBinding.instance.window.devicePixelRatio > 0 || viewInsets.bottom > 0;
  }

  RxList homeTutorialSteps = [
    {
      'holeTop': 0.260,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.260,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.140,
      'holeLeft': 0.180,
      'holeWidth': 0.780,
      'holeHeight': 0.079,
    },
    {
      'holeTop': 0.880,
      'holeLeft': 0.427,
      'holeWidth': 0.376,
      'holeHeight': 0.086,
    },
    {
      'holeTop': 0.880,
      'holeLeft': 0.773,
      'holeWidth': 0.168,
      'holeHeight': 0.086,
    },
    {
      'holeTop': 0.070,
      'holeLeft': 0.700,
      'holeWidth': 0.256,
      'holeHeight': 0.058,
    }
  ].obs;

  RxList homeNotLoginTutorialSteps = [
    {
      'holeTop': 0.0,
      'holeLeft': 0.0,
      'holeWidth': 0.0,
      'holeHeight': 0.0,
    },
    {
      'holeTop': 0.180,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.180,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.030,
      'holeLeft': 0.660,
      'holeWidth': 0.300,
      'holeHeight': 0.058,
    }
  ].obs;

  RxList homeNoCarTutorialSteps = [
    {
      'holeTop': 0.260,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.260,
      'holeLeft': 0.093,
      'holeWidth': 0.813,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.140,
      'holeLeft': 0.180,
      'holeWidth': 0.780,
      'holeHeight': 0.079,
    },
    {
      'holeTop': 0.880,
      'holeLeft': 0.330,
      'holeWidth': 0.376,
      'holeHeight': 0.086,
    },
    {
      'holeTop': 0.880,
      'holeLeft': 0.773,
      'holeWidth': 0.168,
      'holeHeight': 0.086,
    },
    {
      'holeTop': 0.070,
      'holeLeft': 0.700,
      'holeWidth': 0.256,
      'holeHeight': 0.058,
    }
  ].obs;

  RxList homeMessageTutorial = [
    {
      'message': 'คุณสามารถค้นหางานบริการของ',
      'message1': '\nISUZU ประชากิจฯ ',
      'message2': 'ได้ที่เมนูเหล่านี้',
      'message3': '',
    },
    {
      'message': 'ตัวอย่างเมนูแนะนำของเรา',
      'message1': '',
      'message2': '\nจองคิวล่วงหน้า,\nแจ้งรถเสียฉุกเฉินกับเราได้ทุกที่,\nบริการซ่อมรถถึงบ้านโดยไม่ต้องมาที่ศูนย์',
      'message3': '',
    },
    {
      'message': 'พี่ ',
      'message1': '',
      'message2': ' สามารถตรวจสอบ\nPMSPoint ที่มีได้ที่นี่',
      'message3': '',
    },
    {
      'message': 'สามารถดูกิจกรรมและข่าวสารล่าสุด\nได้ที่เมนูหลักด้านล่างนี้ได้ทันที',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'สามารถดูประวัติการใช้งานย้อนหลัง\nและคะแนนสะสมพอยท์ได้ที่รูปกระดิ่ง',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'และยังสามารถแก้ไขข้อมูลส่วนตัวอื่นๆ\nได้ที่เมนูโปรไฟล์',
      'message1': '',
      'message2': '',
      'message3': '',
    }
  ].obs;

  RxList homeNotLoginMessageTutorial = [
    {
      'message': 'สวัสดีครับผมชื่อ ',
      'message1': 'น้องช่าง ',
      'message2': ' ผู้ช่วยแนะนำ\nการใช้งาน App PMS นะครับ',
      'message3': '',
      'img':"asstets/image/tutorial/serviceHome.png",
    },
    {
      'message': 'คุณสามารถใช้งานบริการของ ',
      'message1': '\nISUZU ประชากิจฯ ',
      'message2': 'ได้ที่เมนูเหล่านี้',
      'message3': '',
      'img':"asstets/image/tutorial/serviceHome.png",
    },
    {
      'message': 'ตัวอย่างเมนูแนะนำของเรา',
      'message1': '',
      'message2': '\nจองคิวล่วงหน้า,\nแจ้งรถเสียฉุกเฉินกับเราได้ทุกที่,\nบริการซ่อมรถถึงบ้านโดยไม่ต้องมาที่ศูนย์',
      'message3': '',
       'img':"asstets/image/tutorial/serviceHome.png",
    },
    {
      'message': 'คุณสามารถรับบริการจากเราได้ทั้งหมด\nเพียงสมัครสมาชิกหรือเข้าสู่ระบบ',
      'message1': '',
      'message2': '',
      'message3': '',
      'img':"asstets/image/tutorial/serviceHome.png",
    }
  ].obs;

  RxList homeNoCarMessageTutorial = [
    {
      'message': 'คุณสามารถค้นหางานบริการของ',
      'message1': '\nISUZU ประชากิจฯ ',
      'message2': ' ได้ที่เมนูเหล่านี้',
      'message3': '',
    },
    {
      'message': 'ตัวอย่างเมนูแนะนำของเรา',
      'message1': '',
      'message2': '\nจองคิวล่วงหน้า,\nแจ้งรถเสียฉุกเฉินกับเราได้ทุกที่,\nบริการซ่อมรถถึงบ้านโดยไม่ต้องมาที่ศูนย์',
      'message3': '',
    },
    {
      'message': 'พี่ ',
      'message1': '',
      'message2': ' สามารถตรวจสอบ\nPMSPoint ที่มีได้ที่นี่',
      'message3': '',
    },
    {
      'message': 'สามารถดูกิจกรรมและข่าวสารล่าสุด\nได้ที่เมนูหลักด้านล่างนีได้้ทันที',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'สามารถดูประวัติการใช้งานย้อนหลัง\nและคะแนนสะสมพอยท์ได้ที่รูปกระดิ่ง',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'และยังสามารถแก้ไขข้อมูลส่วนตัวอื่นๆ\nได้ที่เมนูโปรไฟล์',
      'message1': '',
      'message2': '',
      'message3': '',
    }
  ].obs;

  RxList profileTutorialSteps = [
    {
      'holeTop': 0.230,
      'holeLeft': 0.840,
      'holeWidth': 0.123,
      'holeHeight': 0.058,
    },
    {
      'holeTop': 0.750,
      'holeLeft': 0.050,
      'holeWidth': 0.900,
      'holeHeight': 0.068,
    }
  ].obs;

  RxList profileMessageTutorial = [
    {
      'message': 'สามารถแก้ไขและอัพเดตข้อมูลส่วนตัวได้ที่นี่',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'และยังสามารถเสนอแนะและติดต่อเราได้ที่นี่',
      'message1': '',
      'message2': '',
      'message3': '',
    }
  ].obs;

  RxList mrTutorialSteps = [
    {
      'holeTop': 0.300,
      'holeLeft': 0.072,
      'holeWidth': 0.864,
      'holeHeight': 0.062,
    },
    {
      'holeTop': 0.090,
      'holeLeft': 0.043,
      'holeWidth': 0.915,
      'holeHeight': 0.135,
    },
    {
      'holeTop': 0.370,
      'holeLeft': 0.072,
      'holeWidth': 0.864,
      'holeHeight': 0.074,
    },
    {
      'holeTop': 0.450,
      'holeLeft': 0.043,
      'holeWidth': 0.915,
      'holeHeight': 0.062,
    }
  ].obs;

  RxList mrMessageTutorial = [
    {
      'message': 'พี่สามารถตรวจสอบรหัสแนะนำเพื่อน\nได้ตรงนี้ เพื่อใช้แนะนำและรับพอยท์',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'สามารถตรวจสอบระดับของพี่ ',
      'message1': '',
      'message2': 'ได้ที่นี่',
      'message3': '',
    },
    {
      'message': 'สามารถตรวจสอบเพื่อนที่เคยแนะนำและพอยท์\nที่เคยได้รับได้ที่นี่',
      'message1': '',
      'message2': '',
      'message3': '',
    },
    {
      'message': 'มาแนะนำเพื่อนเพื่อเพิ่มระดับและรางวัลกันเลย!',
      'message1': '',
      'message2': '',
      'message3': '',
    }
  ].obs;

  final Map<String, Color> messageColors = {
    'message': Color(0xFF282828),
    'message1': Color(0xFFE98900),
    'message2': Color(0xFF282828),
    'message3': Color(0xFF666666),
  };

  final RxList<Map<String, dynamic>> displayedMessages =
      <Map<String, dynamic>>[].obs;
  final RxInt currentMessageType = 0.obs;
  final RxBool isAnimating = false.obs;
  final RxBool shouldPlayNext = true.obs;

  bool get hasMoreMessages {
    if (currentTutorialStep.value >= getCurrentTutorialMessages().length) {
      debugPrint(
          "No more messages: current step ${currentTutorialStep.value} exceeds message length");
      return false;
    }

    final step = getCurrentTutorialMessages()[currentTutorialStep.value];
    final types = ['message', 'message1', 'message2', 'message3'];

    for (int i = currentMessageType.value; i < types.length; i++) {
      final type = types[i];
      if (step[type] != null && step[type].toString().isNotEmpty) {
        return true;
      }
    }
    debugPrint("No more messages in current step");
    return false;
  }

  String get currentMessageTypeString {
    final types = ['message', 'message1', 'message2', 'message3'];
    final type = types[currentMessageType.value];
    return type;
  }

  String get currentMessage {
    if (currentTutorialStep.value >= getCurrentTutorialMessages().length) {
      debugPrint("No current message: step index out of bounds");
      return '';
    }

    final step = getCurrentTutorialMessages()[currentTutorialStep.value];
    final type = currentMessageTypeString;
    final message = step[type]?.toString() ?? '';
    return message;
  }

  Color get currentMessageColor {
    final color = messageColors[currentMessageTypeString] ?? Colors.black;
    return color;
  }

  void finishCurrentMessage(String fullText, Map<String, dynamic> step) {
    if (_isProcessing || !hasMoreMessages) {
      return;
    }
    _isProcessing = true;

    displayedMessages.add({
      'text': fullText,
      'step': step,
    });

    final types = ['message', 'message1', 'message2', 'message3'];
    bool foundNext = false;
    for (int i = currentMessageType.value + 1; i < types.length; i++) {
      final type = types[i];
      if (step[type] != null && step[type].toString().isNotEmpty) {
        currentMessageType.value = i;
        shouldPlayNext.value = true;
        foundNext = true;
        debugPrint("Moving to next message type: $type");
        break;
      }
    }

    if (!foundNext) {
      isAnimating.value = false;
      shouldPlayNext.value = false;
      debugPrint("No more messages in this step");
    }
    _isProcessing = false;
  }

  List<TextSpan> getCombinedMessageTextSpans(
      Map<String, dynamic> step, Map<String, Color> messageColors) {
    final spans = <TextSpan>[];
    final keys = ['message', 'message1', 'message2', 'message3'];

    for (var key in keys) {
      final text = step[key];
      if (text != null && text.toString().trim().isNotEmpty) {
        spans.add(TextSpan(
          text: text.toString(),
          style: TextStyle(
            fontSize: 14.0,
            fontWeight: FontWeight.w500,
            fontFamily: 'Prompt',
            color: messageColors[key] ?? Colors.black,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 2.0,
                color: Color(0xFF000000).withOpacity(0.2),
              ),
            ],
          ),
        ));
      }
    }
    return spans;
  }

  String extractPlainTextFromSpans(List<TextSpan> spans) {
    final text = spans.map((span) => span.text ?? '').join('');
    return text;
  }

  Map<String, dynamic> get currentStepData =>
      getCurrentTutorialMessages()[currentTutorialStep.value];

  void startAnimation() {
    if (isAnimating.value || !hasMoreMessages) {
      return;
    }

    currentMessageType.value = 0;
    final types = ['message', 'message1', 'message2', 'message3'];
    final step = getCurrentTutorialMessages()[currentTutorialStep.value];

    for (int i = 0; i < types.length; i++) {
      final type = types[i];
      if (step[type] != null && step[type].toString().isNotEmpty) {
        currentMessageType.value = i;
        debugPrint("Starting animation with message type: $type");
        break;
      }
    }

    isAnimating.value = true;
    shouldPlayNext.value = true;
  }

  void resetMessages() {
    displayedMessages.clear();
    currentMessageType.value = 0;
    isAnimating.value = false;
    shouldPlayNext.value = true;
    debugPrint("Messages reset");
  }

  void saveTutorialPlayed(String pageName) {
    final key = 'tutorial_played_$pageName';
    // if (pageName == "home" && homeStatus.value == "not_login") {
    //   debugPrint("⛔ Skipping save for home tutorial when not logged in");
    //   return; // ไม่เซฟเมื่อยังไม่ได้ล็อกอิน
    // }
    _storage.write(key, true);
    debugPrint("💾 Saved tutorial as played for $pageName");
  }

  bool hasPlayedTutorial(String pageName) {
    final key = 'tutorial_played_$pageName';
    return _storage.read(key) ?? false;
  }

  void clearAllTutorials() {
    final allKeys = _storage.getKeys().cast<String>().toList();
    final keysToRemove = allKeys.where((String key) => key.startsWith('tutorial_played_')).toList();

    for (final key in keysToRemove) {
      _storage.remove(key);
    }
    checkHomeStatus();
    debugPrint("🔄 Cleared all tutorial states");
  }

  void showTutorialPage(BuildContext context, String type,
      {ScrollController? controller}) {
    final alreadyPlayed = hasPlayedTutorial(type);
    if (alreadyPlayed) {
      debugPrint("⛔ Tutorial for '$type' has already been played. Skipping.");
      return;
    }

    tutorialType.value = type;
    currentTutorialStep.value = 0;
    displayedMessages.clear();

    if (controller != null) {
      debugPrint("Received ScrollController from profile page");
      profileScrollController = controller;
    } else {
      debugPrint("No ScrollController provided");
    }

    if (type == "home") {
      checkHomeStatus();
    }

    updateMessagesWithProfileData();
    startAnimation();
    markTutorialAsPlayedToday(type);

    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (context, animation, secondaryAnimation) {
        return const TutorialScreen();
      },
    ).then((_) {
      saveTutorialPlayed(type);
      resetMessages();
      isAnimating.value = false;
      showTutorial.value = false;
      debugPrint("✅ Tutorial dialog closed and marked as played for '$type'");
      if(type == "home"){
        homeTutorialFinished.value = true;
      }
    });

  }
  bool hasPlayedTutorialToday(String type) {
    final today = DateTime.now().toIso8601String().split('T').first;
    final lastPlayedDate = _storage.read('$type-lastPlayedDate') ?? '';
    return lastPlayedDate == today;
  }

  void markTutorialAsPlayedToday(String type) {
    final today = DateTime.now().toIso8601String().split('T').first;
    _storage.write('$type-lastPlayedDate', today);
  }

  void startTutorial(String type) {
    tutorialType.value = type;
    updateMessagesWithProfileData();
    showTutorial.value = true;
    currentTutorialStep.value = 0;
    resetMessages();
    startAnimation();
    debugPrint("Started tutorial for type: $type");
  }

  void scrollToPosition(double position,
      {Duration duration = const Duration(milliseconds: 500)}) {
    debugPrint("Attempting to scroll to position: $position");
    if (profileScrollController.hasClients) {
      debugPrint("ScrollController has clients, scrolling now");
      profileScrollController.animateTo(
        position,
        duration: duration,
        curve: Curves.easeInOut,
      );
    } else {
      debugPrint("ScrollController has no clients");
      Future.delayed(Duration(milliseconds: 200), () {
        if (profileScrollController.hasClients) {
          debugPrint("ScrollController now has clients, scrolling");
          profileScrollController.animateTo(
            position,
            duration: duration,
            curve: Curves.easeInOut,
          );
        } else {
          debugPrint("ScrollController still has no clients after delay");
        }
      });
    }
  }

  void nextTutorialStep() {
    if (_isProcessing) {
      debugPrint("Cannot proceed to next step: processing");
      return;
    }
    _isProcessing = true;
    debugPrint("Next step requested: Current step ${currentTutorialStep.value}");

    if (currentTutorialStep.value < getCurrentTutorialSteps().length - 1) {
      currentTutorialStep.value++;
      displayedMessages.clear();
      isAnimating.value = false;
      shouldPlayNext.value = true;
      currentMessageType.value = 0;
      startAnimation();
      debugPrint("Moved to next step: ${currentTutorialStep.value}");
    } else {
      showTutorial.value = false;
      Get.back();
      debugPrint("Tutorial completed, closing");
    }
    _isProcessing = false;
  }

  void updateMessagesWithProfileData() {
    try {
      final name = profileCtl.profile.value.firstname;
      if (name != null && name.isNotEmpty) {
        if (tutorialType.value == "home" && homeStatus.value == "has_car") {
          final step3 = Map<String, String>.from(homeMessageTutorial[2]);
          step3['message1'] = "$name";
          homeMessageTutorial[2] = step3;
          debugPrint("Updated homeMessageTutorial step 2 with name: $name");
        }

        if (tutorialType.value == "home" && homeStatus.value == "no_car") {
          final step3 = Map<String, String>.from(homeNoCarMessageTutorial[2]);
          step3['message1'] = "$name";
          homeNoCarMessageTutorial[2] = step3;
          debugPrint("Updated homeNoCarMessageTutorial step 2 with name: $name");
        }

        if (tutorialType.value == "mr") {
          final step2 = Map<String, String>.from(mrMessageTutorial[1]);
          step2['message1'] = "$name";
          mrMessageTutorial[1] = step2;
          debugPrint("Updated mrMessageTutorial step 1 with name: $name");
        }
      }
    } catch (e) {
      debugPrint('Error updating messages with profile data: $e');
    }
  }

  RxList getCurrentTutorialSteps() {
    switch (tutorialType.value) {
      case "profile":
        return profileTutorialSteps;
      case "mr":
        return mrTutorialSteps;
      case "home":
        switch (homeStatus.value) {
          case "not_login":
            return homeNotLoginTutorialSteps;
          case "no_car":
            return homeNoCarTutorialSteps;
          case "has_car":
            return homeTutorialSteps;
          default:
            return homeNotLoginTutorialSteps;
        }
      default:
        return homeNotLoginTutorialSteps;
    }
  }

  RxList getCurrentTutorialMessages() {
    switch (tutorialType.value) {
      case "profile":
        return profileMessageTutorial;
      case "mr":
        return mrMessageTutorial;
      case "home":
        switch (homeStatus.value) {
          case "not_login":
            return homeNotLoginMessageTutorial;
          case "no_car":
            return homeNoCarMessageTutorial;
          case "has_car":
            return homeMessageTutorial;
          default:
            return homeNotLoginMessageTutorial;
        }
      default:
        return homeNotLoginMessageTutorial;
    }
  }

  Map<String, dynamic> getCurrentStep() {
    return getCurrentTutorialSteps()[currentTutorialStep.value];
  }

  void checkHomeStatus() {
    // debugPrint("Checking home status");
    // debugPrint("Current token: ${profileCtl.token.value}");

    if (profileCtl.token.value == null || profileCtl.token.value!.isEmpty) {
      homeStatus.value = "not_login";
      debugPrint("Tutorial: User not logged in");
      return;
    }

    debugPrint("Tutorial: User is logged in");

    try {
      debugPrint('try checkHomeStatus');
      if (getCarOnwerCtl.carOwnerInfo != null &&
          getCarOnwerCtl.carOwnerInfo.carList != null) {
        debugPrint(
            "Tutorial debug - carList length: ${getCarOnwerCtl.carOwnerInfo.carList!.length}");

        if (getCarOnwerCtl.carOwnerInfo.carList!.isNotEmpty) {
          homeStatus.value = "has_car";
          debugPrint("Tutorial: User has car - carList is not empty");
        } else {
          homeStatus.value = "no_car";
          debugPrint("Tutorial: User has no car - carList is empty");
        }
      } else {
        homeStatus.value = "no_car";
        debugPrint("Tutorial: User has no car - carOwnerInfo or carList is null");
      }
      debugPrint('try checkHomeStatus end');
    } catch (e) {
      debugPrint("Error in checkHomeStatus: $e");
      homeStatus.value = "no_car";
    }

    debugPrint("Final home status: ${homeStatus.value}");
  }

  @override
  void onInit() {
    super.onInit();
    isAnimating.value = true;
    shouldPlayNext.value = true;
    updateMessagesWithProfileData();
    checkHomeStatus();

    ever(profileCtl.profile, (_) {
      updateMessagesWithProfileData();
      checkHomeStatus();
    });

    ever(profileCtl.token, (_) {
      checkHomeStatus();
    });

    ever(getCarOnwerCtl.carOwnerInfo.obs, (_) {
      checkHomeStatus();
    });
    debugPrint("TutorialController initialized");
  }

  double getTopPadding(String type, int step, BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    final ctl = Get.find<TutorialController>(); // อ้างอิงตัวเองเพื่อเข้าถึง homeStatus

    if (type == "home") {
      switch (ctl.homeStatus.value) {
        case "not_login":
          switch (step) {
            case 0:
              return 21.h ;
            case 1:
              return 21.h ;
            case 2:
              return 24.h;
            case 3:
              return 21.h ; // ปรับตามความเหมาะสมสำหรับ not_login
            default:
              return 21.h ;
          }
        case "no_car":
          switch (step) {
            case 0:
              return 21.h  ; // เพิ่ม padding สำหรับ no_car
            case 1:
              return  21.h ;
            case 2:
              return 21.h ;
            case 3:
              return 21.h ; // ปรับตามความเหมาะสมสำหรับ no_car
            default:
              return 21.h ;
          }
        case "has_car":
          switch (step) {
            case 0:
              return 21.h ;
            case 1:
              return 24.h ;
            case 2:
              return 25.h ;
            case 3:
              return 21.h ; // ปรับตามความเหมาะสมสำหรับ has_car
            default:
              return 21.h ;
          }
        default:
          return 21.h ;
      }
    } else if (type == "mr") {
      switch (step) {
        case 0:
          return 21.h ;
        case 1:
          return 24.h ;
        default:
          return 21.h ;
      }
    } else if (type == "profile") {
      switch (step) {
        case 0:
          return 24.h;
        case 1:
          return 21.h ;
        default:
          return 21.h ;
      }
    }
    return topPadding;
  }
}