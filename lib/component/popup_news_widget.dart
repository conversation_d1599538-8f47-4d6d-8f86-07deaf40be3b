import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/popup_news_controller.dart';
import 'package:mapp_prachakij_v3/model/popup_news_model.dart';

class PopupNewsWidget extends StatelessWidget {
  final PopupNews popupNews;
  final VoidCallback onClose;
  final VoidCallback? onNext;

  const PopupNewsWidget({
    Key? key,
    required this.popupNews,
    required this.onClose,
    this.onNext,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // คำนวณขนาดตาม UI design - ใช้อัตราส่วน 330:500
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    // กำหนดขนาดตาม design (330x500) แต่ปรับตามหน้าจอ
    double popupWidth = screenWidth * 0.8; // 80% ของหน้าจอ
    double maxPopupWidth = 330; // ขนาดสูงสุดตาม design
    if (popupWidth > maxPopupWidth) {
      popupWidth = maxPopupWidth;
    }

    // คำนวณความสูงตามอัตราส่วน 330:500
    double popupHeight = (popupWidth / 330) * 500;

    // จำกัดความสูงไม่ให้เกิน 85% ของหน้าจอ
    double maxAllowedHeight = screenHeight * 0.85;
    if (popupHeight > maxAllowedHeight) {
      popupHeight = maxAllowedHeight;
      popupWidth = (popupHeight / 500) * 330; // ปรับความกว้างตามสัดส่วน
    }

    return Scaffold(
      backgroundColor: Color(0xFF1A1818).withOpacity(0.5),
      body: Center(
        child: Container(
          height: screenHeight * 0.6,
          // color: Colors.white.withOpacity(0.3),
          padding: EdgeInsets.only(left: 32, right: 32),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: InkWell(
                  onTap: onClose,
                  child: Container(
                    width: 34,
                    height: 34,
                    decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.7),
                        shape: BoxShape.circle,
                        border: Border.all(
                            width: 0.5,
                            color: Color(0xFF1A1818).withOpacity(0.2))),
                    child: const Icon(
                      Icons.close,
                      size: 18,
                      color: Color(0xFF000000),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 68,
              ),
              Expanded(
                flex: 7, // 70% ของพื้นที่สำหรับรูป
                child: Container(
                  width: double.infinity,
                  child: popupNews.imageUrl?.isNotEmpty == true
                      ? ClipRRect(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12)),
                          child: CachedNetworkImage(
                            imageUrl: popupNews.imageUrl!,
                            width: double.infinity,
                            height: double.infinity,
                            fit: BoxFit.cover, // ให้รูปเต็มพื้นที่
                            placeholder: (context, url) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Color(0xFFFFB100),
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[200],
                              child: const Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                  size: 50,
                                ),
                              ),
                            ),
                          ),
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: const Center(
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                              size: 50,
                            ),
                          ),
                        ),
                ),
              ),
              SizedBox(
                height: 16,
              ),
              //  // Bottom section with button
              Container(
                height: 80, // ความสูงคงที่สำหรับส่วนล่าง
                padding: const EdgeInsets.all(16),
                child: Center(
                  child: InkWell(
                    onTap: () => _handleAction(context),
                    child: Container(
                      width: 120, // ตาม design
                      height: 52, // ตาม design
                      decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFFC700),
                              Color(0xFFFFB100),
                              Color(0xFFFF9900)
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          borderRadius: BorderRadius.circular(20), // มุมมน
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.5),
                              blurRadius: 2,
                              offset: const Offset(0, 2),
                            ),
                          ],
                          border:
                              Border.all(width: 1, color: Color(0xFF282828))),
                      child: Center(
                        child: Text(
                          _getActionButtonText(),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    //  Material(
    //   color: Colors.transparent,
    //   child: Container(
    //     width: double.infinity,
    //     height: double.infinity,
    //     color: Colors.black.withOpacity(0.5),
    //     child: Center(
    //       child: Container(
    //         width: popupWidth,
    //         height: popupHeight,
    //         decoration: BoxDecoration(
    //           // color: Colors.white,
    //           borderRadius: BorderRadius.circular(16),
    //           boxShadow: [
    //             BoxShadow(
    //               color: Colors.black.withOpacity(0.3),
    //               blurRadius: 15,
    //               offset: const Offset(0, 8),
    //             ),
    //           ],
    //         ),
    //         child: Stack(
    //           children: [
    //             // Main content area
    //             ClipRRect(
    //               borderRadius: BorderRadius.circular(16),
    //               child: Column(
    //                 children: [
    //                   // Image area - ใช้พื้นที่ส่วนใหญ่
    //                   Expanded(
    //                     flex: 7, // 70% ของพื้นที่สำหรับรูป
    //                     child: Container(
    //                       width: double.infinity,
    //                       child: popupNews.imageUrl?.isNotEmpty == true
    //                           ? ClipRRect(
    //                               borderRadius: const BorderRadius.only(
    //                                 topLeft: Radius.circular(16),
    //                                 topRight: Radius.circular(16),
    //                               ),
    //                               child: CachedNetworkImage(
    //                                 imageUrl: popupNews.imageUrl!,
    //                                 width: double.infinity,
    //                                 height: double.infinity,
    //                                 fit: BoxFit.cover, // ให้รูปเต็มพื้นที่
    //                                 placeholder: (context, url) => Container(
    //                                   color: Colors.grey[200],
    //                                   child: const Center(
    //                                     child: CircularProgressIndicator(
    //                                       color: Color(0xFFFFB100),
    //                                     ),
    //                                   ),
    //                                 ),
    //                                 errorWidget: (context, url, error) =>
    //                                     Container(
    //                                   color: Colors.grey[200],
    //                                   child: const Center(
    //                                     child: Icon(
    //                                       Icons.image_not_supported,
    //                                       color: Colors.grey,
    //                                       size: 50,
    //                                     ),
    //                                   ),
    //                                 ),
    //                               ),
    //                             )
    //                           : Container(
    //                               color: Colors.grey[200],
    //                               child: const Center(
    //                                 child: Icon(
    //                                   Icons.image_not_supported,
    //                                   color: Colors.grey,
    //                                   size: 50,
    //                                 ),
    //                               ),
    //                             ),
    //                     ),
    //                   ),

    //                   // Bottom section with button
    //                   Container(
    //                     height: 80, // ความสูงคงที่สำหรับส่วนล่าง
    //                     padding: const EdgeInsets.all(16),
    //                     child: Center(
    //                       child: InkWell(
    //                         onTap: () => _handleAction(context),
    //                         child: Container(
    //                           width: 120, // ตาม design
    //                           height: 52, // ตาม design
    //                           decoration: BoxDecoration(
    //                             gradient: const LinearGradient(
    //                               colors: [
    //                                 Color(0xFFFFB200),
    //                                 Color(0xFFFF8C00)
    //                               ],
    //                               begin: Alignment.topCenter,
    //                               end: Alignment.bottomCenter,
    //                             ),
    //                             borderRadius:
    //                                 BorderRadius.circular(26), // มุมมน
    //                             boxShadow: [
    //                               BoxShadow(
    //                                 color: Colors.orange.withOpacity(0.3),
    //                                 blurRadius: 8,
    //                                 offset: const Offset(0, 4),
    //                               ),
    //                             ],
    //                           ),
    //                           child: Center(
    //                             child: Text(
    //                               _getActionButtonText(),
    //                               style: const TextStyle(
    //                                 fontSize: 14,
    //                                 fontWeight: FontWeight.w600,
    //                                 color: Colors.white,
    //                               ),
    //                             ),
    //                           ),
    //                         ),
    //                       ),
    //                     ),
    //                   ),
    //                 ],
    //               ),
    //             ),

    //             // Close button - วงกลมเล็กๆ มุมบนขวา
    //             Positioned(
    //               top: 12,
    //               right: 12,
    //               child: InkWell(
    //                 onTap: onClose,
    //                 child: Container(
    //                   width: 32,
    //                   height: 32,
    //                   decoration: BoxDecoration(
    //                     color: Colors.white.withOpacity(0.9),
    //                     shape: BoxShape.circle,
    //                     boxShadow: [
    //                       BoxShadow(
    //                         color: Colors.black.withOpacity(0.2),
    //                         blurRadius: 4,
    //                         offset: const Offset(0, 2),
    //                       ),
    //                     ],
    //                   ),
    //                   child: const Icon(
    //                     Icons.close,
    //                     size: 18,
    //                     color: Color(0xFF666666),
    //                   ),
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ),
    //     ),
    //   ),
    // );
  }

  String _getActionButtonText() {
    if (popupNews.buttonText?.isNotEmpty == true) {
      return popupNews.buttonText!;
    }

    switch (popupNews.actionType) {
      case 'external':
        return 'เปิดลิงก์';
      case 'internal':
        return 'ดูรายละเอียด';
      default:
        return 'ดูเพิ่มเติม';
    }
  }

  void _handleAction(BuildContext context) {
    switch (popupNews.actionType) {
      case 'external':
        _launchURL(popupNews.actionValue ?? '');
        break;
      case 'internal':
        _navigateToPage(popupNews.actionValue ?? '');
        break;
      default:
        onClose();
        break;
    }
  }

  void _launchURL(String url) async {
    if (url.isNotEmpty) {
      try {
        final Uri uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      } catch (e) {
        print('Error launching URL: $e');
      }
    }
    onClose();
  }

  void _navigateToPage(String route) {
    if (route.isNotEmpty) {
      try {
        // Handle internal navigation based on route
        switch (route) {
          case '/news':
            Get.toNamed('/news');
            break;
          case '/promotion':
            Get.toNamed('/promotion');
            break;
          case '/service':
            Get.toNamed('/service');
            break;
          case '/profile':
            Get.toNamed('/profile');
            break;
          default:
            // Try to navigate to the route directly
            Get.toNamed(route);
            break;
        }
      } catch (e) {
        print('Error navigating to route: $e');
      }
    }
    onClose();
  }
}

// Pop-up overlay widget สำหรับแสดงบนหน้าจอ
class PopupNewsOverlay extends StatelessWidget {
  const PopupNewsOverlay({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PopupNewsController controller = Get.find<PopupNewsController>();

    return Obx(() {
      if (!controller.shouldShowPopup.value) {
        return const SizedBox.shrink();
      }

      final PopupNews? currentPopup = controller.currentPopup;
      if (currentPopup == null) {
        return const SizedBox.shrink();
      }

      final bool hasMultiplePopups = controller.popupNewsList.value
              .getActivePopupsForUser(controller.currentUserGroup.value)
              .length >
          1;
      final bool isLastPopup = controller.currentPopupIndex.value >=
          controller.popupNewsList.value
                  .getActivePopupsForUser(controller.currentUserGroup.value)
                  .length -
              1;

      return PopupNewsWidget(
        popupNews: currentPopup,
        onClose: () => controller.closePopup(),
        onNext: hasMultiplePopups && !isLastPopup
            ? () => controller.nextPopup()
            : null,
      );
    });
  }
}
