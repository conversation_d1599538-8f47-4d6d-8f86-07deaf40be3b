import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/popup_news_test_service_v2.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/popup_news_controller.dart';

class PopupTestingWidget extends StatelessWidget {
  const PopupTestingWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('🧪 Popup News Testing'),
        backgroundColor: Colors.orange,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      '🎯 Popup News Testing Center',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'ทดสอบระบบ popup news สำหรับกลุ่มผู้ใช้ต่างๆ',
                      style: TextStyle(color: Colors.blue.shade600),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Quick Tests Section
            _buildSection(
              '⚡ Quick Tests',
              [
                _buildTestButton(
                  'Test MR Group',
                  Colors.orange,
                  () => PopupNewsTestService.quickTestMR(),
                ),
                _buildTestButton(
                  'Test MA Group',
                  Colors.green,
                  () => PopupNewsTestService.quickTestMA(),
                ),
                _buildTestButton(
                  'Test SS Group',
                  Colors.purple,
                  () => PopupNewsTestService.quickTestSS(),
                ),
                _buildTestButton(
                  'Test General',
                  Colors.blue,
                  () => PopupNewsTestService.quickTestGeneral(),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Advanced Tests Section
            _buildSection(
              '🔬 Advanced Tests',
              [
                _buildTestButton(
                  'Test Aspect Ratios',
                  Colors.teal,
                  () {
                    PopupNewsTestService.testDifferentAspectRatios();
                    PopupNewsTestService.testPopupForUserGroup('general');
                  },
                ),
                _buildTestButton(
                  'Test Multi-Target Groups',
                  Colors.indigo,
                  () {
                    PopupNewsTestService.testMultiTargetGroups();
                    PopupNewsTestService.showCurrentStatus();
                  },
                ),
                _buildTestButton(
                  'Test Expired Popups',
                  Colors.red,
                  () {
                    PopupNewsTestService.testExpiredPopups();
                    PopupNewsTestService.testPopupForUserGroup('general');
                  },
                ),
                _buildTestButton(
                  'Run Full Test',
                  Colors.brown,
                  () => PopupNewsTestService.runFullTest(),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Status & Control Section
            _buildSection(
              '📊 Status & Control',
              [
                _buildTestButton(
                  'Show Current Status',
                  Colors.cyan,
                  () => PopupNewsTestService.showCurrentStatus(),
                ),
                _buildTestButton(
                  'Check Stored Data',
                  Colors.amber,
                  () => PopupNewsTestService.checkStoredData(),
                ),
                _buildTestButton(
                  'Clear Shown Popups',
                  Colors.orange.shade300,
                  () => PopupNewsTestService.clearShownPopups(),
                ),
                _buildTestButton(
                  'Clear All Test Data',
                  Colors.red.shade300,
                  () => PopupNewsTestService.clearAllTestData(),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Manual User Group Testing
            _buildSection(
              '👤 Manual User Group Testing',
              [
                _buildUserGroupRow('MR', 'mr'),
                _buildUserGroupRow('MA', 'ma'),
                _buildUserGroupRow('SS', 'ss'),
                _buildUserGroupRow('General', 'general'),
              ],
            ),

            SizedBox(height: 16),

            // Current Status Display
            GetBuilder<PopupNewsController>(
              builder: (controller) {
                return Card(
                  color: Colors.grey.shade50,
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '📋 Current Status',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        _buildStatusRow(
                            'User Group', controller.currentUserGroup.value),
                        _buildStatusRow(
                            'Loading', controller.isLoading.value.toString()),
                        _buildStatusRow('Should Show Popup',
                            controller.shouldShowPopup.value.toString()),
                        _buildStatusRow(
                            'Total Popups',
                            (controller.popupNewsList.value.data?.length ?? 0)
                                .toString()),
                        _buildStatusRow(
                            'Active for User',
                            controller.popupNewsList.value
                                .getActivePopupsForUser(
                                    controller.currentUserGroup.value)
                                .length
                                .toString()),
                      ],
                    ),
                  ),
                );
              },
            ),

            SizedBox(height: 32),
          ],
        ),
      ),

      // Floating Action Button for Quick Access
      floatingActionButton: kDebugMode
          ? FloatingActionButton.extended(
              onPressed: () {
                // Quick add test data and show popup
                PopupNewsTestService.addTestPopupNews();
                PopupNewsTestService.testPopupForUserGroup('mr');
              },
              backgroundColor: Colors.orange,
              label: Text('Quick Test'),
              icon: Icon(Icons.play_arrow),
            )
          : null,
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildTestButton(String text, Color color, VoidCallback onPressed) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(text),
      ),
    );
  }

  Widget _buildUserGroupRow(String displayName, String groupValue) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '$displayName:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: ElevatedButton(
              onPressed: () {
                // Change user group and test
                final controller = Get.find<PopupNewsController>();
                controller.currentUserGroup.value = groupValue;
                PopupNewsTestService.addTestPopupNews();
                PopupNewsTestService.testPopupForUserGroup(groupValue);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade100,
                foregroundColor: Colors.blue.shade800,
                padding: EdgeInsets.symmetric(vertical: 8),
              ),
              child: Text('Test $displayName'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(color: Colors.blue.shade800),
            ),
          ),
        ],
      ),
    );
  }
}
