import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/popup_news_controller.dart';
import 'package:mapp_prachakij_v3/model/popup_news_model.dart';

class PopupNewsTestService extends GetxController {
  static PopupNewsTestService get to => Get.find();

  // สำหรับทดสอบ: สร้างข้อมูล popup news ตัวอย่าง
  static void addTestPopupNews([List<PopupNews>? customPopups]) {
    final PopupNewsController controller = Get.find<PopupNewsController>();

    List<PopupNews> testPopups = customPopups ??
        [
          // Popup สำหรับลูกค้าทั่วไป
          PopupNews(
            id: 1,
            title: "ยินดีต้อนรับสู่แอปพลิเคชันใหม่!",
            content:
                "สวัสดีครับ! ขอบคุณที่เลือกใช้แอปพลิเคชันของเรา เรามีบริการใหม่ๆ มากมายที่พร้อมให้บริการคุณ",
            imageUrl:
                "https://via.placeholder.com/400x200/FFB200/ffffff?text=Welcome",
            actionType: "internal",
            actionValue: "/news",
            buttonText: "ดูข่าวสาร",
            isActive: true,
            createdDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 30)),
            targetGroups: ['general'],
            aspectRatio: 16.0 / 9.0, // Wide format
            widthPercent: 0.9,
          ),
          // Popup สำหรับ MR
          PopupNews(
            id: 2,
            title: "ข้อมูลใหม่สำหรับ MR",
            content: "มีรายงานยอดขายและคอมมิชชันใหม่เข้ามา กรุณาตรวจสอบในระบบ",
            imageUrl:
                "https://via.placeholder.com/300x400/FF8C00/ffffff?text=MR+Update",
            actionType: "internal",
            actionValue: "/mr-dashboard",
            buttonText: "ดูรายงาน",
            isActive: true,
            createdDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 7)),
            targetGroups: ['mr'],
            aspectRatio: 3.0 / 4.0, // Portrait format
            widthPercent: 0.8,
          ),
          // Popup สำหรับ MA
          PopupNews(
            id: 3,
            title: "ประชุมผู้จัดการประจำเดือน",
            content:
                "เชิญเข้าร่วมประชุมผู้จัดการประจำเดือน วันที่ 15 เวลา 14:00 น.",
            imageUrl: null,
            actionType: "external",
            actionValue: "https://zoom.us/meeting",
            buttonText: "เข้าร่วมประชุม",
            isActive: true,
            createdDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 3)),
            targetGroups: ['ma'],
            aspectRatio: 4.0 / 3.0, // Standard format
            widthPercent: 0.85,
          ),
          // Popup สำหรับ SS
          PopupNews(
            id: 4,
            title: "รายงานยอดขายทีม",
            content:
                "ตรวจสอบผลประกอบการของทีมในเดือนนี้ และวางแผนกลยุทธ์เดือนหน้า",
            imageUrl:
                "https://via.placeholder.com/400x300/28a745/ffffff?text=SS+Report",
            actionType: "internal",
            actionValue: "/ss-reports",
            buttonText: "ดูรายงาน",
            isActive: true,
            createdDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 5)),
            targetGroups: ['ss'],
            aspectRatio: 4.0 / 3.0,
            widthPercent: 0.9,
          ),
          // Popup สำหรับทุกกลุ่ม
          PopupNews(
            id: 5,
            title: "การอัพเดทระบบ",
            content:
                "ระบบจะมีการปรับปรุงในวันเสาร์ที่ 10 เวลา 02:00-06:00 น. อาจมีการหยุดให้บริการชั่วคราว",
            imageUrl: null,
            actionType: "none",
            actionValue: "",
            buttonText: "เข้าใจแล้ว",
            isActive: true,
            createdDate: DateTime.now(),
            expiryDate: DateTime.now().add(const Duration(days: 2)),
            targetGroups: ['all'], // แสดงกับทุกกลุ่ม
            aspectRatio: 16.0 / 9.0,
            widthPercent: 0.95,
          ),
        ];

    // รวมข้อมูลเดิมกับข้อมูลใหม่
    List<PopupNews> existingPopups = controller.popupNewsList.value.data ?? [];
    List<PopupNews> allPopups = [...existingPopups, ...testPopups];

    controller.popupNewsList.value = PopupNewsList(data: allPopups);
    controller.checkAndShowPopup();

    print("=== TEST POPUP NEWS ADDED ===");
    print("Added ${testPopups.length} test popups");
    print("Total popups: ${allPopups.length}");
    print("=============================");
  }

  // ฟังก์ชันสำหรับทดสอบ navigation ภายในแอป
  static void testInternalNavigation(String route) {
    print("Testing internal navigation to: $route");

    switch (route) {
      case '/news':
        // Navigate to news page
        Get.toNamed('/news');
        break;
      case '/promotion':
        // Navigate to promotion page
        Get.toNamed('/promotion');
        break;
      case '/service':
        // Navigate to service page
        Get.toNamed('/service');
        break;
      case '/profile':
        // Navigate to profile page
        Get.toNamed('/profile');
        break;
      default:
        print("Unknown route: $route");
        break;
    }
  }

  // ทดสอบ popup สำหรับกลุ่มเฉพาะ
  static void testPopupForUserGroup(String userGroup) {
    final PopupNewsController controller = Get.find<PopupNewsController>();

    print("=== TESTING POPUP FOR USER GROUP: $userGroup ===");

    // เปลี่ยนกลุ่มผู้ใช้ชั่วคราว
    controller.currentUserGroup.value = userGroup;

    // รีเฟรชและแสดง popup
    controller.refreshPopupNews().then((_) {
      List<PopupNews> activePopups =
          controller.popupNewsList.value.getActivePopupsForUser(userGroup);

      print(
          "Found ${activePopups.length} active popups for user group: $userGroup");

      if (activePopups.isNotEmpty) {
        for (int i = 0; i < activePopups.length; i++) {
          print("Popup ${i + 1}: ${activePopups[i].title}");
          print("  - Target Groups: ${activePopups[i].targetGroups}");
          print("  - Aspect Ratio: ${activePopups[i].aspectRatio}");
          print("  - Width Percent: ${activePopups[i].widthPercent}");
        }

        // แสดง popup
        controller.shouldShowPopup.value = true;
        controller.currentPopupIndex.value = 0;
      } else {
        print("No active popups found for user group: $userGroup");
      }
    });

    print("===============================");
  }

  // รีเซ็ตและแสดงข้อมูลสถานะปัจจุบัน
  static void showCurrentStatus() {
    final PopupNewsController controller = Get.find<PopupNewsController>();

    print("=== Current Popup Status ===");
    print("User Group: ${controller.currentUserGroup.value}");
    print("Total Popups: ${controller.popupNewsList.value.data?.length ?? 0}");
    print(
        "Active Popups for User: ${controller.popupNewsList.value.getActivePopupsForUser(controller.currentUserGroup.value).length}");
    print("Should Show Popup: ${controller.shouldShowPopup.value}");
    print("Current Popup Index: ${controller.currentPopupIndex.value}");

    if (controller.currentPopup != null) {
      print("Current Popup: ${controller.currentPopup!.title}");
      print("Target Groups: ${controller.currentPopup!.targetGroups}");
      print("Aspect Ratio: ${controller.currentPopup!.aspectRatio}");
      print("Width Percent: ${controller.currentPopup!.widthPercent}");
    }
    print("============================");
  }

  // ทดสอบอัตราส่วนต่างๆ
  static void testDifferentAspectRatios() {
    List<PopupNews> testPopups = [
      PopupNews(
        id: 1001,
        title: "ทดสอบ 16:9 (Widescreen)",
        content: "นี่คือ popup ในอัตราส่วน 16:9 สำหรับการแสดงผลแบบ widescreen",
        aspectRatio: 16.0 / 9.0,
        widthPercent: 0.9,
        targetGroups: ['general'],
        buttonText: "ปิด",
        isActive: true,
        createdDate: DateTime.now(),
      ),
      PopupNews(
        id: 1002,
        title: "ทดสอบ 4:3 (Traditional)",
        content: "นี่คือ popup ในอัตราส่วน 4:3 สำหรับการแสดงผลแบบดั้งเดิม",
        aspectRatio: 4.0 / 3.0,
        widthPercent: 0.8,
        targetGroups: ['general'],
        buttonText: "ปิด",
        isActive: true,
        createdDate: DateTime.now(),
      ),
      PopupNews(
        id: 1003,
        title: "ทดสอบ 1:1 (Square)",
        content:
            "นี่คือ popup ในอัตราส่วน 1:1 สำหรับการแสดงผลแบบสี่เหลี่ยมจัตุรัส",
        aspectRatio: 1.0,
        widthPercent: 0.7,
        targetGroups: ['general'],
        buttonText: "ปิด",
        isActive: true,
        createdDate: DateTime.now(),
      ),
      PopupNews(
        id: 1004,
        title: "ทดสอบ 3:4 (Portrait)",
        content: "นี่คือ popup ในอัตราส่วน 3:4 สำหรับการแสดงผลแบบ portrait",
        aspectRatio: 3.0 / 4.0,
        widthPercent: 0.6,
        targetGroups: ['general'],
        buttonText: "ปิด",
        isActive: true,
        createdDate: DateTime.now(),
      ),
    ];

    addTestPopupNews(testPopups);
    print("=== ASPECT RATIO TEST POPUPS ADDED ===");
    print("Added 4 different aspect ratio test popups");
    print("Use testPopupForUserGroup('general') to test them");
    print("======================================");
  }

  // ทดสอบ multi-target groups
  static void testMultiTargetGroups() {
    List<PopupNews> testPopups = [
      PopupNews(
        id: 2001,
        title: "ข่าวสำหรับ MR และ MA",
        content: "นี่คือข่าวที่แสดงให้ทั้ง MR และ MA เห็น",
        targetGroups: ['mr', 'ma'],
        aspectRatio: 16.0 / 9.0,
        widthPercent: 0.85,
        buttonText: "รับทราบ",
        isActive: true,
        createdDate: DateTime.now(),
      ),
      PopupNews(
        id: 2002,
        title: "ข่าวสำหรับทุกกลุ่ม",
        content: "นี่คือข่าวที่แสดงให้ทุกกลุ่มเห็น",
        targetGroups: ['all'],
        aspectRatio: 4.0 / 3.0,
        widthPercent: 0.9,
        buttonText: "รับทราบ",
        isActive: true,
        createdDate: DateTime.now(),
      ),
      PopupNews(
        id: 2003,
        title: "ข่าวเฉพาะ SS",
        content: "นี่คือข่าวที่แสดงเฉพาะ SS เท่านั้น",
        targetGroups: ['ss'],
        aspectRatio: 1.0,
        widthPercent: 0.75,
        buttonText: "เข้าใจแล้ว",
        isActive: true,
        createdDate: DateTime.now(),
      ),
    ];

    addTestPopupNews(testPopups);
    print("=== MULTI-TARGET TEST POPUPS ADDED ===");
    print("Added 3 different target group test popups");
    print("Test with different user groups to see different results");
    print("======================================");
  }

  // ทดสอบ expired popups
  static void testExpiredPopups() {
    List<PopupNews> testPopups = [
      PopupNews(
        id: 3001,
        title: "ข่าวหมดอายุแล้ว",
        content: "นี่คือข่าวที่หมดอายุไปแล้ว ไม่ควรแสดง",
        targetGroups: ['general'],
        expiryDate: DateTime.now().subtract(Duration(days: 1)),
        isActive: true,
        createdDate: DateTime.now().subtract(Duration(days: 2)),
      ),
      PopupNews(
        id: 3002,
        title: "ข่าวใหม่ยังไม่หมดอายุ",
        content: "นี่คือข่าวที่ยังไม่หมดอายุ ควรแสดง",
        targetGroups: ['general'],
        expiryDate: DateTime.now().add(Duration(days: 7)),
        isActive: true,
        createdDate: DateTime.now(),
      ),
    ];

    addTestPopupNews(testPopups);
    print("=== EXPIRY TEST POPUPS ADDED ===");
    print("Added 1 expired and 1 active popup");
    print("Only the active one should show");
    print("================================");
  }

  // เคลียร์ข้อมูลที่แสดงไปแล้ว
  static Future<void> clearShownPopups() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('shown_popup_ids');
      await prefs.remove('popup_last_shown_date');

      print("=== CLEARED SHOWN POPUPS ===");
      print("All shown popup records cleared");
      print("You can now see popups again");
      print("============================");
    } catch (e) {
      print("Error clearing shown popups: $e");
    }
  }

  // เคลียร์ข้อมูลทดสอบทั้งหมด
  static void clearAllTestData() {
    final PopupNewsController controller = Get.find<PopupNewsController>();
    controller.popupNewsList.value = PopupNewsList(data: []);

    print("=== CLEARED ALL TEST DATA ===");
    print("All test popup data cleared");
    print("==============================");
  }

  // ตรวจสอบข้อมูลใน SharedPreferences
  static Future<void> checkStoredData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastShownDate = prefs.getString('popup_last_shown_date') ?? 'Never';
      final shownPopupIds = prefs.getStringList('shown_popup_ids') ?? [];

      print("=== STORED DATA CHECK ===");
      print("Last shown date: $lastShownDate");
      print("Shown popup IDs: $shownPopupIds");
      print("=========================");
    } catch (e) {
      print("Error checking stored data: $e");
    }
  }

  // Quick test สำหรับแต่ละกลุ่ม
  static void quickTestMR() {
    addTestPopupNews();
    testPopupForUserGroup('mr');
  }

  static void quickTestMA() {
    addTestPopupNews();
    testPopupForUserGroup('ma');
  }

  static void quickTestSS() {
    addTestPopupNews();
    testPopupForUserGroup('ss');
  }

  static void quickTestGeneral() {
    addTestPopupNews();
    testPopupForUserGroup('general');
  }

  // ทดสอบครบทุกกรณี
  static void runFullTest() {
    print("=== STARTING FULL POPUP TEST ===");

    // เคลียร์ข้อมูลเดิม
    clearAllTestData();
    clearShownPopups();

    // เพิ่มข้อมูลทดสอบ
    addTestPopupNews();
    testDifferentAspectRatios();
    testMultiTargetGroups();
    testExpiredPopups();

    print(
        "Full test data prepared. Use individual test functions to test each scenario.");
    print("================================");
  }
}
