class PopupNews {
  int? id;
  String? title;
  String? content;
  String? imageUrl;
  String? actionType; // 'internal', 'external', 'none'
  String? actionValue; // route path for internal, URL for external
  String? buttonText;
  bool? isActive;
  DateTime? createdDate;
  DateTime? expiryDate;
  List<String>? targetGroups; // ['general', 'mr', 'ma', 'ss']
  double? aspectRatio; // อัตราส่วนของ popup (width/height)
  double? widthPercent; // เปอร์เซ็นต์ความกว้างของหน้าจอ (0.0-1.0)

  PopupNews({
    this.id,
    this.title,
    this.content,
    this.imageUrl,
    this.actionType,
    this.actionValue,
    this.buttonText,
    this.isActive,
    this.createdDate,
    this.expiryDate,
    this.targetGroups,
    this.aspectRatio,
    this.widthPercent,
  });

  factory PopupNews.fromJson(Map<String, dynamic> json) => PopupNews(
        id: json["id"] ?? 0,
        title: json["title"] ?? "",
        content: json["content"] ?? "",
        imageUrl: json["image_url"] ?? "",
        actionType: json["action_type"] ?? "none",
        actionValue: json["action_value"] ?? "",
        buttonText: json["button_text"] ?? "",
        isActive: json["is_active"] ?? false,
        createdDate: json["created_date"] != null
            ? DateTime.parse(json["created_date"])
            : null,
        expiryDate: json["expiry_date"] != null
            ? DateTime.parse(json["expiry_date"])
            : null,
        targetGroups: json["target_groups"] != null
            ? List<String>.from(json["target_groups"])
            : ['general'], // default ให้แสดงกับลูกค้าทั่วไป
        aspectRatio: json["aspect_ratio"] != null
            ? json["aspect_ratio"].toDouble()
            : 4.0 / 3.0, // default ratio 4:3
        widthPercent: json["width_percent"] != null
            ? json["width_percent"].toDouble()
            : 0.9, // default 90% ของหน้าจอ
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "image_url": imageUrl,
        "action_type": actionType,
        "action_value": actionValue,
        "button_text": buttonText,
        "is_active": isActive,
        "created_date": createdDate?.toIso8601String(),
        "expiry_date": expiryDate?.toIso8601String(),
        "target_groups": targetGroups,
        "aspect_ratio": aspectRatio,
        "width_percent": widthPercent,
      };

  // ตรวจสอบว่า popup นี้ยังไม่หมดอายุหรือไม่
  bool get isValid {
    if (expiryDate == null) return true;
    return DateTime.now().isBefore(expiryDate!);
  }

  // ตรวจสอบว่า popup นี้เหมาะสมกับกลุ่มผู้ใช้หรือไม่
  bool isTargetForUserGroup(String userGroup) {
    if (targetGroups == null || targetGroups!.isEmpty) {
      return true; // ถ้าไม่ระบุกลุ่ม ให้แสดงกับทุกคน
    }
    return targetGroups!.contains(userGroup) || targetGroups!.contains('all');
  }

  // ตรวจสอบว่าควรแสดง popup นี้หรือไม่ (รวมกลุ่มผู้ใช้)
  bool shouldShowForUser(String userGroup) {
    return isActive == true && isValid && isTargetForUserGroup(userGroup);
  }

  // ตรวจสอบว่าควรแสดง popup นี้หรือไม่ (แบบเดิม - เพื่อความเข้ากันได้)
  bool get shouldShow {
    return isActive == true && isValid;
  }
}

class PopupNewsList {
  final List<PopupNews>? data;

  PopupNewsList({this.data});

  factory PopupNewsList.fromJson(List<dynamic> parsedJson) {
    List<PopupNews>? data = <PopupNews>[];
    data = parsedJson.map((i) => PopupNews.fromJson(i)).toList();
    return PopupNewsList(data: data);
  }

  // ได้ popup ที่ควรแสดงสำหรับกลุ่มผู้ใช้เฉพาะ
  List<PopupNews> getActivePopupsForUser(String userGroup) {
    if (data == null) return [];
    return data!.where((popup) => popup.shouldShowForUser(userGroup)).toList();
  }

  // ได้ popup ที่ควรแสดง (เฉพาะที่ active และยังไม่หมดอายุ - แบบเดิม)
  List<PopupNews> get activePopups {
    if (data == null) return [];
    return data!.where((popup) => popup.shouldShow).toList();
  }
}
