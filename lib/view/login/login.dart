import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/agreement_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/view/login/register.dart';
import 'package:mapp_prachakij_v3/view/login/verify.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {

  final centerCtl = Get.put(SettingController());
  final agreementCtl = Get.put(AgreementController());
  final loginCtl = Get.put(LoginController());
  final registerCtl = Get.put(RegisterController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "Login");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: InkWell(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: Stack(
          children: [
            // Background
            ClipRRect(
          borderRadius: BorderRadius.circular(10), // Optional for rounded corners
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // Adjust values
            child: Container(
              width: Get.width,
              height: Get.height,
              color: Colors.white.withOpacity(0.09), // Semi-transparent overlay
            ),
          ),
        ),
            Container(
              width: Get.width,
              height: Get.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  // stops: const [0.0, 0.5, 1.0],
                  colors: [
                    const Color(0xFFFFC700).withOpacity(0.7),
                    const Color(0xFFFFC700),
                    // const Color(0xFFFF9900).withOpacity(0.5),
                  ],
                ),
              ),
            ),
            Container(
              width: Get.width,
              height: Get.height,
              color: Colors.white.withOpacity(0.3),
            ),
            // ClipRRect(
            //   borderRadius: BorderRadius.circular(10), // Optional for rounded corners
            //   child: BackdropFilter(
            //     filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0), // Adjust values
            //     child: Container(
            //       width: Get.width,
            //       height: Get.height,
            //       color: Colors.white.withOpacity(0.4), // Semi-transparent overlay
            //     ),
            //   ),
            // ),
            // ClipRRect(
            //     child: BackdropFilter(
            //       filter: ImageFilter.blur(sigmaX: 30.0, sigmaY: 10.0),
            //       child: Container(
            //         width: Get.width,
            //         height: Get.height,
            //         decoration: BoxDecoration(
            //           gradient: LinearGradient(
            //             begin: Alignment.topCenter,
            //             end: Alignment.bottomCenter,
            //             stops: const [0.0, 0.5, 1.0],
            //             colors: [
            //               const Color(0xFFFFC700).withOpacity(0.8),
            //               const Color(0xFFFFB100),
            //               const Color(0xFFFF9900).withOpacity(0.8),
            //             ],
            //           ),
            //         ),
            //       ),
            //     )),
            Positioned(
                left: 30,
                top: 100,
                child: Image.asset(
                  'assets/image/logo.png',
                  width: 104,
                )),
            // Positioned(
            //     right: 30,
            //     top: 60,
            //     child:InkWell(
            //       onTap: () {
            //         if (loginCtl.page.value == "register") {
            //           loginCtl.page.value = "login"; // เปลี่ยนค่าเป็น login
            //           loginCtl.typeMenu.value = "login";
            //           print("🔄 เปลี่ยนเป็นหน้า Login: ${loginCtl.page.value}");
            //
            //           // ❌ ไม่ต้องใช้ Get.back(); เพราะ Obx จะเปลี่ยน UI ให้อัตโนมัติ
            //         } else {
            //           loginCtl.typeMenu.value = "login";
            //           loginCtl.page.value = "login";
            //           Get.back(); // ปิดหน้าถ้าไม่ได้อยู่ในหน้า Register
            //         }
            //       },
            //       child: Container(
            //         height: 34,
            //         width: 34,
            //         decoration: BoxDecoration(
            //           border: Border.all(color: Colors.white, width: 1),
            //           borderRadius: BorderRadius.circular(12),
            //         ),
            //         child: Center(
            //           child: Icon(
            //             Icons.close_rounded,
            //             color: Colors.white,
            //             size: 18,
            //           ),
            //         ),
            //       ),
            //     )
            // ),
            Obx(() {
              print("🔄 UI อัปเดต, loginCtl.page.value = ${loginCtl.page.value}");
              if(loginCtl.page.value == "register"){
                return  RegisterPage();
              } else if (loginCtl.page.value == "verify"){
                return const VerifyPage();
              } else {
                return buildLogin();
              }
            }),
          ],
        ),
      ),
    );
  }

  buildLogin(){
    return Stack(
      children: [
        Positioned(
            right: 30,
            top: 60,
            child:InkWell(
              onTap: () {
                  // loginCtl.typeMenu.value = "login";
                  // loginCtl.page.value = "login";
                  // Get.back(); // ปิดหน้าถ้าไม่ได้อยู่ในหน้า Register
              Get.close(1);
              },
              child: Container(
                height: 34,
                width: 34,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Icon(
                    Icons.close_rounded,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            )
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            width: Get.width,
            height: Get.height * 0.57,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25), topRight: Radius.circular(25)),
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0.0, 0.5, 1.0],
                colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFFFFFFF),
                  Color(0xFFECECEC),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF664701).withOpacity(1),
                  offset: const Offset(0, -2), // changes position of shadow
                ),
              ],
            ),
            padding: const EdgeInsets.only(
              top: 30,
              left: 18,
              right: 18
            ),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: SizedBox(
                height: Get.height * 0.5,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: const TextSpan(
                          children: [
                            TextSpan(
                              text: 'เข้าสู่ระบบ ',
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt',
                                color: Color(0xFF282828),
                                fontSize: 18,
                              ),
                            ),
                            TextSpan(
                              text: 'กรอกข้อมูลให้ถูกต้อง',
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt',
                                color: Color(0xFF895F00),
                                fontSize: 12,
                              ),
                            ),
                          ]
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    //TODO :: เบอร์โทรศัพท์
                    SizedBox(
                      width: Get.width,
                      height: 53,
                      child: TextField(
                        controller: loginCtl.phoneTextController,
                        textAlign: TextAlign.center,
                        textAlignVertical: TextAlignVertical.bottom,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                            AppService.deny(),
                          ),
                        ],
                        style: const TextStyle(
                          fontWeight: FontWeight.w400,
                          fontFamily: 'Prompt',
                          color: Color(0xFF282828),
                          fontSize: 14,
                        ),
                        decoration: InputDecoration(
                          hintText: 'เบอร์โทรศัพท์',
                          hintStyle: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: Color(0xFF555555),
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: const BorderSide(
                              color: Color(0xFFECECEC),
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: const BorderSide(
                              color: Color(0xFFECECEC),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(15),
                            borderSide: const BorderSide(
                              color: Color(0xFFFFB100),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    //TODO :: ล็อคอิน
                    InkWell(
                      onTap: (){
                        loginCtl.typeMenu.value = "login";
                        if( agreementCtl.accept.value == false ){
                          AppWidget.showDialogPage(context, buildAlertAgreement());
                        } else {
                          loginCtl.page.value = "login";
                        }
                      },
                      child: Container(
                        width: Get.width,
                        height: 53,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: const [0.0, 1.0],
                            colors: [
                              const Color(0xFF000000).withOpacity(0.7),
                              const Color(0xFF000000),
                            ],
                          ),
                        ),
                        alignment: Alignment.center,
                        child: AppWidget.boldText(
                            context,
                            "ล็อคอิน",
                            16,
                            const Color(0xFFFFB100),
                            FontWeight.w500),
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Container(
                      width: Get.width,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        border: Border.all(
                          color: const Color(0xFFECECEC),
                        ),
                      ),
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(
                            width: 10,
                          ),
                          AppWidget.normalText(
                              context,
                              "หรือ ผ่านช่องทาง",
                              12,
                              const Color(0xFF282828),
                              FontWeight.w400),
                          const SizedBox(
                            width: 20,
                          ),
                          const VerticalDivider(
                            indent: 15,
                            endIndent: 15,
                            color: Color(0xFFECECEC),
                            thickness: 1,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Obx(() => centerCtl.os.value == "ios"
                              ? InkWell(
                            onTap: () async {
                              loginCtl.typeMenu.value = "registerWithApple";
                              if( agreementCtl.accept.value == false ){
                                AppWidget.showDialogPage(context, buildAlertAgreement());
                              } else {
                                if (kDebugMode) {
                                  print('login Apple');
                                }
                                registerCtl.appleLogin(context);
                              }
                            },
                            child: Image.asset(
                              'assets/image/login/apple_button.png',
                              width: 42,
                            ),
                          )
                              : const SizedBox()),
                          const SizedBox(
                            width: 10,
                          ),
                          InkWell(
                            onTap: () async {
                              loginCtl.typeMenu.value = "registerWithLine";
                              if( agreementCtl.accept.value == false ){
                                AppWidget.showDialogPage(context, buildAlertAgreement());
                              } else {
                                if (kDebugMode) {
                                  print('login line');
                                }
                                registerCtl.lineLogin(context);
                              }
                            },
                            child: Image.asset(
                              'assets/image/login/line_button.png',
                              width: 42,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Spacer(),
                    InkWell(
                      onTap: (){
                        loginCtl.typeMenu.value = "register";
                        if( agreementCtl.accept.value == false ){
                          AppWidget.showDialogPage(context, buildAlertAgreement());
                        } else {
                          loginCtl.page.value = "register";
                        }
                      },
                      child: Center(
                        child: RichText(
                            text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'ยังไม่มีบัญชีผู้ใช้งาน? ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF555555),
                                    fontSize: 14,
                                  ),
                                ),
                                TextSpan(
                                  text: 'สมัครสมาชิก',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFFFFB100),
                                    fontSize: 14,
                                    decoration: TextDecoration.underline,
                                  ),
                                ),
                              ]
                            )),
                      ),
                    ),
                    const Padding(padding: EdgeInsets.only(bottom: 40)),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  buildLoginForTG(){
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: Get.width,
        height: Get.height * 0.57,
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(25), topRight: Radius.circular(25)),
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.5, 1.0],
            colors: [
              Color(0xFFFFFFFF),
              Color(0xFFFFFFFF),
              Color(0xFFECECEC),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF664701).withOpacity(1),
              offset: const Offset(0, -2), // changes position of shadow
            ),
          ],
        ),
        padding: const EdgeInsets.only(
            top: 30,
            left: 18,
            right: 18
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: const TextSpan(
                  children: [
                    TextSpan(
                      text: 'เข้าสู่ระบบ ',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: Color(0xFF282828),
                        fontSize: 18,
                      ),
                    ),
                    TextSpan(
                      text: 'กรอกข้อมูลให้ถูกต้อง',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontFamily: 'Prompt',
                        color: Color(0xFF895F00),
                        fontSize: 12,
                      ),
                    ),
                  ]
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            //TODO :: เบอร์โทรศัพท์
            SizedBox(
              width: Get.width,
              height: 53,
              child: TextField(
                controller: loginCtl.phoneTextController,
                textAlign: TextAlign.center,
                textAlignVertical: TextAlignVertical.bottom,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.deny(
                    AppService.deny(),
                  ),
                ],
                style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFF282828),
                  fontSize: 14,
                ),
                decoration: InputDecoration(
                  hintText: 'เบอร์โทรศัพท์',
                  hintStyle: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF555555),
                    fontSize: 14,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(
                      color: Color(0xFFECECEC),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(
                      color: Color(0xFFECECEC),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(
                      color: Color(0xFFFFB100),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            //TODO :: ล็อคอิน
            InkWell(
              onTap: () async {
                loginCtl.typeMenu.value = "registerWithTG";
                if( agreementCtl.accept.value == false ){
                  AppWidget.showDialogPage(context, buildAlertAgreement());
                } else {
                  // await registerCtl.checkUserTG(context);
                }
              },
              child: Container(
                width: Get.width,
                height: 53,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0.0, 1.0],
                    colors: [
                      const Color(0xFF000000).withOpacity(0.7),
                      const Color(0xFF000000),
                    ],
                  ),
                ),
                alignment: Alignment.center,
                child: AppWidget.boldText(
                    context,
                    "ล็อคอิน",
                    16,
                    const Color(0xFFFFB100),
                    FontWeight.w500),
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 40)),
          ],
        ),
      ),
    );
  }

  buildAlertAgreement() {
    return Scaffold(
        body: Stack(
          children: [
            InkWell(
              onTap: () {
                agreementCtl.accept.value = false;
                Get.back();
              },
              child: Container(
                width: Get.width,
                height: Get.height,
                color: const Color(0xFFFFB100).withOpacity(0.3),
              ),
            ),
            Align(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 10),
                      child: Image.asset(
                        'assets/image/login/half_mascot.png', width: 127,
                      ),),
                    const SizedBox(
                      height: 3,
                    ),
                    Container(
                        width: Get.width,
                        height: 299,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                          gradient: const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: [0.0, 0.5, 1.0],
                            colors: [
                              Color(0xFFFFFFFF),
                              Color(0xFFFFFFFF),
                              Color(0xFFECECEC)
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF664701).withOpacity(1),
                              offset: const Offset(0, -2), // changes position of shadow
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            const SizedBox(
                              height: 40,
                            ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin:
                                  const EdgeInsets.only(left: 20, right: 20),
                                  child: Row(
                                    children: [
                                      Obx(
                                            () => agreementCtl.accept.value == false
                                            ? InkWell(
                                          onTap: () {
                                            agreementCtl.accept.value = true;
                                          },
                                          child: Container(
                                            width: 24,
                                            height: 24,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                const BorderRadius.only(
                                                  topLeft: Radius.circular(5),
                                                  topRight: Radius.circular(5),
                                                  bottomLeft: Radius.circular(5),
                                                  bottomRight: Radius.circular(5),
                                                ),
                                                border: Border.all(
                                                  width: 1.5,
                                                  color: const Color(0xFFDEDEDE),
                                                )),
                                          ),
                                        )
                                            : InkWell(
                                          onTap: () {
                                            agreementCtl.accept.value = false;
                                          },
                                          child: Container(
                                            width: 24,
                                            height: 24,
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFFFB100),
                                              borderRadius:
                                              const BorderRadius.only(
                                                topLeft: Radius.circular(5),
                                                topRight: Radius.circular(5),
                                                bottomLeft: Radius.circular(5),
                                                bottomRight: Radius.circular(5),
                                              ),
                                              border: Border.all(
                                                width: 1.5,
                                                color: const Color(0xFF664701),
                                              ),
                                            ),
                                            child: const Icon(
                                              Icons.check_rounded,
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Row(
                                      children: [
                                        AppWidget.normalText(
                                            context,
                                            "ฉันได้อ่านและยอมรับ ",
                                            12,
                                            const Color(0xFF555555),
                                            FontWeight.w400),
                                        InkWell(
                                          onTap: () {
                                            AppService.launchUrl(
                                                'https://www.agilesoftgroup.com/pms_term.html');
                                          },
                                          child: AppWidget.boldText(
                                              context,
                                              "ข้อตกลงเงื่อนไขการใช้บริการ",
                                              13,
                                              const Color(0xFF555555),
                                              FontWeight.w600),
                                        ),
                                        AppWidget.normalText(context, " และ ", 12,
                                            const Color(0xFF555555), FontWeight.w400),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            AppService.launchUrl(
                                                'https://www.agilesoftgroup.com/pms_policy.html');
                                          },
                                          child: AppWidget.boldText(
                                              context,
                                              "นโยบายความเป็นส่วนตัว",
                                              13,
                                              const Color(0xFF555555),
                                              FontWeight.w600),
                                        ),
                                        AppWidget.normalText(
                                            context,
                                            " บัญชีผู้ใช้งานของ",
                                            12,
                                            const Color(0xFF555555),
                                            FontWeight.w400),
                                      ],
                                    ),
                                    AppWidget.normalText(
                                        context,
                                        "PRACHAKIJ MOBILE แล้ว",
                                        12,
                                        const Color(0xFF555555),
                                        FontWeight.w400),
                                  ],
                                ),
                              ],
                            ),
                            const Spacer(),
                            Obx(
                                  () => agreementCtl.accept.value == false
                                  ? Container(
                                margin: const EdgeInsets.only(
                                  left: 20,
                                  right: 20,
                                ),
                                width: Get.width,
                                height: 53,
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(
                                    15,
                                  ),
                                  gradient: LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    stops: const [0.0, 1.0],
                                    colors: [
                                      const Color(0xFF000000).withOpacity(0.3),
                                      const Color(0xFF000000).withOpacity(0.4),
                                    ],
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: AppWidget.boldText(
                                    context,
                                    "ดำเนินการต่อ",
                                    16,
                                    const Color(0xFFFFB100).withOpacity(0.4),
                                    FontWeight.w500),
                              )
                                  : InkWell(
                                onTap: () async {
                                  if (kDebugMode) {
                                    print(loginCtl.typeMenu.value);
                                  }
                                  if(loginCtl.typeMenu.value == "register"){
                                    loginCtl.page.value = "register";
                                    agreementCtl.accept.value = false;
                                  } else if (loginCtl.typeMenu.value == "login"){
                                    if(loginCtl.phoneTextController.text == "0800000001"){
                                      loginCtl.newCheckProfile(context);
                                    } else {
                                      await loginCtl.newSendOTP(context);
                                      loginCtl.page.value = "verify";
                                      agreementCtl.accept.value = false;
                                    }
                                  } else if (loginCtl.typeMenu.value == "registerWithLine"){
                                    if (kDebugMode) {
                                      print('join accept agreement line');
                                    }
                                    await registerCtl.lineLogin(context);
                                    agreementCtl.accept.value = false;
                                  } else if(loginCtl.typeMenu.value == "registerWithApple"){
                                    if (kDebugMode) {
                                      print('join accept agreement apple');
                                    }
                                    await registerCtl.appleLogin(context);
                                    agreementCtl.accept.value = false;
                                  } else if (loginCtl.typeMenu.value == "registerWithTG"){
                                    if (kDebugMode) {
                                      print('join accept agreement tg');
                                    }
                                    loginCtl.page.value = "tg";
                                  }
                                  Get.back();

                                },
                                child: Container(
                                  margin: const EdgeInsets.only(
                                    left: 20,
                                    right: 20,
                                  ),
                                  width: Get.width,
                                  height: 53,
                                  decoration: BoxDecoration(
                                    color: Colors.red,
                                    borderRadius: BorderRadius.circular(
                                      15,
                                    ),
                                    gradient: LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      stops: const [0.0, 1.0],
                                      colors: [
                                        const Color(0xFF000000).withOpacity(0.7),
                                        const Color(0xFF000000),
                                      ],
                                    ),
                                  ),
                                  alignment: Alignment.center,
                                  child: AppWidget.boldText(
                                      context,
                                      "ดำเนินการต่อ",
                                      16,
                                      const Color(0xFFFFB100),
                                      FontWeight.w500),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                          ],
                        ))
                  ],
                )),
          ],
        ));
  }
}
