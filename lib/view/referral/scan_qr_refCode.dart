import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class ScanQrRefcode extends StatefulWidget {
  const ScanQrRefcode({super.key});

  @override
  State<ScanQrRefcode> createState() => _ScanQrRefcodeState();
}

class _ScanQrRefcodeState extends State<ScanQrRefcode> {
  final _textController = TextEditingController();
  // String qrText = '';
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  String? qrText;
  final registerCtl = Get.put(RegisterController());
  // late MobileScannerController controller = MobileScannerController(
  //   formats: const [BarcodeFormat.qrCode],
  // );

  @override
  void initState() {
    super.initState();
    registerCtl.refTextController.addListener(() {
      registerCtl.isScanned.value = registerCtl.refTextController.text.isNotEmpty;
    });
  }

  @override
  void reassemble() {
    super.reassemble();
    if (controller != null) {
      controller!.pauseCamera();
      controller!.resumeCamera();
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final scanWindow = Rect.fromCenter(
      center: MediaQuery.sizeOf(context).center(Offset.zero),
      width: 200,
      height: 200,
    );
    return Scaffold(
      body: Container(
        width: 1.sw,
        height: 1.sh,
        color: Colors.transparent,
        child: Stack(
          children: [
            Container(
              child: QRView(
                key: qrKey,
                onQRViewCreated: (QRViewController controller) {
                  this.controller = controller;

                  controller.scannedDataStream.listen((scanData) async {
                    String? qrCode = scanData.code;
                    print('QR Code: $qrCode');

                    if (qrCode != null && qrCode.isNotEmpty) {
                      // ตั้งค่าข้อมูลที่แสกนไปที่ TextEditingController
                      registerCtl.refTextController.text = qrCode;
                      print('refTextController: ${registerCtl.refTextController.text}');
                      registerCtl.isScanned.value = true;
                      print('isScanned: ${registerCtl.isScanned.value}');
                      registerCtl.update();

                      // หยุดการสแกนหลังจากได้ผลลัพธ์
                      controller.pauseCamera();

                      // ปิดการเชื่อมต่อกล้องหลังจากที่ส่งค่ากลับ
                      controller.dispose();  // ปิดการเชื่อมต่อกล้อง

                      // ส่งค่ากลับไปยังหน้าที่เรียก (ไม่ปิดหน้าหลัก)
                      Navigator.of(context).pop(qrCode); // ส่งค่ากลับ
                    } else {
                      // หากไม่มีค่า qrCode หรือ qrCode เป็นค่าว่าง
                      print("QR Code ไม่ถูกต้องหรือไม่มีค่า");
                    }
                  });
                },
              ),
            ),

            // Container(
            //   child: QRView(
            //     key: qrKey,
            //     onQRViewCreated: (QRViewController controller) {
            //       this.controller = controller;
            //
            //       // รอให้กล้องเริ่มต้นเสร็จแล้วค่อยเริ่มฟังข้อมูล
            //       controller.scannedDataStream.listen((scanData) async {
            //         String? qrCode = scanData.code;
            //         print('QR Code: $qrCode');
            //
            //         if (qrCode != null && qrCode.isNotEmpty) {
            //           // ตั้งค่าข้อมูลที่แสกนไปที่ TextEditingController
            //           registerCtl.refTextController.text = qrCode;
            //           print('refTextController: ${registerCtl.refTextController.text}');
            //           registerCtl.isScanned.value = true;
            //           print('isScanned: ${registerCtl.isScanned.value}');
            //           registerCtl.update();
            //
            //           // หยุดการสแกนหลังจากได้ผลลัพธ์
            //           controller.pauseCamera();
            //
            //           // ใช้ `addPostFrameCallback` เพื่อให้แน่ใจว่าหยุดการทำงานของกล้องก่อนที่เราจะเปลี่ยนหน้าจอ
            //           WidgetsBinding.instance.addPostFrameCallback((_) {
            //             controller.dispose();  // ทำการปิดการเชื่อมต่อของกล้อง
            //             Navigator.of(context).pop(qrCode); // ส่งค่ากลับ
            //           });
            //         } else {
            //           // หากไม่มีค่า qrCode หรือ qrCode เป็นค่าว่าง
            //           print("QR Code ไม่ถูกต้องหรือไม่มีค่า");
            //         }
            //       });
            //     },
            //   ),
            // ),
            // Container(
            //   child: QRView(
            //     key: qrKey,
            //     onQRViewCreated: (QRViewController controller) {
            //       this.controller = controller;
            //       controller.scannedDataStream.listen((scanData) {
            //         // เมื่อสแกน QR Code
            //         String? qrCode = scanData.code;
            //         print('QR Code: $qrCode');
            //
            //         // ตั้งค่าข้อมูลที่แสกนไปที่ TextEditingController
            //         if (qrCode != null && qrCode.isNotEmpty) {
            //           registerCtl.refTextController.text = qrCode;
            //           print('refTextController: ${registerCtl.refTextController.text}');
            //           registerCtl.isScanned.value = true;
            //           print('isScanned: ${registerCtl.isScanned.value}');
            //           registerCtl.update();
            //         }
            //         // หยุดการสแกนหลังจากได้ผลลัพธ์
            //         controller.pauseCamera();
            //
            //         // ส่งค่ากลับ
            //         Navigator.of(context).pop(qrCode);
            //       });
            //     },
            //   ),
            // ),
            // child:
            // MobileScanner(
            //   scanWindow: scanWindow,
            //   controller: controller,
            //   onDetect: (BarcodeCapture code) {
            //     if (code.barcodes.isNotEmpty) {
            //       var qrCode = code.barcodes.first.rawValue.toString();
            //       print('QR Code: $qrCode'); // Debugging line to check the QR code
            //       _textController.text = qrCode;
            //
            //       // Provide feedback after scanning
            //       Vibrate.feedback(FeedbackType.success);
            //
            //       // If there's valid QR code data, pop and pass the value
            //       Navigator.of(context).pop(qrCode);
            //     } else {
            //       print('No QR code detected');
            //     }
            //   },
            // ),
            Container(
              height: 96,
              width: Get.width,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFFFC700).withOpacity(0.8),
                    Color(0xFFFFB100),
                    Color(0xFFFF9900).withOpacity(0.8)
                  ],
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 16, top: 38, right: 16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: (){
                        Navigator.pop(context);
                      },
                      child: Container(
                        height: 34,
                        width: 34,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.white, width: 1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                    Text(
                      'แสกน คิวอาร์โค้ด',
                      style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          height: 1,
                          fontFamily: 'Prompt',
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            )
                          ]),
                    ),
                    Container(
                      // color: Colors.red,
                      height: 34,
                      width: 34,
                    )
                  ],
                ),
              ),
            ),
            // Positioned(
            //   top: 202, // Center the container vertically
            //   left: (1.sw - 255) / 2,
            //   child: Container(
            //     height: 255,
            //     width: 255,
            //     decoration: BoxDecoration(
            //       borderRadius: BorderRadius.circular(20),
            //       border: Border.all(color: Color(0xFFD9D9D9).withOpacity(0.5), width: 30),
            //     ),
            //   ),
            // ),
            Positioned(
              top: 202, // Center the container vertically
              left: (1.sw - 255) / 2, // Center the container horizontally
              child: Column(
                children: [
                  Container(
                      height: 255,
                      width: 255,
                      decoration: BoxDecoration(
                        // color: Color(0xFFD9D9D9).withOpacity(0.5),
                        // border: Border.all(color: Color(0xFFD9D9D9).withOpacity(0.5), width: 4),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: CustomPaint(
                        painter: ScannerOverlayPainter(),
                      )),
                  SizedBox(height: 58),
                  Text(
                    'กรุณาเช็คตำแหน่งคิวอาร์โค้ด',
                    style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        height: 0.25,
                        fontFamily: 'Prompt',
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.25),
                            blurRadius: 5,
                            offset: Offset(0, 1),
                          )
                        ]),
                  ),
                  SizedBox(height: 20),
                  Text(
                    'ให้อยู่ในกรอบที่เหมาะสม เพื่อทำการสแกน',
                    style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w300,
                        height: 0.25,
                        fontFamily: 'Prompt',
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.25),
                            blurRadius: 5,
                            offset: Offset(0, 1),
                          )
                        ]
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Color(0xFFF3A700)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final double radius = 20; // ความโค้งของมุม
    final double cornerLength = 40; // ความยาวของขีดมุม (แก้ให้เท่ากันทุกมุม)

    Path path = Path();

    // 🔹 มุมบนซ้าย
    path.moveTo(0, radius);
    path.arcToPoint(Offset(radius, 0), radius: Radius.circular(radius));
    path.lineTo(cornerLength, 0);
    path.moveTo(0, radius);
    path.lineTo(0, cornerLength);

    // 🔹 มุมบนขวา
    path.moveTo(size.width - cornerLength, 0);
    path.lineTo(size.width - radius, 0);
    path.arcToPoint(Offset(size.width, radius),
        radius: Radius.circular(radius));
    path.moveTo(size.width, radius);
    path.lineTo(size.width, cornerLength);

    // 🔹 มุมล่างขวา
    path.moveTo(size.width, size.height - cornerLength);
    path.lineTo(size.width, size.height - radius);
    path.arcToPoint(Offset(size.width - radius, size.height),
        radius: Radius.circular(radius));
    path.moveTo(size.width - radius, size.height);
    path.lineTo(size.width - cornerLength, size.height); // ✅ แก้ให้ยาวเท่ากัน

    // 🔹 มุมล่างซ้าย (แก้ไขให้ยาวเท่ากัน)
    path.moveTo(radius, size.height);
    path.arcToPoint(Offset(0, size.height - radius),
        radius: Radius.circular(radius));
    path.lineTo(0, size.height - cornerLength); // ✅ แก้ให้ยาวเท่ากัน
    path.moveTo(radius, size.height);
    path.lineTo(cornerLength, size.height); // ✅ เพิ่มเส้นแนวนอนให้ยาวเท่ากัน

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
