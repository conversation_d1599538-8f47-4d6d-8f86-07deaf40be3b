import 'dart:ui';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/drawer/about_app/about_app.dart';
import 'package:mapp_prachakij_v3/view/drawer/contact/contact_us.dart';
import 'package:mapp_prachakij_v3/view/drawer/feedback/feedback.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/view/profile/edit_MR/show_detail_edit_MR.dart';
import 'package:mapp_prachakij_v3/view/profile/profile_edit.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final ScrollController scrollController = ScrollController();

  RxBool showSwitchStatus = false.obs;
  RxBool lineStatus = false.obs;
  RxBool tgStatus = false.obs;

  RxDouble scrollParam = 0.00.obs;

  bool isLoading = true;

  final dataProfileCtl = Get.put(ProfileController());
  final settingCtl = Get.put(SettingController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    getData();
    // settingCtl.getVersion();
  }

  getData() async {
    analytics.setCurrentScreen(screenName: "Profile");
    if (dataProfileCtl.profile.value.lineConnect == 'Y' &&
        dataProfileCtl.profile.value.userIdLine != null) {
        lineStatus.value = true;
    }
    if (dataProfileCtl.profile.value.userIdTg != "" && dataProfileCtl.profile.value.userIdTg != null) {
      tgStatus.value = true;
    }
    showSwitchStatus.value = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFEDEDED),
              Color(0xFFF2F2F2),
            ],
          ),
        ),
        child: Stack(
          children: [
            buildTopBackground(),
            buildContent(),
            buildTopContainer(),
            Positioned(
              top: 65,
                left: 30,
                child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      height: 35,
                      width: 35,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white,
                        border: Border.all(color: Color(0xFFBCBCBC).withOpacity(0.5)),
                      ),
                      alignment: Alignment.center,
                      child: Icon(Icons.keyboard_arrow_left_rounded,
                        color: Color(0xFFFFB100),),
                    )
                ),
            ),
          ],
        ),
      ),
    );
  }

  buildTopBackground() {
    return Container(
        width: Get.width,
        height: 170,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFFFB100).withOpacity(1),
              const Color(0xFFFFC700).withOpacity(0.7),
              const Color(0xFFFFC700).withOpacity(0.4),
              const Color(0xFFFFC700).withOpacity(0),
            ],
          ),
        ));
  }

  buildTopContainer() {
    return Stack(
      children: [
        Obx(
          () => scrollParam.value >= 14
              ? ClipRRect(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                    child: SizedBox(
                      width: Get.width,
                      height: 175,
                    ),
                  ),
                )
              : const SizedBox(),
        ),
        Container(
          margin: const EdgeInsets.only(
            top: 65,
          ),
          width: Get.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              AppWidget.boldTextS(
                context,
                "ข้อมูลโปรไฟล์",
                18,
                const Color(0xFF282828),
                FontWeight.w300,
              ),
              const SizedBox(
                height: 20,
              ),
              Container(
                height: 55,
                width: 55,
                decoration: BoxDecoration(
                    border: Border.all(
                        width: 2.0,
                        color: const Color(0xFF282828),
                        style: BorderStyle.solid),
                    shape: BoxShape.circle),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(50.0),
                  child: Image(
                    image: dataProfileCtl.profile.value.profilePicture ==
                                null ||
                            dataProfileCtl.profile.value.profilePicture == ""
                        ? const AssetImage('assets/image/home/<USER>')
                            as ImageProvider
                        : NetworkImage(
                            "${dataProfileCtl.profile.value.profilePicture}"),
                    width: 50,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  buildContent() {
    return Obx(() => NotificationListener(
      onNotification: (notificationInfo) {
        if (notificationInfo is ScrollUpdateNotification) {
          scrollParam.value = notificationInfo.metrics.pixels;
        }
        return true;
      },
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: Get.width,
              margin: EdgeInsets.only(
                top: 200,
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AppWidget.boldText(
                        context,
                        "ข้อมูลส่วนตัว",
                        16,
                        const Color(0xFF282828),
                        FontWeight.w400,
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                  const ProfileEditPage()));
                        },
                        child: SizedBox(
                          width: 25,
                          height: 25,
                          child: Image.asset(
                            'assets/image/profile/pen.png',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "ชื่อ-นามสกุล",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.fullname == null ||
                        dataProfileCtl.profile.value.fullname == ""
                        ? "ไม่มีข้อมูล"
                        : dataProfileCtl.profile.value.fullname,
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "เลขที่บัตรประชาชน",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.idcard == null ||
                        dataProfileCtl.profile.value.idcard == ""
                        ? "ไม่มีข้อมูล"
                        : dataProfileCtl.profile.value.idcard,
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "วันเกิด",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.birthday == null ||
                        dataProfileCtl.profile.value.birthday == "" ||
                        dataProfileCtl.profile.value.birthday == "0000-00-00"
                        ? "ไม่มีข้อมูล"
                        : AppService.dateThaiDate(dataProfileCtl
                        .profile.value.birthday
                        ?.split("T")[0]),
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "ที่อยู่",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.address == null ||
                        dataProfileCtl.profile.value.address == ""
                        ? "ไม่มีข้อมูล"
                        : dataProfileCtl.profile.value.address,
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "ตำแหน่งที่ตั้ง",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  dataProfileCtl.profile.value.locationHome == null ||
                      dataProfileCtl.profile.value.locationHome == ""
                      ? AppWidget.normalText(
                    context,
                    "ไม่มีข้อมูล",
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  )
                      : Center(
                    child: Container(
                      margin: const EdgeInsets.only(top: 10),
                      width: Get.width,
                      height: 40,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black45,
                            offset: Offset(1, 1),
                            blurRadius: 2,
                          ),
                        ],
                        color: const Color(0xFF282828),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.my_location,
                            color: Color(0xFFFFB100),
                            size: 16,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          AppWidget.normalText(
                            context,
                            dataProfileCtl.profile.value.locationHome,
                            14,
                            const Color(0xFFFFFFFF),
                            FontWeight.w400,
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "เบอร์โทรศัพท์",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.mobile == null ||
                        dataProfileCtl.profile.value.mobile == ""
                        ? "ไม่มีข้อมูล"
                        : dataProfileCtl.profile.value.mobile,
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  AppWidget.normalText(
                    context,
                    "อีเมลล์",
                    14,
                    const Color(0xFF282828),
                    FontWeight.w400,
                  ),
                  AppWidget.normalText(
                    context,
                    dataProfileCtl.profile.value.email == null ||
                        dataProfileCtl.profile.value.email == ""
                        ? "ไม่มีข้อมูล"
                        : dataProfileCtl.profile.value.email,
                    13,
                    const Color(0xFF707070),
                    FontWeight.w400,
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
            const Divider(
              thickness: 0.5,
              indent: 0,
              endIndent: 0,
              color: Color(0xFFE8E6E2),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              margin: EdgeInsets.only(
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              child: AppWidget.boldText(
                context,
                "เชื่อมต่อบัญชีโซเชียลมิเดีย",
                14,
                const Color(0xFF282828),
                FontWeight.w400,
              ),
            ),
            // const SizedBox(
            //   height: 10,
            // ),
            // Container(
            //   margin: EdgeInsets.only(
            //     left: Get.width < 500 ? 18 : 54,
            //     right: Get.width < 500 ? 18 : 54,
            //   ),
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //     children: [
            //       Row(
            //         children: [
            //           SvgPicture.asset(
            //               'assets/image/profile/black_logo_line.svg'),
            //           const SizedBox(
            //             width: 10,
            //           ),
            //           AppWidget.normalText(
            //             context,
            //             "LINE",
            //             14,
            //             const Color(0xFF707070),
            //             FontWeight.w400,
            //           ),
            //         ],
            //       ),
            //       buildLineSwitch(),
            //     ],
            //   ),
            // ),
            // const SizedBox(
            //   height: 10,
            // ),
            // Container(
            //   margin: EdgeInsets.only(
            //     left: Get.width < 500 ? 18 : 54,
            //     right: Get.width < 500 ? 18 : 54,
            //   ),
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //     children: [
            //       Row(
            //         children: [
            //           const Icon(Icons.telegram, color: Colors.black, size: 22,),
            //           const SizedBox(
            //             width: 8,
            //           ),
            //           AppWidget.normalText(
            //             context,
            //             "TELEGRAM",
            //             14,
            //             const Color(0xFF707070),
            //             FontWeight.w400,
            //           ),
            //         ],
            //       ),
            //       buildTGSwitch(),
            //     ],
            //   ),
            // ),
            const SizedBox(
              height: 10,
            ),
            const Divider(
              thickness: 0.5,
              indent: 0,
              endIndent: 0,
              color: Color(0xFFE8E6E2),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              margin: EdgeInsets.only(
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              child: AppWidget.boldText(
                context,
                "จัดการบัญชีใช้งาน",
                14,
                const Color(0xFF282828),
                FontWeight.w400,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (context) => const ShowDetailEditMRPage(),
                ));
              },
              child: Container(
                margin: EdgeInsets.only(
                  left: Get.width < 500 ? 18 : 54,
                  right: Get.width < 500 ? 18 : 54,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppWidget.normalText(
                      context,
                      "ข้อมูลลงทะเบียนตัวแทนผู้แนะนำลูกค้า MR",
                      14,
                      const Color(0xFF707070),
                      FontWeight.w400,
                    ),
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 10,
                      color: Color(0xFF895F00),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            InkWell(
              onTap: () {
                AppAlert.showAlertDel(
                    context,
                    "ปิด / ลบ บัญชีใช้งาน",
                    "หากคุณต้องการที่จะทำการ ปิดการใช้งานบัญชี \nหรือลบบัญชี PRACHAKIJ mobile ของคุณ",
                    "กรุณาติดต่อผ่านช่องทางด้านล่าง",
                    "ยกเลิก");
              },
              child: Container(
                  margin: EdgeInsets.only(
                    left: Get.width < 500 ? 18 : 54,
                    right: Get.width < 500 ? 18 : 54,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AppWidget.normalText(
                        context,
                        "ปิดบัญชีใช้งาน",
                        14,
                        const Color(0xFF707070),
                        FontWeight.w400,
                      ),
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 10,
                        color: Color(0xFF895F00),
                      ),
                    ],
                  )),
            ),
            const SizedBox(
              height: 10,
            ),
            const Divider(
              thickness: 0.5,
              indent: 0,
              endIndent: 0,
              color: Color(0xFFE8E6E2),
            ),
            const SizedBox(
              height: 10,
            ),
            Container(
              margin: EdgeInsets.only(
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              child: AppWidget.boldText(
                context,
                "เกี่ยวกับแอพ",
                14,
                const Color(0xFF282828),
                FontWeight.w400,
              ),
            ),
            Container(
              width: Get.width,
              margin: EdgeInsets.only(
                left: Get.width < 500 ? 18 : 54,
                right: Get.width < 500 ? 18 : 54,
              ),
              child: Column(
                children: [
                  const SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => const AboutAppPage());
                    },
                    child: SizedBox(
                        width: Get.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              "เงื่อนไขและข้อกำหนด",
                              14,
                              const Color(0xFF707070),
                              FontWeight.w400,
                            ),
                            const Icon(
                              Icons.arrow_forward_ios,
                              size: 10,
                              color: Color(0xFF895F00),
                            ),
                          ],
                        )),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => const FeedbackPage());
                    },
                    child: SizedBox(
                        width: Get.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              "ข้อเสนอแนะ",
                              14,
                              const Color(0xFF707070),
                              FontWeight.w400,
                            ),
                            const Icon(
                              Icons.arrow_forward_ios,
                              size: 10,
                              color: Color(0xFF895F00),
                            ),
                          ],
                        )),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => const ContactUsPage());
                    },
                    child: SizedBox(
                        width: Get.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            AppWidget.normalText(
                              context,
                              "ติดต่อเรา",
                              14,
                              const Color(0xFF707070),
                              FontWeight.w400,
                            ),
                            const Icon(
                              Icons.arrow_forward_ios,
                              size: 10,
                              color: Color(0xFF895F00),
                            ),
                          ],
                        )),
                  ),
                  const SizedBox(
                    height: 40,
                  ),
                  InkWell(
                    onTap: () {
                      dataProfileCtl.clearProfile();
                      AppService.logout(context);
                    },
                    child: Center(
                      child: Container(
                          width: Get.width,
                          height: 40,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            boxShadow: const [
                              BoxShadow(
                                color: Colors.black45,
                                offset: Offset(1, 1),
                                blurRadius: 2,
                              ),
                            ],
                            color: const Color(0xFF282828),
                          ),
                          child: Center(
                            child: AppWidget.normalText(
                              context,
                              "ออกจากระบบ",
                              14,
                              const Color(0xFFFFFFFF),
                              FontWeight.w400,
                            ),
                          )),
                    ),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AppWidget.normalTextS(context, "Version : ", 14,
                          Colors.black, FontWeight.w400),
                      AppWidget.boldTextS(
                          context,
                          settingCtl.currentVersion.toString(),
                          14,
                          Colors.black,
                          FontWeight.w400),
                    ],
                  ),
                ],
              ),
            ),
            const Padding(padding: EdgeInsets.only(bottom: 100))
          ],
        ),
      ),
    ));
  }

  buildLineSwitch() {
    return Row(
      children: <Widget>[
        Transform.scale(
          scale: 0.7,
          child: Obx(() => showSwitchStatus.value == true
              ? FlutterSwitch(
                  width: 50,
                  height: 25,
                  toggleSize: 20,
                  borderRadius: 50,
                  padding: 3,
                  value: lineStatus.value,
                  showOnOff: false,
                  activeColor: const Color(0xFFFFB100),
                  onToggle: (val) async {
                    // lineStatus.value = val;
                  },
                )
              : Container()),
        )
      ],
    );
  }

  buildTGSwitch() {
    return Row(
      children: <Widget>[
        Transform.scale(
          scale: 0.7,
          child: Obx(() => showSwitchStatus.value == true
              ? FlutterSwitch(
            width: 50,
            height: 25,
            toggleSize: 20,
            borderRadius: 50,
            padding: 3,
            value: tgStatus.value,
            showOnOff: false,
            activeColor: const Color(0xFFFFB100),
            onToggle: (val) async {
              // lineStatus.value = val;
            },
          )
              : Container()),
        )
      ],
    );
  }
}
