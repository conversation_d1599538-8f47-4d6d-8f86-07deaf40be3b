import 'dart:io';
import 'dart:ui';
import 'dart:math' as math;

import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/couponRestaurant_Cafe/couponR_C.controller.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/couponRestaurant_Cafe/modal_confirm.dart';
import 'package:mapp_prachakij_v3/view/couponRestaurant_Cafe/modal_getCoupon.dart';

class CouponrC_R extends StatefulWidget {
  const CouponrC_R({super.key});

  @override
  State<CouponrC_R> createState() => _CouponrC_RState();
}

class _CouponrC_RState extends State<CouponrC_R> {
  var formattedPrice =
  NumberFormat.currency(locale: 'en_US', symbol: '', decimalDigits: 0);

  bool showTab = true;
  var cataloge = "accessories";

  var couponRCGet = Get.find<CouponRCController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  bool _isCouponValid(coupon) {
    DateTime expireDate = DateTime.parse(coupon.expire_date.toString());
    DateTime today = DateTime.now();
    bool isToday = expireDate.year == today.year &&
        expireDate.month == today.month &&
        expireDate.day == today.day;

    return isToday;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        body: ColorfulSafeArea(
          color: Color(0xFFF3F3F3),
          top: true,
          bottom: false,
          child: GetBuilder<CouponRCController>(
              builder: (couponRC) {
                return Container(
                  height: MediaQuery.of(context).size.height,
                  width: MediaQuery.of(context).size.width,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(0.00, -1.00),
                      end: Alignment(0, 1),
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                  ),
                  child: Stack(
                    children: [
                      Column(
                        children: [
                          _buildAppBar(),
                          _buildTab(),
                          const SizedBox(
                            height: 10,
                          ),
                          couponRC.isLoading.value == true ? Container() :Expanded(
                            child: RefreshIndicator(
                              color: const Color(0xFFFFB100),
                              backgroundColor: Colors.white,
                              onRefresh: () async {
                                couponRC.getCouponRC();
                              },
                              child: SingleChildScrollView(
                                physics: AlwaysScrollableScrollPhysics(),
                                child: showTab
                                    ? Column(
                                  children: couponRC.couponList.data!
                                      .where((coupon) {
                                    if(_isCouponValid(coupon)){
                                      return true;
                                    }
                                    return false; // เงื่อนไขที่เช็ก
                                  })
                                      .toList()
                                      .isNotEmpty
                                      ? List.generate(
                                    couponRC.couponList.data!.length,
                                        (index) {
                                      if(_isCouponValid(couponRC.couponList.data![index])){
                                        return CouponDetail(index);
                                      }
                                      return Container();
                                    },
                                  )
                                      : [_buildShowNone("ไม่มีรายการคูปองของคุณ")],
                                )
                                    : Column(
                                  children: couponRC.couponList.data!
                                      .where((coupon) {
                                    if(_isCouponValid(coupon) != true){
                                      return true;
                                    }
                                    return false; // เงื่อนไขที่เช็ก
                                  }) // กรองเฉพาะคูปองที่ status == "1"
                                      .toList()
                                      .isNotEmpty
                                      ? List.generate(
                                    couponRC.couponList.data!.length,
                                        (index) {
                                      if(_isCouponValid(couponRC.couponList.data![index])){
                                        return Container();
                                      }
                                      return CouponDetail(index);
                                    },
                                  )
                                      : [
                                    _buildShowNone(
                                        "ไม่มีรายการประวัติการใช้คูปองของคุณ"),
                                  ],
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                );
              }),
        ),
      ),
    );
  }

  Widget _buildShowNone(String title) {
    return Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.5,
        alignment: Alignment.bottomCenter,
        // color: Colors.red.withOpacity(0.5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 90,
              child:
              Image.asset("assets/image/ticket/CouponLogoWithTokjai.png"),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF895F00),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(
              height: 10,
            ),
            const Text(
              "หากพบปัญหา หรือมีข้อสงสัยกรุณาแจ้งพนักงาน",
              style: TextStyle(
                fontSize: 15,
                color: Color(0xFF2B1710),
                fontWeight: FontWeight.w500,
                letterSpacing: 0
              ),
            ),
          ],
        ));
  }

  Widget _buildTab() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            showTab = true;
            setState(() {});
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: showTab
                      ? const BoxDecoration(
                    border: Border(
                      top: BorderSide(
                        // width: 1,
                        color: Colors.transparent,
                      ),
                      // bottom: BorderSide(
                      //   width: 1,
                      //   color: Color(0xFFFFB100)
                      // ),
                    ),
                    color: const Color(0xFFFFB100),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0xFFEDEDED),
                        blurRadius: 15,
                        offset: Offset(3, 0),
                      ),
                    ],
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                    // color: Colors.white,
                    border: Border(
                      right: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),
                    ),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(18),
                    ),
                  )),
              showTab
                  ? Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 47,
                decoration: const BoxDecoration(
                  // color: Colors.white,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("คูปองที่ใช้ได้",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: showTab ? FontWeight.w600: FontWeight.w400,
                        color: showTab
                            ? Color(0xFF282828)
                            : Color(0xFF282828).withOpacity(0.5))),
              ),
            ],
          ),
        ),
        InkWell(
          onTap: () {
            showTab = false;
            setState(() {});
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width: MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: !showTab
                      ? const BoxDecoration(
                    color: Color(0xFFFFB100),
                    // border: Border.all(
                    //   width: 1,
                    //   color: Color(0xFFBCBCBC),
                    // ),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                    ),
                    // color: Colors.white,
                    border: Border(
                      left: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),
                    ),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(18),
                    ),
                  )),
              !showTab
                  ? Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 47,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                  ),
                  // color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width: MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("ประวัติการใช้คูปอง",
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: !showTab ? FontWeight.w600: FontWeight.w400,
                        color:
                        !showTab ? Colors.black : const Color(0x88282828))),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.95,
      height: 50,
      decoration: BoxDecoration(
        // color: Colors.white,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFF3F3F3),
            const Color(0xFFFFFFFF).withOpacity(0)
          ],
        ),
      ),
      // color: Colors.white,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.centerLeft,
              child: InkWell(
                onTap: () {
                  Get.back();
                  // Navigator.push(
                  //     context,
                  //     MaterialPageRoute(
                  //         builder: (context) => const MobileBodyPage()));
                },
                child: Container(
                  width: 34,
                  height: 34,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(10)),
                    border: Border.all(
                        width: 1,
                        color: const Color(0xFFBCBCBC).withOpacity(0.5)),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.arrow_back_ios_new_rounded,
                      size: 14,
                      color: Color(0xFFFFB100),
                    ),
                  ),
                ),
              ),
            ),
            Text(
              "คูปองของขวัญ",
              style: TextStyle(
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.2),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF2B1710),
              ),
            ),
            InkWell(
              onTap: () {
                showDialog(
                    barrierColor: Colors.transparent,
                    context: context,
                    useSafeArea: false,
                    builder: (_) => const ModalGetcoupon());
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.25,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  border: Border.all(
                    width: 1,
                    color: const Color(0xFFBCBCBC).withOpacity(0.5),
                  ),
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.control_point,
                      size: 18,
                      color: Color(0xFF2B1710),
                    ),
                    SizedBox(
                      width: 5,
                    ),
                    Text(
                      "รับคูปองแทน",
                      style: TextStyle(
                        fontSize: 10,
                        color: Color(0xFF2B1710),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget CouponDetail(index) {
    bool checkExpire = false;
    if (_isCouponValid(couponRCGet.couponList.data![index])) {
      checkExpire = false;
    } else {
      checkExpire = true;
    }
    bool availableUse = isAvailableUse();
    // not use expire
    return GetBuilder<CouponRCController>(
        builder: (couponRC) {
          return GestureDetector(
            onTap: () {
              couponRC.selectItem(index);

              if(!showTab){
                return;
              }

              if(!availableUse){
                Get.snackbar(
                  'คูปองนี้เริ่มใช้ได้ในเวลา 10:30 น. ถึง 16:00 น.',
                  'วันที่ ${formatThaiDate(
                      couponRC.couponList.data![index].expire_date.toString())} 10:30 น. ถึง 16:00 น.',
                  snackPosition: SnackPosition.TOP,
                  duration: const Duration(seconds: 5),
                );
                return;
              }
              if(couponRC.couponList.data![index].status == "1" || checkExpire || !availableUse){
                return;
              }
              showDialog(
                barrierColor: Colors.transparent,
                context: context,
                useSafeArea: false,
                builder: (_) => AnimatedSheet(),
              );
            },
            child: Container(
              width: MediaQuery.of(context).size.width,
              // color: Colors.green,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height * 0.16,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        // color: Colors.red,
                        color: Colors.transparent,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.2),
                            spreadRadius: -15,
                            blurRadius: 15,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      )),
                  Container(
                    width: MediaQuery.of(context).size.width * 0.95,
                    height: MediaQuery.of(context).size.height * 0.17,
                    child: SvgPicture.asset(
                      "assets/image/ticket/ticketRestaurant_Cefe.svg",
                      fit: BoxFit.fill,
                    ),
                  ),

                  Container(
                      width: MediaQuery.of(context).size.width * 0.81,
                      height: MediaQuery.of(context).size.height * 0.14,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Positioned(
                            top: 5,
                            left: 0,
                            child: Text.rich(TextSpan(children: [
                              TextSpan(
                                text: "คุณ${couponRC.couponList.data![index].car_owner_name}",
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF282828),
                                  fontWeight: FontWeight.w400,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.05),
                                      offset: const Offset(0, 4),
                                      blurRadius: 10,
                                    ),
                                  ],
                                ),
                              ),
                              TextSpan(
                                text: ' ทะเบียนรถ ${couponRC.couponList.data![index].register_license}',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF895F00),
                                  fontWeight: FontWeight.w400,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.05),
                                      offset: const Offset(0, 4),
                                      blurRadius: 10,
                                    ),
                                  ],
                                ),
                              ),
                            ])),
                          ),
                          Positioned(
                            left: 0,
                            child: Container(
                              // color: Colors.red.withOpacity(0.5),
                              width: MediaQuery.of(context).size.width * 0.625,
                              // margin: const EdgeInsets.only(bottom: 4,top: 6),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "คูปองคะแนน",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Color(0xFF282828),
                                          fontWeight: FontWeight.w600,
                                          shadows: [
                                            Shadow(
                                              color: Colors.black.withOpacity(0.05),
                                              offset: Offset(0, 4),
                                              blurRadius: 10,
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        height: 50,
                                        // color: Colors.red.withOpacity(0.5),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          children: [
                                            Container(
                                              width: MediaQuery.of(context).size.width * 0.225,
                                              // height: 40,
                                              alignment: Alignment.centerLeft,
                                              // color: Colors.green.withOpacity(0.5),
                                              child: ShaderMask(
                                                shaderCallback: (Rect bounds) {
                                                  return const LinearGradient(
                                                    begin: Alignment(0.00, -1.00),
                                                    end: Alignment(0, 1),
                                                    colors: [
                                                      Color(0xFFFFC700),
                                                      Color(0xFFFFB100),
                                                      Color(0xFFFF9900)
                                                    ],
                                                  ).createShader(bounds);
                                                },
                                                child: Text(
                                                  "${AppService.numberFormatNon0(int.parse(couponRC.couponList.data![index].point.toString()))}",
                                                  textAlign: TextAlign.center,
                                                  style: TextStyle(
                                                    fontSize: 28.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color: Colors.white, // Important for ShaderMask
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Container(
                                              height: 17,
                                              alignment: Alignment.center,
                                              // color: Colors.red.withOpacity(0.5),
                                              child: Text(
                                                "PMSpoint",
                                                style: TextStyle(
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w600,

                                                  color: Color(0xFF282828),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Text(
                                        "คูปองคะแนน",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.transparent,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    width: 50,
                                    height: 50,
                                    child: Image.asset(
                                        "assets/image/ticket/couponLogo.png"),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // Divider(
                          //   color: Colors.green,
                          //   thickness: 1,
                          // ),
                          Positioned(
                            left: 0,
                            bottom: 5,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [

                                // SizedBox(
                                //   height: 10,
                                // ),

                                // SizedBox(
                                //   height: 10,
                                // ),
                                Text(
                                  "ใช้สำหรับร้านอาหารอร่อยทุกวัน และร้านกาแฟ CU Cafe",
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Color(0xFF282828),
                                    fontWeight: FontWeight.w400,
                                    shadows: [
                                      Shadow(
                                        color: Colors.black.withOpacity(0.05),
                                        offset: const Offset(0, 4),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  children: [
                                    const Text(
                                      "วันหมดอายุ",
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Color(0xFF895F00),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 2,
                                    ),
                                    Text(
                                      formatThaiDate(
                                          couponRC.couponList.data![index].expire_date.toString()),
                                      // couponRC.couponList.data![index].expire_date.toString(),
                                      style: const TextStyle(
                                        fontSize: 10,
                                        color: Color(0xFF895F00),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    const Icon(
                                      Icons.error_outline,
                                      color: Color(0xFFEB2227),
                                      size: 15,
                                    ),
                                    const SizedBox(
                                      width: 2,
                                    ),
                                    const Text(
                                      "ใช้ได้ในเวลาทำการเท่านั้น",
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Color(0xFFEB2227),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            top: 5,
                            right: MediaQuery.of(context).size.width * 0.1275,
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.1,
                              height: MediaQuery.of(context).size.height * 0.125,
                              alignment: Alignment.centerRight,
                              // color: Colors.red.withOpacity(0.5),
                              child: const DottedLine(
                                direction: Axis.vertical,
                                dashColor: Colors.orange,
                                dashLength: 5,
                                dashGapLength: 5,
                                lineThickness: 2,
                                dashRadius: 2,
                              ),
                            ),
                          ),
                          Positioned(
                            right: - MediaQuery.of(context).size.width * 0.04,
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.18,
                              height: MediaQuery.of(context).size.height * 0.125,
                              alignment: Alignment.centerLeft,
                              child: Transform.rotate(
                                  angle: -math.pi / 2,
                                  // alignment: Alignment.center,
                                  child: Center(
                                    child: Container(
                                      width: MediaQuery.of(context).size.width * 0.25,
                                      child:
                                          Text(
                                            couponRC.couponList.data![index].status == "1" ? "ใช้แล้ว" :checkExpire ? "หมดอายุ" : "ใช้คูปอง",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              color: Color(0xFF282828),
                                              fontWeight: FontWeight.w600,
                                                letterSpacing: 0
                                            ),
                                          )
                                    ),
                                  )

                              ),
                            ),
                          )
                        ],
                      )
                  ),
                  couponRC.couponList.data![index].status == "1" || checkExpire ?Container(
                    width: MediaQuery.of(context).size.width * 0.95,
                    child: SvgPicture.asset(
                      "assets/image/ticket/ticketRestaurant_Cefe.svg",
                      fit: BoxFit.fill,
                      color: Colors.white.withOpacity(0.75),
                    ),
                  ) : Container(),
                ],
              ),
            ),
          );
        }
    );
    // return Obx( () =>
    //
    // );
  }

  String formatThaiDate(String expire) {
    try {
      // แปลงจาก ISO 8601 → DateTime
      DateTime date = DateTime.parse(expire);

      // แปลง ค.ศ. → พ.ศ.
      int year = date.year + 543;
      int month = date.month;
      int day = date.day;

      // รายชื่อเดือนภาษาไทย
      List<String> thaiMonths = [
        "",
        "ม.ค.",
        "ก.พ.",
        "มี.ค.",
        "เม.ย.",
        "พ.ค.",
        "มิ.ย.",
        "ก.ค.",
        "ส.ค.",
        "ก.ย.",
        "ต.ค.",
        "พ.ย.",
        "ธ.ค."
      ];

      // คืนค่าวันที่ในรูปแบบ "12 ม.ค. 2568"
      return "$day ${thaiMonths[month]} $year";
    } catch (e) {
      return "รูปแบบวันที่ไม่ถูกต้อง"; // กรณี format ผิด
    }
  }

  bool isAvailableUse() {
    DateTime now = DateTime.now();

    // กำหนดเวลาเริ่ม (10:30 น.)
    DateTime start = DateTime(now.year, now.month, now.day, 10, 30);

    // กำหนดเวลาสิ้นสุด (16:30 น.)
    DateTime end = DateTime(now.year, now.month, now.day, 16, 00);

    // ตรวจสอบว่าเวลาปัจจุบันอยู่ระหว่าง start และ end หรือไม่
    return now.isAfter(start) && now.isBefore(end);
  }

}
