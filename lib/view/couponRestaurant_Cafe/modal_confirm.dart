import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_prachakij_v3/controller/couponRestaurant_Cafe/couponR_C.controller.dart';
import 'package:mapp_prachakij_v3/view/couponRestaurant_Cafe/modal_getCoupon.dart';

class AnimatedSheet extends StatefulWidget {
  @override
  _AnimatedSheetState createState() => _AnimatedSheetState();
}

class _AnimatedSheetState extends State<AnimatedSheet> {
  bool animateShow = false; // เปลี่ยนเป็นตัวแปรของ State

  var selectStore = 0;

  var couponRC = Get.find<CouponRCController>();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 100), () {
      setState(() {
        animateShow = true; // อัปเดตค่าใน State
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          /// ✅ **Background Overlay (จางๆ เมื่อเปิด)**
          AnimatedOpacity(
            duration: Duration(milliseconds: 300),
            opacity: animateShow ? 1.0 : 0.0,
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.black.withOpacity(0.5),
              ),
            ),
          ),
          /// ✅ **Animated Bottom Sheet**
          AnimatedPositioned(
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            bottom: animateShow ? 0 : -MediaQuery.of(context).size.height * 0.5,
            // ซ่อน-แสดง
            left: 0,
            right: 0,
            child: Obx(() => Container(
                width: MediaQuery.of(context).size.width,
                height: couponRC.isConfirmLoading.value == true ? MediaQuery.of(context).size.height * 0.35:MediaQuery.of(context).size.height * 0.5,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        SizedBox(
                          height: 5,
                        ),
                        Container(
                          width: 50,
                          height: 5,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ],
                    ),
                    Container(
                      width: 80,
                      child: Image.asset(
                          'assets/image/ticket/CouponLogoWithCheck.png'),
                    ),
                    Container(
                      height: couponRC.isConfirmLoading.value == true ? MediaQuery.of(context).size.height * 0.12 :MediaQuery.of(context).size.height * 0.3,
                      // color: Colors.red.withOpacity(0.5),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Container(
                            // color: Colors.red.withOpacity(0.5),
                            width: MediaQuery.of(context).size.width * 0.9,
                          height: couponRC.isConfirmLoading.value == true ? MediaQuery.of(context).size.height * 0.1 :MediaQuery.of(context).size.height * 0.075,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Text('ยืนยันใช้คูปองของขวัญ',
                                  style: TextStyle(
                                    fontSize: 17,
                                    fontWeight: FontWeight.w600,
                                    // color: const Color(0xFF2B1710),
                                  )),
                                Container(
                                  width: MediaQuery.of(context).size.width * 0.9,
                                  alignment: Alignment.center,
                                  child: Text(couponRC.isConfirmLoading.value == true ? 'กรุณารอสักครู่' :'กรุณาเลือกร้านที่คุณต้องการใช้คูปอง',
                                      style: TextStyle(
                                        fontSize: 15,
                                        wordSpacing: 25.0,
                                      )),
                                ),
                                if(couponRC.isConfirmLoading.value == true) Container(
                                  width: 30,
                                  child: LoadingIndicator(
                                    indicatorType: Indicator.ballPulseSync,
                                    colors: [Color(0xFFFFB100)],
                                    strokeWidth: 2,

                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          if (couponRC.isConfirmLoading == false)
                          Column(
                            children: [
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    selectStore = 1;
                                  });
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width * 0.95,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.grey.withOpacity(0.4),
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                width: 10,
                                              ),
                                              AnimatedContainer(
                                                duration: Duration(milliseconds: 300),
                                                width: 25,
                                                height: 25,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: selectStore == 1 ?Colors.orange : Colors.white,
                                                  border: Border.all(
                                                    color: Colors.black,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Icon(
                                                    Icons.check,
                                                    color: Colors.white,
                                                    size: 15,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                              Text("ร้านอาหารอร่อยทุกวัน",
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                  )),
                                            ],
                                          )),
                                      SizedBox(
                                        height: 35,
                                        width: 70,
                                        child: Image.asset(
                                            'assets/image/ticket/restaurantLogo.png'),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    selectStore = 2;
                                  });
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width * 0.95,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color: Colors.grey.withOpacity(0.4),
                                    ),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                width: 10,
                                              ),
                                              AnimatedContainer(
                                                duration: Duration(milliseconds: 300),
                                                width: 25,
                                                height: 25,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: selectStore == 2 ?Colors.orange : Colors.white,
                                                  border: Border.all(
                                                    color: Colors.black,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: Icon(
                                                    Icons.check,
                                                    color: Colors.white,
                                                    size: 15,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                              Text("ร้านกาแฟ CU Cafe",
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                  )),
                                            ],
                                          )),
                                      SizedBox(
                                        height: 35,
                                        width: 70,
                                        child:
                                        Image.asset('assets/image/ticket/CafeLogo.png'),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              InkWell(
                                onTap: () async {
                                  bool checkPass = await couponRC.confirmCouponUsed(selectStore);

                                  if(checkPass == true){
                                    Navigator.pop(context);

                                    showDialog(
                                        barrierColor: Colors.transparent,
                                        context: context,
                                        useSafeArea: false,
                                        builder: (_) => const ModalGetcoupon());
                                  }
                                },
                                child: Container(
                                  width: MediaQuery.of(context).size.width * 0.95,
                                  alignment: Alignment.center,
                                  child: Stack(
                                    children: [
                                      Container(
                                        width: MediaQuery.of(context).size.width * 0.95,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          gradient: LinearGradient(
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter,
                                            stops: const [0.0, 1.0],
                                            colors: [
                                              Color(0xFF000000).withOpacity(0.7),
                                              Color(0xFF000000),
                                            ],
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            'ใช้คูปอง',
                                            style: TextStyle(
                                              color: Color(0xFFFFB100),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600
                                            ),
                                          ),
                                        ),
                                      ),
                                      selectStore == 0 ?Container(
                                        width: MediaQuery.of(context).size.width * 0.95,
                                        height: 50,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(10),
                                          color: Colors.white.withOpacity(0.5),
                                        ),
                                      ) :Container(),
                                    ],
                                  ),
                                ),
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
