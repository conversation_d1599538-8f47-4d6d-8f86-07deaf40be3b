import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:lottie/lottie.dart';
import 'package:mapp_prachakij_v3/controller/couponRestaurant_Cafe/couponR_C.controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class ModalGetcoupon extends StatefulWidget {
  const ModalGetcoupon({super.key});

  @override
  State<ModalGetcoupon> createState() => _ModalGetcouponState();
}

class _ModalGetcouponState extends State<ModalGetcoupon> {
  bool animateShow = false; // เปลี่ยนเป็นตัวแปรของ State
  TextEditingController phoneController = TextEditingController();
  var couponRC = Get.find<CouponRCController>();

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 100), () {
      setState(() {
        animateShow = true; // อัปเดตค่าใน State
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        couponRC.wrongNumber.value = false;
        couponRC.storeName.value = "";
        return true;
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Stack(
          children: [
            /// ✅ **Background Overlay (จางๆ เมื่อเปิด)**
            AnimatedOpacity(
              duration: Duration(milliseconds: 300),
              opacity: animateShow ? 1.0 : 0.0,
              child: GestureDetector(
                onTap: () {
                  couponRC.wrongNumber.value = false;
                  couponRC.storeName.value = "";
                  Navigator.pop(context);
                },
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.black.withOpacity(0.5),
                ),
              ),
            ),

            /// ✅ **Animated Bottom Sheet**
            AnimatedPositioned(
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              bottom:
                  animateShow ? 0 : -MediaQuery.of(context).size.height * 0.5,
              // ซ่อน-แสดง
              left: 0,
              right: 0,
              child: Obx(
                () => Container(
                  width: MediaQuery.of(context).size.width,
                  height: couponRC.wrongNumber.value == true
                      ? (MediaQuery.of(context).size.height * 0.45) + 20
                      : MediaQuery.of(context).size.height * 0.45,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          SizedBox(
                            height: 5,
                          ),
                          Container(
                            width: 50,
                            height: 5,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        height: MediaQuery.of(context).size.height * 0.4,
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              couponRC.storeName.value == ""
                                  ? Container(
                                      width: 80,
                                      child: Image.asset(
                                          'assets/image/ticket/CouponLogoWithPlus.png'),
                                    )
                                  : Container(
                                      // color: Colors.red.withOpacity(0.5),
                                      child: Lottie.asset(
                                        'assets/animatedlogo/check_ring_round.json',
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.fill,
                                      ),
                                    ),
                              couponRC.storeName.value == ""
                                  ? Container(
                                      // color: Colors.red.withOpacity(0.5),
                                      height: couponRC.wrongNumber.value == true
                                          ? (MediaQuery.of(context)
                                                      .size
                                                      .height *
                                                  0.075) +
                                              20
                                          : MediaQuery.of(context).size.height *
                                              0.075,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Text('ต้องการรับคูปองของขวัญ',
                                              style: TextStyle(
                                                fontSize: 17,
                                                fontWeight: FontWeight.w600,
                                              )),
                                          Text(
                                              'กรอกข้อมูลเบอร์โทรศัพท์ของเจ้าของรถ',
                                              style: TextStyle(
                                                fontSize: 15,
                                              )),
                                          if (couponRC.wrongNumber.value ==
                                              true)
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.error_outline,
                                                  color: Colors.red,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Text(
                                                    'ไม่พบข้อมูลคูปองจากเบอร์โทรนี้ กรุณาลองใหม่',
                                                    style: TextStyle(
                                                      fontSize: 13,
                                                      color: Colors.red,
                                                    ))
                                              ],
                                            ),
                                        ],
                                      ),
                                    )
                                  : Container(
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.075,
                                      // color: Colors.red.withOpacity(0.5),
                                      child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            Text('คูปองถูกใช้เรียบร้อย',
                                                style: TextStyle(
                                                  fontSize: 17,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w600,
                                                )),
                                            Text(formatThaiDateTimeNow(),
                                                style: TextStyle(
                                                  color: Color(0xFF895F00),
                                                  fontSize: 14.sp,
                                                )),
                                          ]),
                                    ),
                              couponRC.storeName.value == ""
                                  ? Column(
                                      children: [
                                        Container(
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width *
                                              0.95,
                                          height: 50,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color:
                                                  Colors.grey.withOpacity(0.4),
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: TextField(
                                            controller: phoneController,
                                            textAlign: TextAlign.center,
                                            maxLength: 10,
                                            decoration: InputDecoration(
                                              hintText: 'เบอร์โทรศัพท์',
                                              hintStyle: TextStyle(
                                                fontSize: 16,
                                                color: Colors.black,
                                              ),
                                              border: InputBorder.none,
                                              contentPadding:
                                                  EdgeInsets.only(left: 10),
                                              counterText: '',
                                            ),
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              setState(() {});
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        InkWell(
                                          onTap: () async {
                                            FocusScope.of(context).unfocus();
                                            if (phoneController.text.length <
                                                10) {
                                              return;
                                            }
                                            await couponRC.cliamCouponRC(
                                                context, phoneController.text);
                                          },
                                          child: Container(
                                            width: MediaQuery.of(context)
                                                .size
                                                .width,
                                            alignment: Alignment.center,
                                            child: couponRC.isLoading.value ==
                                                    true
                                                ? Container(
                                                    width:
                                                        MediaQuery.of(context)
                                                                .size
                                                                .width *
                                                            0.95,
                                                    alignment: Alignment.center,
                                                    child: Container(
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.95,
                                                        height: 50,
                                                        decoration: BoxDecoration(
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        10),
                                                            color: Color(
                                                                0xFFEBEBEB)),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Center(
                                                              child: Text(
                                                                'รอสักครู่',
                                                                style:
                                                                    TextStyle(
                                                                  color: Colors
                                                                      .black,
                                                                  fontSize: 16,
                                                                      fontWeight: FontWeight.w600
                                                                ),
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              width: 10,
                                                            ),
                                                            Container(
                                                              width: 30,
                                                              child:
                                                                  LoadingIndicator(
                                                                indicatorType:
                                                                    Indicator
                                                                        .ballPulseSync,
                                                                colors: [
                                                                  Color(
                                                                      0xFFFFB100)
                                                                ],
                                                                strokeWidth: 2,
                                                              ),
                                                            )
                                                          ],
                                                        )),
                                                  )
                                                : Stack(
                                                    alignment: Alignment.center,
                                                    children: [
                                                      Container(
                                                        width: MediaQuery.of(
                                                                    context)
                                                                .size
                                                                .width *
                                                            0.95,
                                                        height: 50,
                                                        decoration:
                                                            BoxDecoration(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(10),
                                                          gradient:
                                                              LinearGradient(
                                                            begin: Alignment
                                                                .topCenter,
                                                            end: Alignment
                                                                .bottomCenter,
                                                            stops: const [
                                                              0.0,
                                                              1.0
                                                            ],
                                                            colors: [
                                                              Color(0xFF000000)
                                                                  .withOpacity(
                                                                      0.7),
                                                              Color(0xFF000000),
                                                            ],
                                                          ),
                                                        ),
                                                        child: Center(
                                                          child: Text(
                                                            'รับคูปอง',
                                                            style: TextStyle(
                                                              color: Color(
                                                                  0xFFFFB100),
                                                              fontSize: 16,
                                                              fontWeight: FontWeight.w600
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      phoneController
                                                                  .text.length <
                                                              10
                                                          ? Container(
                                                              width:
                                                                  MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width,
                                                              height: 50,
                                                              decoration:
                                                                  BoxDecoration(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            10),
                                                                color: Colors
                                                                    .white
                                                                    .withOpacity(
                                                                        0.5),
                                                              ),
                                                            )
                                                          : Container(),
                                                    ],
                                                  ),
                                          ),
                                        )
                                      ],
                                    )
                                  : Container(
                                      // color: Colors.red.withOpacity(0.5),
                                      height:
                                          MediaQuery.of(context).size.height *
                                              0.175,
                                      width: MediaQuery.of(context).size.width *
                                          0.95,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text('คูปองของขวัญ',
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    color: Color(0xFF895F00),
                                                  )),
                                              Text(
                                                  '${AppService.numberFormatNon0(int.parse(couponRC.couponList.data![couponRC.selectItemIndex].point.toString()))} PMSpoint',
                                                  style: TextStyle(
                                                      fontSize: 15,
                                                      color: Colors.black)),
                                            ],
                                          ),
                                          DottedLine(
                                              dashColor: const Color(0xFF000000)
                                                  .withOpacity(0.35),
                                              lineThickness: 1,
                                              dashRadius: 2,
                                              dashLength: 4.0),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text('ใช้กับ',
                                                  style: TextStyle(
                                                    fontSize: 15,
                                                    color: Color(0xFF895F00),
                                                  )),
                                              Text('${couponRC.storeName}',
                                                  style: TextStyle(
                                                      fontSize: 15,
                                                      color: Colors.black)),
                                            ],
                                          ),
                                          InkWell(
                                            onTap: () async {
                                              Navigator.pop(context);
                                            },
                                            child: Container(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.95,
                                              height: 50,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                gradient: LinearGradient(
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                  stops: const [0.0, 1.0],
                                                  colors: [
                                                    Color(0xFF000000)
                                                        .withOpacity(0.7),
                                                    Color(0xFF000000),
                                                  ],
                                                ),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  'ตกลง',
                                                  style: TextStyle(
                                                    color: Color(0xFFFFB100),
                                                    fontSize: 17,
                                                    fontWeight: FontWeight.w600,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      )),
                            ]),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String formatThaiDateTimeNow() {
    try {
      // ใช้เวลาปัจจุบัน
      DateTime date = DateTime.now();

      // แปลง ค.ศ. → พ.ศ.
      int year = date.year + 543;
      int month = date.month;
      int day = date.day;
      int hour = date.hour;
      int minute = date.minute;

      // รายชื่อเดือนภาษาไทย
      List<String> thaiMonths = [
        "",
        "ม.ค.",
        "ก.พ.",
        "มี.ค.",
        "เม.ย.",
        "พ.ค.",
        "มิ.ย.",
        "ก.ค.",
        "ส.ค.",
        "ก.ย.",
        "ต.ค.",
        "พ.ย.",
        "ธ.ค."
      ];

      // แปลงปีให้เหลือ 2 ตัวท้าย (เช่น 2568 → 68)
      String shortYear = (year % 100).toString().padLeft(2, '0');

      // แปลงเวลาให้อยู่ในรูปแบบ 09:22
      String time =
          "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}";

      // คืนค่าวันที่และเวลาในรูปแบบ "29 ม.ค. 68 - 09:22"
      return "$day ${thaiMonths[month]} $shortYear - $time";
    } catch (e) {
      return "รูปแบบวันที่ไม่ถูกต้อง";
    }
  }
}
