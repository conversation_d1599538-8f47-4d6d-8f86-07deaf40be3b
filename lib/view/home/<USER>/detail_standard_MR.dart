import 'package:dotted_line/dotted_line.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/benefit_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_referrel.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/recommend_MR.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:mapp_prachakij_v3/view/referral/referralQR.dart';
import 'package:mapp_prachakij_v3/view/referral/scan_qr_refCode.dart';
import 'package:pinput/pinput.dart';

import '../../../controller/psiController/psi_controller.dart';
import '../../../controller/responsive/mobile_body.dart';

class DetailStandardMRPage extends StatefulWidget {
  const DetailStandardMRPage({Key? key}) : super(key: key);

  @override
  State<DetailStandardMRPage> createState() => _DetailStandardMRPageState();
}

class _DetailStandardMRPageState extends State<DetailStandardMRPage> {
  int data = 3;
  int takeOffset = 0;

  final formatter = NumberFormat("#,###,###,###");
  var formatYear = DateFormat('yyyy');

  double remain = 0.0;
  // double maxRemian = 250000.0;
  double per = Get.width * 0.72;

  int numReferral = 0;

  final dataProfileCtl = Get.put(ProfileController());
  final referralMRCtl = Get.put(ReferralMRController());
  final mrCtl = Get.put(ReferralMRController());
  final registerCtl = Get.put(RegisterController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  String buttonText = "ดำเนินการต่อ"; // ข้อความปุ่มเริ่มต้น
  bool isLoading = false; // กำหนดสถานะโหลด
  bool isError = false; // ตรวจสอบว่ามีข้อผิดพลาดไหม
  String errorMessage = ""; // ข้อความผิดพลาด

  void _onSubmit() {
    String inputCode = registerCtl.refTextController.text.trim();
    String checkCode = "018814";

    if (inputCode.isNotEmpty) {
      if (isLoading == false) {
        // ✅ แก้ไขการตรวจสอบ isLoading
        setState(() {
          isLoading = true;
          isError = false;
        });
        print("isLoading: $isLoading");
        print("isError: $isError");
      }
      // Future.delayed(Duration(seconds: 1), () {  // ✅ ใช้ milliseconds แทน microseconds
      //   if (inputCode != checkCode) { // ❌ รหัสผิด
      //     setState(() {
      //       isLoading = false;
      //       isError = true;
      //       buttonText = "ดำเนินการต่อ";
      //       errorMessage = "รหัสผู้แนะนำไม่ถูกต้อง กรุณาลองใหม่"; // ✅ ปรับข้อความให้เข้าใจง่ายขึ้น
      //     });
      //   } else { // ✅ รหัสถูกต้อง
      //     setState(() {
      //       isLoading = false;
      //       isError = false;
      //     });
      //
      //     Get.snackbar(
      //       "สำเร็จ",
      //       "รหัสถูกต้อง! ดำเนินการต่อ...",
      //       snackPosition: SnackPosition.BOTTOM,
      //       backgroundColor: Colors.green.withOpacity(0.8),
      //       colorText: Colors.white,
      //     );
      //
      //     // ✅ สามารถใส่โค้ดไปหน้าถัดไปหรือปิด modal ได้ที่นี่
      //   }
      // });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "ProfileMarketingRepresentative");
    Future.delayed(const Duration(seconds: 1), () {
      getRankMR(dataProfileCtl.rankMR.rankCurrent.toString());
    });
  }

  @override
  void dispose() {
    registerCtl.refTextController.clear(); // ✅ ล้างค่าทุกครั้งที่ปิดหน้า
    super.dispose();
  }

  // getRankMR() async {
  //   setState(() {
  //     if (dataProfileCtl.rankMR.pointLikeCurrent! >= 250000) {
  //       remain = Get.width * 0.72;
  //     } else {
  //       (remain = (dataProfileCtl.rankMR.pointLikeCurrent! * per) / maxRemian);
  //     }
  //   });
  //   if (kDebugMode) {
  //     print('เสร็จ');
  //   }
  // }

  getRankMR(String rank) async {
    setState(() {
      int point = dataProfileCtl.rankMR.pointLikeCurrent ?? 0;
      double maxRemian = 250000.0; // ค่าเริ่มต้นสำหรับ Standard

      // กำหนด maxRemian ตามระดับ
      if (rank == "PLUS" || rank == "PRO") {
        maxRemian = 500000.0;
      }

      // กำหนดเงื่อนไข remain ตามระดับ
      int threshold = (rank == "STANDARD") ? 250000 : 500000;
      if (point >= threshold) {
        remain = Get.width * 0.72;
      } else {
        remain = (point * per) / maxRemian;
      }
    });
    if (kDebugMode) {
      print('เสร็จ');
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProfileController>(builder: (controller) {
      return Scaffold(
        body: Obx(() {
          if (referralMRCtl.isLoading.value) {
            return AppLoader.loaderWaitPage(context);
          } else {
            return Stack(
              alignment: Alignment.topCenter,
              children: <Widget>[
                Container(
                  width: Get.width,
                  height: Get.height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFEDEDED),
                        Color(0xFFF2F2F2),
                      ],
                    ),
                  ),
                  child: Align(
                      alignment: Alignment.topCenter,
                      child: Image.asset(
                        dataProfileCtl.rankMR.rankCurrent == "STANDARD"
                            ? 'assets/image/MR/Standard_BG.png'
                            : dataProfileCtl.rankMR.rankCurrent == "PLUS"
                                ? "assets/image/MR/Plus_BG.png"
                                : "assets/image/MR/Pro_BG.png",
                        // 'assets/image/MR/Standard_BG.png'
                      )),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                        top: Get.width < 500 ? 50 : 100,
                        right: 16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () {
                              // Get.find<PsiController>().getCarOwner();
                              // Navigator.push(context, MaterialPageRoute(builder: (context) => MobileBodyPage()));
                              Get.back();
                              // Get.to(() => const MobileBodyPage());
                            },
                            child: Container(
                              margin: EdgeInsets.only(
                                left: Get.width < 500 ? 18 : 54,
                              ),
                              height: 34,
                              width: 34,
                              alignment: Alignment.centerLeft,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: const Color(0xFFFFFFFF),
                                  width: 1,
                                ),
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.arrow_back_ios_new,
                                  color: Color(0xFF1A1818),
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                          Text(
                            'เพื่อนแนะนำเพื่อน',
                            style: TextStyle(
                              fontFamily: 'Prompt',
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              height: 1.0, // line-height
                              letterSpacing: 0.0, // letter-spacing
                              color: const Color(0xFF2B1710),
                              shadows: [
                                Shadow(
                                  color:
                                      const Color(0xFF000000).withOpacity(0.1),
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                )
                              ],
                            ),
                          ),
                          InkWell(
                            onTap: () {
                              buildInfo(context);
                            },
                            child: Container(
                              height: 34,
                              width: 34,
                              child: Center(
                                child: Image.asset(
                                  'assets/image/MR/alert_!.png',
                                  scale: 2,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    /// Card MR
                    Padding(
                      padding: EdgeInsets.only(top: 10, left: 16, right: 16),
                      child: Container(
                        height: controller.rankMR.pointLikeCurrent! > 500000
                            ? 360
                            : 342,
                        width: Get.width,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          // color: Colors.white,
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            stops: const [0, 1.0],
                            colors: [
                              const Color(0xFFFFFFFF),
                              const Color(0xFFFFFFFF).withOpacity(0.4),
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              offset: const Offset(0, 4),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                        child: Padding(
                          padding:
                              EdgeInsets.only(left: 14, right: 14, top: 14),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              /// ระดับ
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ระดับของคุณ',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF8A8A8A),
                                          fontSize: 12,
                                        ),
                                      ),
                                      SizedBox(
                                        height: 6,
                                      ),
                                      Row(
                                        children: [
                                          Container(
                                            height: 22,
                                            width: 22,
                                            child: Center(
                                              child: Image.asset(
                                                dataProfileCtl.rankMR
                                                            .rankCurrent ==
                                                        "STANDARD"
                                                    ? 'assets/image/MR/bronze_coins.png'
                                                    : dataProfileCtl.rankMR
                                                                .rankCurrent ==
                                                            "PLUS"
                                                        ? "assets/image/MR/gold_coins.png"
                                                        : "assets/image/MR/platinum_coins.png",
                                                // fit: BoxFit.contain
                                              ),
                                            ),
                                            // color: Colors.red,
                                          ),
                                          SizedBox(width: 4),
                                          Text(
                                            dataProfileCtl.rankMR.rankCurrent ==
                                                    "STANDARD"
                                                ? ' STANDARD'
                                                : dataProfileCtl.rankMR
                                                            .rankCurrent ==
                                                        "PLUS"
                                                    ? ' PLUS'
                                                    : "PRO",
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontFamily: 'Prompt',
                                              fontSize: 16,
                                              letterSpacing: 0.5,
                                              color: Color(0xff282828),
                                              height: 1,
                                              shadows: [
                                                Shadow(
                                                  color: Color(0xFF000000)
                                                      .withOpacity(0.2),
                                                  offset: Offset(0, 1),
                                                  blurRadius: 2,
                                                )
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),

                                          /// เปลี่ยนเงื่อนไขสำหรับแสดงเป็น   dataProfileCtl.rankMR.rankCurrent == "PRO"
                                          dataProfileCtl.rankMR.rankCurrent ==
                                                  "PRO"
                                              // dataProfileCtl.rankMR.rankCurrent == "STANDARD" && dataProfileCtl.rankMR.rankCurrent != "PLUS"
                                              ? Container(
                                                  height: 10,
                                                  width: 12,
                                                  child: Center(
                                                    child: Image.asset(
                                                      'assets/image/MR/Frame.png',
                                                      scale: 2,
                                                      // fit: BoxFit.contain
                                                    ),
                                                  ),
                                                  // color: Colors.red,
                                                )
                                              : Container(),
                                        ],
                                      )
                                    ],
                                  ),

                                  /// ผู้แนะนำ
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    // mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ผู้แนะนำคุณ',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF8A8A8A),
                                          fontSize: 12,
                                          // height: 1.5
                                        ),
                                      ),
                                      SizedBox(
                                        height: 6,
                                      ),
                                      dataProfileCtl.profile.value.ref_code_mr!
                                              .isNotEmpty
                                          ? Text(
                                              "MR${dataProfileCtl.profile.value.ref_code_mr!.substring(3)}",
                                              style: TextStyle(
                                                color: Color(0xFFA8A8A8),
                                                fontFamily: 'Prompt',
                                                fontSize: 12,
                                                // height: 1.5,
                                              ),
                                            )
                                          : InkWell(
                                              onTap: () {
                                                registerCtl.refTextController
                                                    .text = "";
                                                addRefCode(context);
                                                setState(() {});
                                              },
                                              child: Text(
                                                'เพิ่มผู้แนะนำ',
                                                style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontFamily: 'Prompt',
                                                    fontSize: 12,
                                                    color: Color(0xff895F00),
                                                    decoration: TextDecoration
                                                        .underline,
                                                    decorationColor:
                                                        Color(0xff895F00)),
                                              ),
                                            ),
                                    ],
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 8,
                              ),

                              /// หลอดแสดงคะแนน
                              Stack(
                                children: [
                                  Container(
                                    width: Get.width,
                                    height: 10,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30),
                                      color: const Color(0xFFD9D9D9),
                                    ),
                                  ),
                                  AnimatedContainer(
                                    width: remain + 2,
                                    // width: 250,
                                    height: 10,
                                    duration:
                                        const Duration(milliseconds: 1000),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(30),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          // stops: const [0, 1.0],
                                          colors: [
                                            const Color(0xFFFFC700),
                                            const Color(0xFFFFB100),
                                            const Color(0xFFFF9900),
                                          ],
                                        )
                                        // color: Colors.transparent,
                                        // boxShadow: [
                                        //   BoxShadow(
                                        //     color: const Color(0xFFFFB100).withOpacity(0.7),
                                        //     spreadRadius: 1,
                                        //     blurRadius: 2,
                                        //     offset: const Offset(0, 0), // changes position of shadow
                                        //   ),
                                        // ],
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 8,
                              ),

                              /// เช็คข้อความ
                              controller.rankMR.pointLikeCurrent! < 200000
                                  ? Text(
                                      'สะสมเพิ่มอีก ${formatter.format(250000 - controller.rankMR.pointLikeCurrent!)} PMSpoint เพื่อเป็นระดับ Plus',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontFamily: 'Prompt',
                                          fontSize: 12,
                                          // height: 1,
                                          color: Color(0xFF1A1818)),
                                    )
                                  : controller.rankMR.pointLikeCurrent! < 500000
                                      ? Text(
                                          'สะสมเพิ่มอีก ${formatter.format(500000 - controller.rankMR.pointLikeCurrent!)} PMSpoint เพื่อเป็นระดับ Pro',
                                          style: const TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontFamily: 'Prompt',
                                              fontSize: 12,
                                              // height: 1,
                                              color: Color(0xFF1A1818)),
                                        )
                                      : Text(
                                          "เก่งมาก..คุณมาถึงระดับ Pro ได้แล้ว ยินดีด้วย! แต่ก็สามารถ\nสะสมพอยท์ได้เรื่อยๆ น้า",
                                          style: TextStyle(
                                              fontWeight: FontWeight.normal,
                                              fontSize: 12,
                                              // height: 18 / 12, // คำนวณจาก line-height / font-size
                                              // letterSpacing: 0,
                                              fontFamily: "Prompt",
                                              color: Color(0xFF1A1818)),
                                        ),
                              SizedBox(
                                height: 8,
                              ),
                              RichText(
                                text: TextSpan(children: [
                                  const TextSpan(
                                    text: 'ระยะสถานะของคุณจะหมดอายุ : ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 12,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: '31 ธ.ค. ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 12,
                                    ),
                                  ),
                                  TextSpan(
                                    text: AppService.yearThaiDate(
                                            formatYear.format(DateTime.now()))
                                        .substring(2, 4),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 12,
                                    ),
                                  ),
                                ]),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              DottedLine(
                                direction: Axis.horizontal,
                                lineLength: double.infinity,
                                lineThickness: 0.3,
                                dashColor: const Color(0xFFA8A8A8),
                                dashGapLength: 3,
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              InkWell(
                                onTap: () {
                                  benefitMR(context);
                                  // Navigator.of(context).push(MaterialPageRoute(
                                  //   builder: (context) => const BenefitMRPage(),
                                  // ));
                                },
                                child: Container(
                                  height: 18,
                                  // color: Colors.red,
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    // mainAxisAlignment: MainAxisAlignment.center,
                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Align(
                                        alignment: Alignment.topCenter,
                                        child: Text(
                                          'สิทธิพิเศษการได้รับคะแนน',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF895F00),
                                            fontSize: 12,
                                          ),
                                          // textAlign: TextAlign.center,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 6,
                                      ),
                                      Container(
                                          // padding: const EdgeInsets.only(right: 20),
                                          height: 16,
                                          width: 16,
                                          // color: Colors.red,
                                          child: Center(
                                              child: Icon(
                                            Icons.error_outline_rounded,
                                            color: Color(0xFF895F00),
                                            size: 16,
                                          ))),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              DottedLine(
                                direction: Axis.horizontal,
                                lineLength: double.infinity,
                                lineThickness: 0.3,
                                dashColor: const Color(0xFFA8A8A8),
                                dashGapLength: 3,
                              ),
                              SizedBox(
                                height: 8,
                              ),

                              /// Ref.code Copy
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start, // ให้ทุกองค์ประกอบอยู่ตรงกลางแนวตั้ง
                                children: [
                                  Container(
                                    width: 172,
                                    height: 40,
                                    // color: Colors.red,
                                    child: InkWell(
                                      onTap: () {
                                        if (dataProfileCtl.profile.value.mrCode
                                                ?.isNotEmpty ?? false) {
                                          Clipboard.setData(ClipboardData(
                                              text: dataProfileCtl
                                                  .profile.value.mrCode!));
                                          Get.snackbar(
                                            "สำเร็จ",
                                            "คัดลอกสำเร็จ!",
                                            snackPosition: SnackPosition.TOP,
                                            backgroundColor:
                                                Colors.transparent, // สีเขียว
                                            colorText: Colors.black, // สีขาว
                                          );
                                        } else {
                                          Get.snackbar(
                                            "ผิดพลาด",
                                            "ไม่พบข้อมูล",
                                            snackPosition: SnackPosition.TOP,
                                            backgroundColor: const Color(
                                                0xFFF44336), // สีแดง
                                            colorText: const Color(
                                                0xFFFFFFFF), // สีขาว
                                          );
                                        }
                                      },
                                      child: Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'รหัสแนะนำ',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Prompt',
                                              color: Color(0xFF8A8A8A),
                                              fontSize: 12,
                                            ),
                                          ),
                                          SizedBox(
                                            width: 20,
                                          ),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.end,
                                            // mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                "MR${dataProfileCtl.profile.value.mrCode.toString().substring(3)}",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: 'Prompt',
                                                  color: Color(0xFF1A1818),
                                                  fontSize: 12,
                                                ),
                                              ),
                                              Text(
                                                'กดเพื่อคัดลอก',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.normal,
                                                  fontFamily: 'Prompt',
                                                  color: Color(0xFF8A8A8A),
                                                  fontSize: 10,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Padding(
                                            padding: EdgeInsets.only(top: 7.5,left: 12),
                                            child: Container(
                                              // color: Colors.teal,
                                              height: 18,
                                              width: 18,
                                              // alignment: Alignment.center, // ทำให้รูปอยู่ตรงกลางของ Container
                                              child: Image.asset(
                                                'assets/image/MR/fileCopy.png',
                                                scale: 2,
                                                color: Color(0xFF664701),
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      // Referralqr();
                                      // showModalBottomSheet(
                                      //   backgroundColor: Colors.red,
                                      //   useSafeArea: true,
                                      //     context: context, builder: (BuildContext context)=>
                                      //     Referralqr()
                                      // );
                                      // Navigator.push(
                                      //     context,
                                      //     MaterialPageRoute(
                                      //         builder: (context) =>
                                      //             Referralqr()));
                                      Get.to(()=>Referralqr());
                                    },
                                    child: Container(
                                      width: 120,
                                      height: 40,
                                      // color: Colors.green,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                          color: const Color(0xFFC8C8C8),
                                          width: 0.5,
                                        ),
                                        // color: Colors.green,
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment
                                            .center, // จัดให้อยู่ตรงกลาง
                                        children: [
                                          Container(
                                            height: 18,
                                            width: 18,
                                            alignment: Alignment
                                                .center, // จัดให้รูปอยู่ตรงกลาง
                                            child: Image.asset(
                                              'assets/image/MR/qr-code.png',
                                              scale: 2,
                                            ),
                                          ),
                                          SizedBox(width: 8), // เพิ่มระยะห่าง
                                          Text(
                                            "ชวนเพื่อน",
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontFamily: 'Prompt',
                                                color: Color(0xFF1A1818),
                                                fontSize: 12),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              DottedLine(
                                direction: Axis.horizontal,
                                lineLength: double.infinity,
                                lineThickness: 0.3,
                                dashColor: const Color(0xFFA8A8A8),
                                dashGapLength: 3,
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              Container(
                                // width: 315,
                                // color: Colors.red,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 180,
                                      // color: Colors.teal,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                             height: 18,
                                            child: Text(
                                              'คะแนน PMSPoint สะสม',
                                              style: TextStyle(
                                                fontWeight: FontWeight.normal,
                                                fontFamily: 'Prompt',
                                                color: Color(0xFF8A8A8A),
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                              height: 2,
                                          ),
                                          SizedBox(
                                            height: 27,
                                            child: Text(
                                              formatter.format(dataProfileCtl
                                                  .rankMR.pointLikeCurrent),
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontFamily: 'Prompt',
                                                // fontSize: 16,
                                                color: Color(0xFF1A1818),
                                                fontSize: 18,
                                                // height: 1.0
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 12,
                                    ),
                                    Align(
                                      // alignment: Alignment.centerLeft,
                                      child: Container(
                                        width: 97,
                                        // color: Colors.orange,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 18,
                                              child: Text(
                                                'เพื่อนที่คุณแนะนำ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.normal,
                                                  fontFamily: 'Prompt',
                                                  color: Color(0xFF8A8A8A),
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 2,
                                            ),
                                            Obx(() => SizedBox(
                                              height: 27,
                                              child: Text(
                                                    "${mrCtl.usageCount.value}", // ✅ แสดงค่าจำนวนเพื่อนที่แนะนำ
                                                    style: TextStyle(
                                                      fontWeight: FontWeight.bold,
                                                      fontFamily: 'Prompt',
                                                      color: Color(0xFF1A1818),
                                                      fontSize: 18,
                                                    ),
                                                  ),
                                            )),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 5,
                                      height: 40,
                                      // color: Colors.pink,
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 8,
                              ),
                              InkWell(
                                onTap: () {
                                  Get.to(() => const RecommendMRPage());
                                },
                                child: Container(
                                    // margin: const EdgeInsets.only(top: 16),
                                    width: Get.width,
                                    height: 40,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        gradient: LinearGradient(
                                          begin: Alignment.topCenter,
                                          end: Alignment.bottomCenter,
                                          colors: [
                                            Colors.black.withOpacity(0.7),
                                            Colors.black,
                                          ],
                                        )),
                                    child: Center(
                                      child: RichText(
                                        text: TextSpan(children: [
                                          TextSpan(
                                            text: 'แนะนำเพื่อน รับคะแนนเพิ่ม ',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Prompt',
                                              color: Color(0xFFFFFFFF)
                                                  .withOpacity(0.9),
                                              fontSize: 14,
                                            ),
                                          ),
                                          TextSpan(
                                            text: 'คลิก',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontFamily: 'Prompt',
                                              color: Color(0xFFFFB100),
                                              fontSize: 14,
                                            ),
                                          ),
                                        ]),
                                      ),
                                    )),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 16, top: 20, right: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'กิจกรรมแนะนำล่าสุด',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF1A1818),
                                  fontSize: 12,
                                  letterSpacing: 0.1,
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  Navigator.of(context).push(MaterialPageRoute(
                                    builder: (context) =>
                                        const historyReferralPage(),
                                  ));
                                },
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "ดูทั้งหมด",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF1A1818),
                                          fontSize: 12,
                                          letterSpacing: 0.1),
                                    ),
                                    SizedBox(
                                      width: 4,
                                    ),
                                    Container(
                                      height: 18,
                                      width: 18,
                                      child: Center(
                                        child: Icon(
                                          Icons.arrow_forward_ios_outlined,
                                          color: Color(0xFFB6B6B6),
                                          size: 13,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          referralMRCtl.referralMR.data!.isNotEmpty
                              ? buildListReferral()
                              : Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const SizedBox(height: 31),
                                    Center(
                                      child: Container(
                                        height: 49,
                                        width: 49,
                                        child: Image.asset(
                                          'assets/image/MR/icon_!.png',
                                          scale: 2,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 7,
                                    ),
                                    Center(
                                      child: Text(
                                        'ยังไม่มีรายการกิจกรรม\nแนะนำเพื่อนง่ายๆ สะสมคะแนนได้รับสิทธิประโยชน์ก่อนใคร',
                                        style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF8A8A8A),
                                            fontSize: 12,
                                            letterSpacing: 0.5,
                                            height: 2),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                          const SizedBox(height: 14),
                          // referralMRCtl.referralMR.data!.length > 3
                          //     ? InkWell(
                          //         onTap: () {
                          //           Navigator.of(context)
                          //               .push(MaterialPageRoute(
                          //             builder: (context) =>
                          //                 const historyReferralPage(),
                          //           ));
                          //         },
                          //         child: SizedBox(
                          //           width: 323,
                          //           height: 33,
                          //           child: Row(
                          //             mainAxisAlignment: MainAxisAlignment.end,
                          //             children: const [
                          //               Padding(
                          //                 padding: EdgeInsets.only(left: 20),
                          //                 child: Text(
                          //                   'ดูทั้งหมด',
                          //                   style: TextStyle(
                          //                     fontWeight: FontWeight.w500,
                          //                     fontFamily: 'Prompt-Medium',
                          //                     color: Color(0xFF282828),
                          //                     fontSize: 14,
                          //                   ),
                          //                 ),
                          //               ),
                          //               SizedBox(width: 5),
                          //               SizedBox(
                          //                 height: 30,
                          //                 child: Icon(
                          //                   Icons.arrow_forward_ios_outlined,
                          //                   color: Color(0xFF895F00),
                          //                   size: 13,
                          //                 ),
                          //               ),
                          //             ],
                          //           ),
                          //         ),
                          //       )
                          //     : Container(),
                          const Padding(padding: EdgeInsets.only(bottom: 20)),
                        ],
                      ),
                    ),
                  ],
                )
              ],
            );
          }
        }),
      );
    });
  }

  buildInfo(BuildContext context) {
    return showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (BuildContext context) => Container(
              height: 380,
              width: Get.width,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: [0, 1.0],
                  colors: [
                    Color(0xFFFFFFFF),
                    Color(0xFFF8F8F8),
                  ],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 15),
                    child: Center(
                      child: Container(
                        width: 44,
                        height: 5,
                        decoration: BoxDecoration(
                          color: Color(0XFF1A1818).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  Image.asset('assets/image/refCode/popUpRef.png',
                      width: 70, height: 70),
                  Padding(
                    padding: EdgeInsets.only(left: 16, right: 16),
                    child: Container(
                      width: Get.width,
                      // color: Colors.red,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'ตัวแทนแนะนำเพื่อน',
                            style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Prompt',
                                color: Color(0xFF1A1818),
                                fontSize: 16,
                                height: 24 / 12),
                          ),
                          const SizedBox(height: 10),
                          Text(
                            'เพียงสมัครเป็นตัวแทนกับเรา และร่วมทำกิจกรรม\nแนะนำเพื่อนตามเงื่อนไขต่างๆ ของประชากิจฯ',
                            style: TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt',
                                color: Color(0xFF1A1818).withOpacity(0.75),
                                fontSize: 14,
                                height: 1.5,
                                letterSpacing: 0.2),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 5),
                          RichText(
                              text: TextSpan(children: [
                            TextSpan(
                                text: 'ได้สิทธิประโยชน์มากกว่าใคร...',
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 14,
                                    height: 1.5,
                                    letterSpacing: 0.2)),
                            TextSpan(
                                text: 'และสามารถสร้าง',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 14,
                                    height: 1.5,
                                    letterSpacing: 0.2)),
                          ])),
                          const SizedBox(height: 5),
                          RichText(
                              text: TextSpan(children: [
                            TextSpan(
                                text: 'รายได้ ได้ง่ายๆ',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 14,
                                    height: 1.5,
                                    letterSpacing: 0.2)),
                            TextSpan(
                                text: ' บนมือถือ',
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 14,
                                    height: 1.5,
                                    letterSpacing: 0.2)),
                          ])),
                          const SizedBox(height: 26),
                          Container(
                            width: Get.width,
                            height: 54,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                // stops: const [0, 1.0],
                                colors: [
                                  Colors.black.withOpacity(0.7),
                                  Colors.black
                                ],
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () {
                                  Navigator.pop(context);
                                },
                                child: Center(
                                  child: Text(
                                    "เข้าใจแล้ว",
                                    style: TextStyle(
                                      color: Color(0xFFFFB100),
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Prompt',
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ));
  }

  benefitMR(BuildContext context) {
    return showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        // backgroundColor: Colors.transparent,
        builder: (BuildContext context) => Container(
              width: Get.width,
              height: Get.height * 0.935,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 1.0],
                  colors: [
                    const Color(0xFFFFFFFF),
                    const Color(0xFFF8F8F8),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: SingleChildScrollView(
                child: Container(
                  margin: EdgeInsets.only(
                    top: 15,
                    left: Get.width < 600 ? 18 : 54,
                    right: Get.width < 600 ? 18 : 54,
                  ),
                  width: Get.width,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Center(
                        child: Container(
                          width: 44,
                          height: 5,
                          decoration: BoxDecoration(
                            color: Color(0XFF1A1818).withOpacity(0.2),
                            borderRadius: BorderRadius.circular(100),
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: 52, left: 148, right: 148, bottom: 12),
                        child: Container(
                          width: 77,
                          height: 72,
                          child: Image.asset('assets/image/MR/Mascot.png'),
                        ),
                      ),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text: 'เป็นตัวแทนผู้แนะนำกับเรา ได้อะไร?',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 16,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 15),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text:
                                'เริ่มต้นจากลูกค้าสู่การเป็นตัวแทนผู้แนะนำลูกค้า MR หรือ ตัวแทน',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nทางการตลาดนั้นเอง สิทธิ์ประโยชน์และผลตอบแทนที่คุณสามารถ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nสร้างรายได้ให้กับตัวเอง จากกิจกรรมแนะนำลูกค้ากับประชากิจฯ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 25),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text: 'ยิ่งอยู่ในระดับที่สูง..',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: 'การได้รับได้รางวัล ก็ยิ่งได้มาก',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nเพิ่มระดับตัวแทนของคุณ เพื่อรับสิทธิประโยชน์ที่มากกว่า',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 15),
                      RichText(
                        text: TextSpan(children: [
                          const TextSpan(
                            text: 'MR ระดับ Pro',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: '\nมีคะแนน PMSpoint สะสม ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: formatter.format(mrCtl.resPro.accumulate),
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: ' พอยท์ขึ้นไป',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: '\n${mrCtl.resPro.detail}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 10),
                      RichText(
                        text: TextSpan(children: [
                          const TextSpan(
                            text: 'MR ระดับ Plus',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: '\nมีคะแนน PMSpoint สะสม ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: formatter.format(mrCtl.resPlus.accumulate),
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: ' พอยท์ขึ้นไป',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: '\n${mrCtl.resPlus.detail}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(height: 10),
                      RichText(
                        text: TextSpan(children: [
                          const TextSpan(
                            text: 'MR ระดับ Standard',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: '\nมีคะแนน PMSpoint สะสม',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '0 - ${formatter.format(mrCtl.resStandard.accumulate)} ',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          const TextSpan(
                            text: 'พอยท์',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: '\n${mrCtl.resStandard.detail}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text: 'สถานะระดับและการได้รับสิทธิพิเศษ',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: ' ตามเงื่อนไขตัวแทนผู้แนะนำ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nระยะเวลาสถานะระดับของผู้แนะนำนั้น จะมีอายุสถานะอยู่ภายในระยะ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nเวลา 1 ปี เท่านั้น และระดับจะมีการปรับเปลี่ยนใหม่ในปีถัดไป โดยการ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nคำนวนจากคะแนนสะสมที่ผู้แนะนำได้ทำไว้ในปีก่อนหน้า',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text: 'รวมถึง..',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: 'สิทธิพิเศษเกณฑ์การได้รับคะแนน',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: ' ผู้แนะนำจะยังคงได้รับ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: '\nตามสถานะที่ได้มีการปรับเปลี่ยนใหม่ไปด้วย',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text: ' ในปีถัดไป',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      RichText(
                        text: const TextSpan(children: [
                          TextSpan(
                            text: 'หมายเหตุ :',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF664701),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                ' คะแนนสะสมจากการร่วมกิจกรรม จะเริ่มคำนวณใหม่',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                          TextSpan(
                            text:
                                '\nทั้งหมด โดยค่าคะแนนจะเริ่มต้นที่ 0 พอยท์ เสมอ',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFF707070),
                              fontSize: 12,
                            ),
                          ),
                        ]),
                      ),
                    ],
                  ),
                ),
              ),
            ));
  }

  buildListReferral() {
    return GetBuilder<ReferralMRController>(builder: (refController) {
      numReferral = refController.referralMR.data!.length;
      List<Widget> list = [];
      for (int i = 0;
          i < refController.referralMR.data!.length && i < data;
          i++) {
        list.add(
          Container(
            margin: const EdgeInsets.only(top: 10),
            // width: Get.width * 0.9,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const [0, 1.0],
                colors: [
                  const Color(0xFFFFFFFF),
                  const Color(0xFFFFFFFF).withOpacity(0.4),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 4),
                  blurRadius: 10,
                ),
              ],
            ),
            child: Stack(
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 10, left: 13),
                  child: Image.asset(
                    'assets/image/MR/File_dock_duotone.png',
                    width: 30,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 10, left: 50, right: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            refController.referralMR.data![i].referral
                                .toString(),
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt',
                              color: Color(0xFF282828),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            AppService.dateEngToThaiMini(refController
                                .referralMR.data![i].createTime
                                .toString()),
                            style: const TextStyle(
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Prompt',
                              color: Color(0xFFA8A8A8),
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 170,
                        child: Text(
                          refController.referralMR.data![i].noteReferral == null
                              ? ""
                              : refController.referralMR.data![i].noteReferral
                                  .toString(),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: Color(0xFF707070),
                            fontSize: 12,
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          RichText(
                            text: refController.referralMR.data![i].referral ==
                                    "ออกรถใหม่"
                                ? TextSpan(
                                    children: [
                                      TextSpan(
                                        text: refController.referralMR.data![i]
                                                    .amountLikepoint ==
                                                null
                                            ? "0"
                                            : formatter.format(refController
                                                .referralMR
                                                .data![i]
                                                .amountLikepoint),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontFamily: 'Prompt-Medium',
                                          color: Color(0xFF895F00),
                                          fontSize: 11,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: ' พอยท์ ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF895F00),
                                          fontSize: 11,
                                        ),
                                      ),
                                      // TextSpan(
                                      //   text: refController.referralMR.data![i]
                                      //               .amountFiat ==
                                      //           null
                                      //       ? "0"
                                      //       : formatter.format(refController
                                      //           .referralMR
                                      //           .data![i]
                                      //           .amountFiat),
                                      //   style: const TextStyle(
                                      //     fontWeight: FontWeight.w700,
                                      //     fontFamily: 'Prompt-Medium',
                                      //     color: Color(0xFF895F00),
                                      //     fontSize: 11,
                                      //   ),
                                      // ),
                                      // const TextSpan(
                                      //   text: ' บาท',
                                      //   style: TextStyle(
                                      //     fontWeight: FontWeight.w400,
                                      //     fontFamily: 'Prompt',
                                      //     color: Color(0xFF895F00),
                                      //     fontSize: 11,
                                      //   ),
                                      // ),
                                    ],
                                  )
                                : TextSpan(
                                    children: [
                                      TextSpan(
                                        text: refController.referralMR.data![i]
                                                    .amountLikepoint ==
                                                null
                                            ? "0"
                                            : formatter.format(refController
                                                .referralMR
                                                .data![i]
                                                .amountLikepoint),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF895F00),
                                          fontSize: 12,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: ' พอยท์',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF895F00),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                          if (refController
                                  .referralMR.data![i].statusReferral ==
                              "pending") ...[
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'รอดำเนินการ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00).withOpacity(0.8),
                                      // letterSpacing: 0.1,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ] else if (refController
                                  .referralMR.data![i].statusReferral ==
                              "pending_review") ...[
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'รอตรวจสอบ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00).withOpacity(0.8),
                                      // letterSpacing: 0.1,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ] else if (refController
                                  .referralMR.data![i].statusReferral ==
                              "pending_payment") ...[
                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'รอทำการจ่าย',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00).withOpacity(0.8),
                                      letterSpacing: 0.1,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ] else if (refController
                                  .referralMR.data![i].statusReferral ==
                              "successful_transfer") ...[
                            RichText(
                              text: const TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'โอนจ่ายสำเร็จ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF5AB13B),
                                      fontSize: 12,
                                      letterSpacing: 0.1,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ] else if (refController
                                  .referralMR.data![i].statusReferral ==
                              "cancel") ...[
                            RichText(
                              text: const TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'รายการแนะนำไม่สำเร็จ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFFFF5757),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      }
      return Column(
        children: list,
      );
    });
  }

  buildCardMR() {
    return GetBuilder<ProfileController>(builder: (controller) {
      return Center(
        child: Container(
          width: Get.width,
          height: 195,
          margin: EdgeInsets.only(
            left: Get.width < 500 ? 18 : 54,
            right: Get.width < 500 ? 18 : 54,
          ),
          child: Stack(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 25),
                width: Get.width,
                height: 137,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0, 1.0],
                    colors: [
                      const Color(0xFFF3F3F3).withOpacity(0.9),
                      const Color(0xFFFFFFFF).withOpacity(0.6),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      left: 20,
                      top: 22,
                      child: Row(
                        children: [
                          Container(
                            height: 40,
                            width: 40,
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1.0,
                                    color: const Color(0xFF000000),
                                    style: BorderStyle.solid),
                                shape: BoxShape.circle),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(50.0),
                              child: Image(
                                image: controller
                                                .profile.value.profilePicture ==
                                            null ||
                                        controller
                                                .profile.value.profilePicture ==
                                            ""
                                    ? const AssetImage(
                                            'assets/image/home/<USER>')
                                        as ImageProvider
                                    : NetworkImage(
                                        "${controller.profile.value.profilePicture}"),
                                width: 50,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 500 ? Get.width * 0.05 : 320,
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                        margin: const EdgeInsets.only(top: 25, right: 24),
                        child: RichText(
                          text: const TextSpan(children: [
                            TextSpan(
                              text: 'ระดับ',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Prompt-Medium',
                                color: Color(0xFF895F00),
                                fontSize: 12,
                              ),
                            ),
                            TextSpan(
                              text: ' STANDARD',
                              style: TextStyle(
                                fontWeight: FontWeight.w700,
                                fontFamily: 'Prompt-Medium',
                                fontSize: 20,
                                letterSpacing: 0.4,
                                color: Color(0xff151423),
                              ),
                            ),
                          ]),
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.topCenter,
                      child: Container(
                          margin: const EdgeInsets.only(top: 55, left: 4),
                          child: Text(
                            ' รหัส : ${dataProfileCtl.profile.value.mrCode}',
                            style: const TextStyle(
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Prompt-Medium',
                                fontSize: 11,
                                color: Color(0xFF282828)),
                          )),
                    ),
                    Positioned(
                        top: 60,
                        left: 20,
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Stack(
                                  children: [
                                    Container(
                                      width: Get.width * 0.72,
                                      height: 5,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(30),
                                        color: const Color(0xFFD9D9D9),
                                      ),
                                    ),
                                    AnimatedContainer(
                                      width: remain,
                                      height: 5,
                                      duration:
                                          const Duration(milliseconds: 1000),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(30),
                                        color: Colors.transparent,
                                        boxShadow: [
                                          BoxShadow(
                                            color: const Color(0xFFFFB100)
                                                .withOpacity(0.7),
                                            spreadRadius: 1,
                                            blurRadius: 2,
                                            offset: const Offset(0,
                                                0), // changes position of shadow
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  width: 10,
                                ),
                                Column(
                                  children: [
                                    Image.asset(
                                      'assets/image/MR/PluscoinShadow.png',
                                      width: 20,
                                      height: 20,
                                    ),
                                    const Text(
                                      'ระดับ\nPLUS',
                                      style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt-Medium',
                                          fontSize: 8,
                                          color: Color(0xFF664701)),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ],
                        )),
                    Positioned(
                      top: 95,
                      left: 20,
                      child: Container(
                        child: controller.rankMR.pointLikeCurrent! < 200000
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'สะสมคะแนน เพิ่มอีก ${formatter.format(250000 - controller.rankMR.pointLikeCurrent!)} พอยท์ เพื่ออัพเกรดเป็น',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 11,
                                        color: Color(0xFF282828)),
                                  ),
                                  const Text(
                                    'ระดับ Plus',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 11,
                                        color: Color(0xFF282828)),
                                  )
                                ],
                              )
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: const [
                                  Text(
                                    'สะสมพคะแนนนเพิ่มอีก 0 เพื่ออัพเกรดเป็น',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 11,
                                        color: Color(0xFF282828)),
                                  ),
                                  Text(
                                    'ระดับ Plus',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 11,
                                        color: Color(0xFF282828)),
                                  )
                                ],
                              ),
                      ),
                    )
                  ],
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: Get.width,
                  height: 33,
                  decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(10),
                        bottomRight: Radius.circular(10),
                      ),
                      color: Colors.white),
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const BenefitMRPage(),
                      ));
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(left: 20),
                          child: Text(
                            'สิทธิพิเศษการได้รับคะแนน',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF895F00),
                              fontSize: 12,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.only(right: 20),
                          height: 30,
                          child: const Icon(
                            Icons.arrow_forward_ios_outlined,
                            color: Color(0xFF282828),
                            size: 13,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.topCenter,
                child: Image.asset(
                  'assets/image/MR/StandardcoinShadow.png',
                  width: 60,
                  height: 60,
                  alignment: Alignment.topCenter,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  void addRefCode(BuildContext context) {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          // left: 16,
          // right: 16,
          bottom: MediaQuery.of(context).viewInsets.bottom +
              20, // ปรับ UI ไม่ให้โดนบัง
        ),
        child: SingleChildScrollView(
          child: Container(
            height: 373,
            width: Get.width,
            // color: Colors.red,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 15),
                Container(
                  width: 44,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Color(0XFF1A1818).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(100),
                  ),
                ),
                SizedBox(height: 54),
                Container(
                    height: 70,
                    width: 75,
                    // color: Colors.red,
                    child: Center(
                        child: Image.asset(
                      'assets/image/refCode/popUpRef.png',
                      scale: 2,
                    ))),
                SizedBox(height: 16),
                Text(
                  'รหัสผู้แนะนำ',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 1.5,
                    color: Color(0xFF1A1818),
                    fontFamily: 'Prompt',
                  ),
                ),
                Text(
                  'กรอกรหัสผู้แนะนำ 6 หลัก ตัวอย่าง: 123456',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    height: 1.71,
                    color: Color(0xFF282828),
                    fontFamily: 'Prompt',
                  ),
                ),
                SizedBox(height: 14),
                _buildPinCodeFields(),
                SizedBox(height: 10),
                // แสดง Error Message ถ้ามี
                Obx(() => mrCtl.errorMessage.isNotEmpty
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.error_outline_rounded,
                              color: Colors.red, size: 18),
                          SizedBox(width: 6),
                          Text(
                            mrCtl.errorMessage.value, // ✅ ดึงค่าจาก RxString
                            style: TextStyle(
                              fontFamily: 'Prompt', // ✅ ใช้ฟอนต์ Prompt
                              fontWeight: FontWeight
                                  .w400, // ✅ น้ำหนักฟอนต์ 400 (Regular)
                              fontSize: 12, // ✅ ขนาด 12px
                              height: 1.0, // ✅ เทียบเท่า line-height: 12px;
                              letterSpacing: 0.0, // ✅ ไม่มี letter-spacing
                              color: Color(
                                  0xFFEB2227), // 🎨 เปลี่ยนสีได้ตามต้องการ
                              shadows: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(
                                      0.05), // ✅ #0000000D (5% opacity)
                                  offset: Offset(0, 4), // ✅ X = 0px, Y = 4px
                                  blurRadius: 10, // ✅ เบลอ 10px
                                  spreadRadius: 0, // ✅ กระจายเงา 0px
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      )
                    : SizedBox()),
                Spacer(),

                /// ปุ่มกด
                Padding(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 16,
                  ),
                  child: Obx(() => Opacity(
                        opacity: (registerCtl.refTextController.text
                                .isEmpty) // ✅ ถ้าไม่มีข้อความ และไม่ได้กำลังโหลด
                            ? 0.2
                            : 1, // ✅ ป้องกันการกดระหว่างโหลด
                        child: Container(
                          width: Get.width,
                          height: 54,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: mrCtl.loadingCheck.value
                                ? Color(
                                    0xFFEBEBEB) // ✅ เปลี่ยนเป็นสีเทาอ่อนเมื่อกด
                                : null, // ✅ ถ้าไม่ได้กด ให้ใช้ Gradient
                            gradient: mrCtl.loadingCheck.value
                                ? null // ❌ ปิด Gradient เมื่อกำลังโหลด
                                : LinearGradient(
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                    colors: [
                                      Colors.black.withOpacity(0.7),
                                      Colors.black,
                                    ],
                                  ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                offset: const Offset(0, 4),
                                blurRadius: 10,
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12),
                              // onTap: mrCtl.loadingCheck.value ? null : ()=>mrCtl.checkMRCode(context), // ✅ ป้องกันกดซ้ำถ้ายังโหลดอยู่
                              onTap: mrCtl.loadingCheck.value
                                  ? null
                                  : () => mrCtl.checkMRCodeForMRPage(
                                      context), // ✅ ป้องกันกดซ้ำถ้ายังโหลดอยู่
                              child: Center(
                                child: mrCtl.loadingCheck.value
                                    ? Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'รอสักครู่',
                                            style: TextStyle(
                                              color: Color(0xFF282828),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: 'Prompt',
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Container(
                                            width: 30,
                                            child: LoadingIndicator(
                                              indicatorType:
                                                  Indicator.ballPulseSync,
                                              colors: [Color(0xFFFFB100)],
                                              strokeWidth: 2,
                                            ),
                                          )
                                        ],
                                      ) // ✅ แสดง Loading
                                    : Text(
                                        "ดำเนินการต่อ",
                                        style: TextStyle(
                                          color: Color(0xFFFFB100),
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Prompt',
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ),
                      )),
                ),
                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    ).whenComplete(() {
      registerCtl.refTextController.clear();
      mrCtl.errorMessage.value = "";
    });
  }

  _buildPinCodeFields() {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 40,
      // color: Colors.red,
      // padding: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Pinput(
              separatorBuilder: (index) => SizedBox(width: 16),
              length: 6, // จำนวนหลักของรหัส
              controller: registerCtl.refTextController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly
              ], // รับเฉพาะตัวเลข
              showCursor: true, // เปิดให้แสดง Cursor
              cursor: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 2, // ความกว้างของ Cursor
                    height: 20, // ความสูงของ Cursor
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: Color(0xFFFF9300), // สีของ Cursor
                    ),
                  ),
                ],
              ),
              defaultPinTheme: PinTheme(
                width: 30, // ความกว้างของช่อง
                height: 40, // ความสูงของช่อง
                textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF1A1818)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFD9D8D5), width: 1),
                ),
              ),
              focusedPinTheme: PinTheme(
                width: 30,
                height: 40,
                textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Prompt',
                    color: Color(0xFF1A1818)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: const Color(0xFFD9D8D5), width: 1),
                  // border: Border.all(color: Colors.blue, width: 2), // เปลี่ยนสีขอบเมื่อ focus
                ),
              ),
              preFilledWidget: Text(
                "–", // ใช้ "–" เป็นค่าพื้นฐาน
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Prompt',
                  color: Color(0xFFD9D8D5), // สีของ placeholder
                ),
              ),
              onCompleted: (code) {
                registerCtl.refTextController.text = code;
                print("รหัสที่กรอก: $code");
                print("รหัสที่กรอก: ${registerCtl.refTextController.text}");
              },
              onChanged: (value) {
                if (value.isNotEmpty) {
                  mrCtl.errorMessage.value = ''; // ✅ ล้าง Error เมื่อมีการแก้ไข
                }
              },
            ),
          ),
          SizedBox(width: 16),
          // Container(
          //   color: Colors.red,
          //   width: 30,
          //   height: 40,),
          InkWell(
            onTap: () {
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => ScanQrRefcode()));
            },
            child: Container(
                width: 30,
                height: 40,
                // color: Colors.teal,
                child: Center(
                    child: Image.asset(
                  'assets/image/refCode/scanQr.png',
                  scale: 2,
                ))),
          ),
        ],
      ),
    );
  }
}
