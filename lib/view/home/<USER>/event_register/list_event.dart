import 'package:cached_network_image/cached_network_image.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

import 'detail_event_register.dart';

class ListEventPage extends StatefulWidget {
  const ListEventPage({Key? key}) : super(key: key);

  @override
  State<ListEventPage> createState() => _ListEventPageState();
}

class _ListEventPageState extends State<ListEventPage> {

  final evenAndNewsCtl  = Get.find<EventAndNewsController>();
  final promotionCtl = Get.find<PromotionController>();

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "EventList");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
          children: [
            Container(
              width: Get.width,
              height: Get.height,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFFEDEDED),
                    Color(0xFFF2F2F2),
                  ],
                  stops: [0.0, 1.0],
                ),
                // gradient: LinearGradient(
                //   begin: Alignment(0.0, -1.0),
                //   end: Alignment(0.0, 1.0),
                //   colors: [Color(0xff505050), Color(0xff282828)],
                //   stops: [0.0, 1.0],
                // ),
              ),
            ),
            Column(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  width: Get.width,
                  height: 90,
                  padding: const EdgeInsets.only(
                      left: 18,
                      right: 18
                  ),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFEDEDED),
                        Color(0xFFF2F2F2),
                      ],
                      stops: [0.0, 1.0],
                    ),
                    // gradient: LinearGradient(
                    //   begin: Alignment(0.0, -1.0),
                    //   end: Alignment(0.0, 1.0),
                    //   colors: [
                    //     Color(0xff505050),
                    //     Color(0xff282828)],
                    //   stops: [0.0, 1.0],
                    // ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: (){
                          Navigator.pop(context);
                        },
                        child:  Container(
                          width: 50,
                          height: 50,
                          alignment: Alignment.centerLeft,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: Color(0xFFFFB100),
                            size: 16,
                          ),
                        ),
                      ),
                      AppWidget.boldTextS(
                          context,
                          "กิจกรรม ร่วมสนุก..กับอีซูซุประชากิจฯ",
                          14,
                          Colors.black,
                          FontWeight.w400),
                      const SizedBox(
                        width: 50,
                        height: 50,
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                      itemCount: evenAndNewsCtl.eventList.data!.length,
                      itemBuilder: (BuildContext context, int index){
                        return buildContent(
                            context,
                            "${promotionCtl.urlEvent.value}${evenAndNewsCtl.eventList.data![index].informationPic}",
                          evenAndNewsCtl.eventList.data![index].nameActivity,
                            AppService.parseHtmlString(evenAndNewsCtl.eventList.data![index].informationBody.toString()),
                          index,
                        );
                      }
                  ),
                )
              ],
            ),
          ],
        )
    );
  }

  buildContent(context, photo, name, body, index,){
    return Container(
      margin: const EdgeInsets.all(18),
      child: CachedNetworkImage(
        imageUrl: photo,
        imageBuilder: (context, imageProvider) => Center(
          child: Container(
            width:  Get.width * 0.9,
            height:  Get.width * 0.9,
            decoration: BoxDecoration(
              image: DecorationImage(
                fit: BoxFit.fill,
                alignment: Alignment.topCenter,
                image: imageProvider,
              ),
              borderRadius: const BorderRadius.all(
                Radius.circular(
                    15
                ),
              ),
              border: Border.all(
                width: 2,
                color: const Color(0xFF000000),
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: Material(
                    color: const Color(0xFF000000).withOpacity(0.7),
                    borderRadius: const BorderRadius.all(
                      Radius.circular(
                          30
                      ),
                    ),
                    child: InkWell(
                      borderRadius: const BorderRadius.all(
                        Radius.circular(
                            20
                        ),
                      ),
                      splashColor: Colors.white,
                      onTap: () {
                        // print(evenAndNewsCtl.eventList.data![event]);
                        Map data = {
                          "id_activity" : evenAndNewsCtl.eventList.data![index].idActivity,
                          "information_pic" : evenAndNewsCtl.eventList.data![index].informationPic,
                          'information_name' : name,
                          "information_body" : body,
                          "fdate_regis_activity" : evenAndNewsCtl.eventList.data![index].fdateRegisActivity,
                          "edate_regis_activity" :evenAndNewsCtl.eventList.data![index].edateRegisActivity,
                          "fdate_activity" : evenAndNewsCtl.eventList.data![index].fdateActivity,
                          "edate_activity" : evenAndNewsCtl.eventList.data![index].edateActivity,
                          "LP_activity" : evenAndNewsCtl.eventList.data![index].lpActivity
                        };
                        Navigator.push(context,
                            MaterialPageRoute(
                              builder: (context) => DetailEventRegisterPage(data),
                            )
                        );
                      },
                      child: Container(
                        width: 85,
                        height: 32,
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(
                            Radius.circular(
                              30,
                            ),
                          ),

                          border: Border.all(
                            color: Colors.black.withOpacity(0.95),
                          ),
                        ),
                        child: const Center(
                          child: Text(
                            'ดูเพิ่มเติม',
                            style: TextStyle(
                              fontFamily: 'Prompt-Medium',
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        fit: BoxFit.cover,
        placeholder: (context, url) => const SizedBox(
          width: 50,
          height: 50,
          child: Center(
            child: CircularProgressIndicator(
              color: Colors.orange,
            ),
          ),
        ),
        errorWidget: (context, url, error) => const Icon(
          Icons.error,
          color: Color(0xFFFFB100),
        ),
      ),
    );
  }
}
