import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/news/news_view.dart';

class NewsListViewPage extends StatefulWidget {
  const NewsListViewPage({Key? key}) : super(key: key);

  @override
  State<NewsListViewPage> createState() => _NewsListViewPageState();
}

class _NewsListViewPageState extends State<NewsListViewPage> {


  final evenAndNewsCtl  = Get.find<EventAndNewsController>();
  final promotionCtl = Get.find<PromotionController>();

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "NewsListView");
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: Stack(
        children: [
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFEDEDED),
                  Color(0xFFF2F2F2),
                ],
                stops: [0.0, 1.0],
              ),
              // gradient: LinearGradient(
              //   begin: Alignment(0.0, -1.0),
              //   end: Alignment(0.0, 1.0),
              //   colors: [Color(0xff505050), Color(0xff282828)],
              //   stops: [0.0, 1.0],
              // ),
            ),
          ),
          Column(
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                width: Get.width,
                height: 90,
                padding: const EdgeInsets.only(
                    left: 18,
                    right: 18
                ),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFEDEDED),
                      Color(0xFFF2F2F2),
                    ],
                    stops: [0.0, 1.0],
                  ),
                  // gradient: LinearGradient(
                  //   begin: Alignment(0.0, -1.0),
                  //   end: Alignment(0.0, 1.0),
                  //   colors: [
                  //     Color(0xff505050),
                  //     Color(0xff282828)],
                  //   stops: [0.0, 1.0],
                  // ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: (){
                        Navigator.pop(context);
                      },
                      child:  Container(
                        width: 50,
                        height: 50,
                        alignment: Alignment.centerLeft,
                        child: const Icon(
                          Icons.arrow_back_ios,
                          color: Color(0xFFFFB100),
                          size: 16,
                        ),
                      ),
                    ),
                    AppWidget.boldTextS(
                        context,
                        "ข่าวสารทั่วไป / กิจกรรม",
                        14,
                        Colors.black,
                        FontWeight.w400),
                    const SizedBox(
                      width: 50,
                      height: 50,
                    )
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: evenAndNewsCtl.newsList.data!.length,
                  padding: EdgeInsets.zero,
                  itemBuilder: (BuildContext context, int index){
                    return buildContent(
                        context,
                        evenAndNewsCtl.newsList.data![index].informationId,
                        '${promotionCtl.urlEvent.value}${evenAndNewsCtl.newsList.data![index].informationPic}',
                        evenAndNewsCtl.newsList.data![index].informationName,
                        AppService.parseHtmlString(evenAndNewsCtl.newsList.data![index].informationBody.toString()),
                        evenAndNewsCtl.newsList.data![index].type,
                        evenAndNewsCtl.newsList.data![index].informationLink,
                        index
                    );
                  }
                ),
              )
            ],
          ),
        ],
      )
    );
  }

  buildContent(context, id, photo, name, body, type, link, index){
    return CachedNetworkImage(
      imageUrl: "$photo",
      imageBuilder: (context, imageProvider) => Container(
        width:  Get.width * 0.9,
        height:  Get.width * 0.9,
        margin: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          image: DecorationImage(
            fit: BoxFit.cover,
            alignment: Alignment.topCenter,
            image: imageProvider,
          ),
          borderRadius: const BorderRadius.all(
            Radius.circular(
              15
            ),
          ),
          border: Border.all(
            width: 2,
            color: const Color(0xFF000000),
          ),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 20,
              right: 20,
              child: Material(
                color: const Color(0xFF000000).withOpacity(0.7),
                borderRadius: const BorderRadius.all(
                  Radius.circular(
                    30
                  ),
                ),
                child: InkWell(
                  borderRadius: const BorderRadius.all(
                    Radius.circular(
                        30
                    ),
                  ),
                  splashColor: Colors.white,
                  onTap: () {
                    evenAndNewsCtl.indexNews.value = index;
                    Navigator.push(context,
                      MaterialPageRoute(
                          builder: (context) => NewsViewPage(),
                    )
                    );
                  },
                  child: Container(
                    width: 85,
                    height: 32,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(
                        Radius.circular(
                          30,
                        ),
                      ),
                      border: Border.all(
                        color: Colors.black,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        'ดูเพิ่มเติม',
                        style: TextStyle(
                          fontFamily: 'Prompt-Medium',
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.9),

                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      fit: BoxFit.cover,
      placeholder: (context, url) => const SizedBox(
        width: 50,
        height: 50,
        child: Center(
          child: CircularProgressIndicator(
            color: Colors.orange,
          ),
        ),
      ),
      errorWidget: (context, url, error) => const Icon(
        Icons.error,
        color: Color(0xFFFFB100),
      ),
    );
  }

}
