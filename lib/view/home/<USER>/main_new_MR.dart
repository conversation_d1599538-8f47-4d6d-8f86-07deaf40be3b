import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/show_detail_regis_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';

class mainNewMRPage extends StatefulWidget {
  const mainNewMRPage({Key? key}) : super(key: key);

  @override
  State<mainNewMRPage> createState() => _mainNewMRPageState();
}


class _mainNewMRPageState extends State<mainNewMRPage> {

  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    isLoading = false;
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;

    if (isLoading == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Scaffold(
            body: SingleChildScrollView(
              child: Stack(
                children: [
                  Container(
                    width: _width,
                    height: _height,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0, 1.0],
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                  ),

                  Column(
                    children: [
                      Stack(
                        children: [
                          SizedBox(
                            height: 200.h,
                            child: Row(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(left: 13.w),
                                  width: 255.w,
                                  child: Column(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 50.h,
                                            left: 10.w
                                        ),
                                        width: 245.w,
                                        height: 30.h,
                                        child: Row(
                                          children: [
                                            InkWell(
                                              onTap: (){
                                                Navigator.pushReplacement(context,
                                                    MaterialPageRoute(builder: (context) => const HomeNavigator()));
                                              },
                                              child: SizedBox(
                                                height: 30.h,
                                                child: Icon(Icons.arrow_back_ios_new,
                                                  color: const Color(0xFF282828),
                                                  size: 15.w,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 140.w,
                                              height: 40.h,
                                              child: Center(
                                                child: Text(
                                                  'ผู้แนะนำลูกค้า MR',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: 'Prompt-Medium',
                                                    color: Colors.black,
                                                    fontSize: 14.w,
                                                    letterSpacing: 0.4,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        margin: EdgeInsets.only(
                                            top: 20.h,
                                            left: 10.w
                                        ),
                                        width: 245.w,
                                        height: 95.h,
                                        child: RichText(
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'สิทธิพิเศษ!',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: 'Prompt-Medium',
                                                  color: const Color(0xFF282828),
                                                  fontSize: 14.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' สำหรับตัวแทนทางการตลาด',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF895F00),
                                                  fontSize: 12.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: '\nเพียงสมัครเป็นตัวแทนกับเรา และร่วมทำกิจกรรม',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' แนะนำลูกค้าตามเงี่อนไขต่างๆ ของประชากิจฯ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF707070),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: '\nได้รับสิทธิประโยนช์มากกว่าใคร...',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF895F00),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: 'และสามารถสร้าง \nรายได้ ได้ง่ายๆ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: 'Prompt-Medium',
                                                  color: const Color(0xFF895F00),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                              TextSpan(
                                                text: ' บนมือถือ',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFF895F00),
                                                  fontSize: 11.w,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            right: -3.w,
                            child: Image.asset('assets/image/MR/Lady.png',
                              width: 140.w,
                              height: 210.w,),
                          )
                        ],
                      ),
                      Container(
                        width: 330.w,
                        height: 70.h,
                        padding: EdgeInsets.zero,
                        child: RichText(
                          text: TextSpan(
                              children: [
                                TextSpan(
                                  text: 'สะสมพอยท์ เพื่อเพิ่มระดับตัวแทนของคุณ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: const Color(0xFF282828),
                                    fontSize: 12.w,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nกับคะแนนพอยท์ที่เป็นมากกว่าคะแนนสะสม ยิ่งแนะนำมาก สิทธิ์ในการได้รับ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF707070),
                                    fontSize: 11.w,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nคะแนนสะสม PMSpoint ก็ยิ่งเพิ่มขึ้น ตามระดับเงื่อนไขการเป็นตัวแทน',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF707070),
                                    fontSize: 11.w,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nผู้แนะนำลูกค้า MR',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: const Color(0xFF895F00),
                                    fontSize: 11.w,
                                  ),
                                ),
                              ]
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 15.h),
                        width: 330.w,
                        height: 60.h,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset('assets/image/home/<USER>',
                              width: 40.h,
                              height: 40.h,),
                            Text(
                              ' สิทธิพิเศษและระดับตัวแทน การได้รับคะแนนสะสม',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Prompt-Medium',
                                color: const Color(0xFF282828),
                                fontSize: 12.sp,
                              ),
                            )
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 16.h),
                        width: 323.w,
                        height: 60.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.h),
                          border: Border.all(
                              width: 1,
                              color: Colors.white
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.3),
                              offset: const Offset(1, 1),
                              blurRadius: 30,
                            ),
                          ],
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              const Color(0xFFFFFFFF).withOpacity(1),
                              const Color(0xFFF3F3F3).withOpacity(0.6),
                            ],
                          ),
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 20.w,
                            ),
                            Image.asset('assets/image/MR/ProcoinShadow.png',
                              width: 30.h,
                              height: 30.h,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'MR ระดับ Pro',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: const Color(0xFF282828),
                                    fontSize: 12.w,
                                  ),

                                ),
                                TextSpan(
                                  text: '\nรับคะแนนสูงสุด 50%',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF707070),
                                    fontSize: 11.w,
                                  ),
                                ),
                              ] ,
                            ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10.h),
                        width: 323.w,
                        height: 60.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.h),
                          border: Border.all(
                              width: 1,
                              color: Colors.white
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.3),
                              offset: const Offset(1, 1),
                              blurRadius: 30,
                            ),
                          ],
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              const Color(0xFFFFFFFF).withOpacity(1),
                              const Color(0xFFF3F3F3).withOpacity(0.6),
                            ],
                          ),
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 20.w,
                            ),
                            Image.asset('assets/image/MR/PluscoinShadow.png',
                              width: 30.h,
                              height: 30.h,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            RichText(
                              text: TextSpan(
                                children:[
                                  TextSpan(
                                    text: 'MR ระดับ Plus',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt-Medium',
                                      color: const Color(0xFF282828),
                                      fontSize: 12.w,
                                    ),

                                  ),
                                  TextSpan(
                                    text: '\nรับคะแนนสูงสุด 20%',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: const Color(0xFF707070),
                                      fontSize: 11.w,
                                    ),
                                  ),
                                ] ,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10.h),
                        width: 323.w,
                        height: 60.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.h),
                          border: Border.all(
                              width: 1,
                              color: Colors.white
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.3),
                              offset: const Offset(1, 1),
                              blurRadius: 30,
                            ),
                          ],
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              const Color(0xFFFFFFFF).withOpacity(1),
                              const Color(0xFFF3F3F3).withOpacity(0.6),
                            ],
                          ),
                        ),
                        child: Row(
                          children: [
                            SizedBox(
                              width: 20.w,
                            ),
                            Image.asset('assets/image/MR/StandardcoinShadow.png',
                              width: 30.h,
                              height: 30.h,
                            ),
                            SizedBox(
                              width: 20.w,
                            ),
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'MR ระดับ Standard',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: const Color(0xFF282828),
                                    fontSize: 12.w,
                                  ),

                                ),
                                TextSpan(
                                  text: '\nรับคะแนนตามเกณฑ์มาตรฐาน',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF707070),
                                    fontSize: 11.w,
                                  ),
                                ),
                              ] ,
                            ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 100.h),
                        width: 316.w,
                        height: 45.h,
                        child: RichText(
                          text: TextSpan(
                            children:[
                              TextSpan(
                                text: 'หมายเหตุ : ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  color: const Color(0xFFFF5757),
                                  fontSize: 11.w,
                                ),

                              ),
                              TextSpan(
                                text: 'สิทธิและพอยท์ที่จะได้รับ จะต้องมาจากกิจกรรมที่แนะนำ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: const Color(0xFF707070),
                                  fontSize: 10.5.w,
                                ),
                              ),
                              TextSpan(
                                text: '\nผลิตภัณฑ์ตามรายการที่กำหนดและเป็นไปตามเงื่อนไขของบริษัทเท่านั้น',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: const Color(0xFF707070),
                                  fontSize: 10.5.w,
                                ),
                              ),
                            ] ,
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: (){
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => ShowDetailRegisMR(),
                          ));
                        },
                        child: Container(
                            margin: EdgeInsets.only(top: 15.h),
                            width: 316.w,
                            height: 40.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5.h),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black45,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                              color: const Color(0xFF282828),
                            ),
                            child: Center(
                              child: RichText(
                                text: TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'เพิ่มระดับตัวแทนของคุณ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w100,
                                        fontFamily: 'Prompt',
                                        color: const Color(0xFFFFFFFF).withOpacity(0.9),
                                        fontSize: 14.w,
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' คลิก',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt-Medium',
                                        color: const Color(0xFFFFB100),
                                        fontSize: 14.w,
                                      ),
                                    ),
                                  ] ,
                                ),
                              ),
                            )
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }
}
