import 'dart:convert';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/component/firestore.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/save_activity.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/notification_detail.dart';
import 'package:mapp_prachakij_v3/view/signature/sig_detail.dart';

class NotificationListPage extends StatefulWidget {
  const NotificationListPage({Key? key}) : super(key: key);

  @override
  State<NotificationListPage> createState() => _NotificationListPageState();
}

class _NotificationListPageState extends State<NotificationListPage> {
  final SecureStorage secureStorage = SecureStorage();

  List<dynamic> notificationItems = [];
  String? phoneNumber;
  RxBool isLoading = true.obs;

  final profileCtl = Get.find<ProfileController>();
  final notificationCtl = Get.put(NotificationController());
  final saveActivityCtl = Get.put(SaveActivityController());
  // final EsignCtrl = Get.find<ESignatureController>();
  final EsignCtrl = Get.put(ESignatureController());

  var testTrue = true;
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  DateFormat dateFormat = DateFormat('d MMM y', 'th_TH');
  DateFormat timeFormat = DateFormat('HH:MM', 'th_TH');

  @override
  void initState() {
    super.initState();
    analytics.setCurrentScreen(screenName: "Notification");
    getData();
  }

  getData() async {
    await notificationCtl.getNotification();
    await EsignCtrl.getCurrentNoti(profileCtl.profile.value.mobile.toString());
    isLoading.value = false;
  }

  getNotification() async {
    var notification = await FireStore.getNotification(
        profileCtl.profile.value.mobile.toString());
    if (notification['status']) {
      setState(() {
        notificationItems = notification['data'];
        phoneNumber = profileCtl.profile.value.mobile;
      });
    } else {
      AppAlert.toastError('ผิดพลาด!! ดึงข้อมูลแจ้งเตือนไม่ได้');
    }
  }

  bool useTab = true;

  @override
  Widget build(BuildContext context) {
    return Obx(() => isLoading.isTrue
        ? AppLoader.loaderWaitPage(context)
        : Scaffold(
      backgroundColor: Colors.transparent,
      // floatingActionButton: Column(
      //   mainAxisAlignment: MainAxisAlignment.center,
      //   children: [
      //     FloatingActionButton(
      //       onPressed: () {
      //         EsignCtrl.margeArrayFunction();
      //       },
      //       child: const Icon(Icons.refresh),
      //       backgroundColor: const Color(0xFFFFB100),
      //     ),
      //   ],
      // ),
      body: Column(
        children: [
          buildTopContainer(),
          const SizedBox(
            height: 20,
          ),
          // Text with point title
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    useTab = true;
                  });
                },
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Container(
                        width: MediaQuery.of(context).size.width * 0.5,
                        height: 50,
                        decoration: useTab
                            ? const BoxDecoration(
                          color: Color(0xFFFFB100),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(20),
                          ),
                        )
                            : const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFFEDEDED),
                              // Color(0xFFF2F2F2),
                              Color(0xFFF9F9F9),
                            ],
                          ),
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(18),
                          ),
                        )),
                    useTab
                        ? Container(
                      width:
                      MediaQuery.of(context).size.width * 0.5,
                      height: 45,
                      decoration: const BoxDecoration(
                        color: Color(0xFFEDEDED),
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(18),
                        ),
                      ),
                    )
                        : Container(),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.5,
                      height: 50,
                      alignment: Alignment.center,
                      child: Text("งานบริการ",
                          style: TextStyle(
                              fontSize: 14,
                              color: useTab
                                  ? Colors.black
                                  : const Color(0x88282828))),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    useTab = false;
                  });
                },
                child: Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Container(
                        width: MediaQuery.of(context).size.width * 0.5,
                        height: 50,
                        decoration: !useTab
                            ? const BoxDecoration(
                          color: Color(0xFFFFB100),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(20),
                          ),
                        )
                            : const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFFEDEDED),
                              // Color(0xFFF2F2F2),
                              Color(0xFFF9F9F9),
                            ],
                          ),
                          border: Border(
                            right: BorderSide(
                              width: 1,
                              color: Color(0xDD282828),
                            ),
                          ),
                          borderRadius: BorderRadius.only(
                            bottomRight: Radius.circular(18),
                          ),
                        )),
                    !useTab
                        ? Container(
                      width:
                      MediaQuery.of(context).size.width * 0.5,
                      height: 45,
                      decoration: const BoxDecoration(
                        color: Color(0xFFEDEDED),
                        borderRadius: BorderRadius.only(
                          bottomRight: Radius.circular(18),
                        ),
                      ),
                    )
                        : Container(),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.5,
                      height: 50,
                      alignment: Alignment.center,
                      child: Text("สะสมพอยท์",
                          style: TextStyle(
                              fontSize: 14,
                              color: !useTab
                                  ? Colors.black
                                  : const Color(0x88282828))),
                    ),
                  ],
                ),
              ),
            ],
          ),
          useTab
              ? Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Column(
                    children: EsignCtrl.margeArrayShow.length == 0
                        ? [
                      Center(
                        child: Container(
                          child: Text("ไม่มีข้อมูล",
                              style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF707070))),
                        ),
                      ),
                    ]
                        : List.generate(
                        EsignCtrl.margeArrayShow!.length,
                            (index) => Column(
                          children: [
                            buildEsign(
                              EsignCtrl
                                  .margeArrayShow![index],
                              index,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                          ],
                        )),
                  ),
                ],
              ),
            ),
          )
              : Obx(() => Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: notificationCtl.notificationItems.length,
              itemBuilder: (BuildContext ctxt, int index) {
                return Column(
                  children: <Widget>[
                    buildContent(
                      notificationCtl.notificationItems[index],
                    ),
                  ],
                );
              },
            ),
          )),
          const SizedBox(
            height: 70,
          )
        ],
      ),
    ));
  }

  int focusIndex = 0;

  buildEsign(items, index) {
    String formattedDate = "";
    var typeDoc = "";
    var statusShow = false;
    final title = items["title"];
    final detail = items["detail"];

    var checkSignFormat = false;
    if (items.containsKey("create_time")) {
      formattedDate =
      "${dateFormat.format(DateTime.parse(items['create_time'].toString()))} / ${timeFormat.format(DateTime.parse(items['create_time'].toString()))} น.";

      String properJsonString = items["body"].replaceAll("'", '"');

      Map<String, dynamic> decodedJson = {};
      decodedJson = jsonDecode(properJsonString);
      typeDoc = decodedJson["typeDoc"];

      statusShow = (items["status_sign"] == null);
      checkSignFormat = true;
    } else if (items.containsKey("create")) {
      DateTime dateTime = (items["create"] as Timestamp).toDate();
      formattedDate =
      "${dateFormat.format(dateTime)} / ${timeFormat.format(dateTime)} น.";

      typeDoc = "";
      statusShow = items["read"] ?? false;
    }

    return Center(
      child: GestureDetector(
        onTap: () async {
          if(checkSignFormat){
            if (EsignCtrl.margeArrayShow![index]["status_sign"] == null) {
              if (focusIndex == index + 1) {
                focusIndex = 0;
              } else {
                focusIndex = index + 1;
              }
              setState(() {});
            } else {
              await EsignCtrl.setVariableForshow(
                  EsignCtrl.margeArrayShow![index],
                  EsignCtrl.margeArrayShow![index]['link_url'],
                  EsignCtrl.margeArrayShow![index]['status_sign'] != null);
              await EsignCtrl.changeIndexNow(index);
              await EsignCtrl.resetPutSign();
              Get.to(() => const SigDetail());
              setState(() {
                focusIndex = 0;
              });
            }
          }else{
            FireStore.updateReadNotification(
                profileCtl.profile.value.mobile.toString(), items['docId']);
            saveActivityCtl.saveActivity("เข้าชมการแจ้งเตือน", "", "");
            await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NotificationDetailPage(
                    items, profileCtl.profile.value.mobile.toString()),
              ),
            );
          }

        },
        child: AnimatedContainer(
          width: Get.width * 0.9,
          duration: const Duration(milliseconds: 500),
          decoration: BoxDecoration(
            color: statusShow ? const Color(0xFFFFFFFF) : Colors.transparent,
            borderRadius: BorderRadius.circular(15),
            boxShadow: statusShow
                ? [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.1),
                offset: const Offset(0, 5),
                blurRadius: 8.0,
              ),
            ]
                : [],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 20,
              ),
              Row(
                children: [
                  SizedBox(
                    width: 10,
                  ),
                  statusShow
                      ? Container(
                    width: 10,
                    height: 10,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 0.5, 1.0],
                        colors: [
                          Color(0xFFFFC700),
                          Color(0xFFFFB100),
                          Color(0xFFFF9900),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(50),
                    ),
                  )
                      : Container(),
                  statusShow
                      ? SizedBox(
                    width: 10,
                  )
                      : Container(),
                  Text(title,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF282828),
                          fontWeight: FontWeight.w600)),
                ],
              ),
              Container(
                width: Get.width * 0.9,
                child: Row(
                  children: [
                    SizedBox(
                      width: 10,
                    ),
                    Container(
                      width: Get.width * 0.8,
                      child: Text(
                        detail,
                        style: TextStyle(color: Color(0xFF707070)),
                        overflow: focusIndex - 1 == index
                            ? null
                            : TextOverflow.ellipsis,
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Container(
                width: Get.width * 0.9,
                child: Row(
                  children: [
                    SizedBox(
                      width: 10,
                    ),
                    Text(formattedDate,
                        style:
                        TextStyle(color: Color(0xFF895F00), fontSize: 12))
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
              focusIndex - 1 == index
                  ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () async {
                      if (checkSignFormat) {
                        final url;
                        if (items["status_sign"] != null) {
                          url = items['status_sign'];
                        } else {
                          url = items['link_url'];
                        }
                        await EsignCtrl.setVariableForshow(
                            items, url, items['status_sign'] != null);
                        await EsignCtrl.changeIndexNow(index);
                        await EsignCtrl.resetPutSign();
                        Get.to(() => const SigDetail());
                        focusIndex = 0;
                        setState(() {});
                      } else {
                        print("it not sign format");
                      }
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.35,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Color(0xFFE8E6E2),
                            Color(0xFFD9D8D5),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                              "assets/image/eSign/doc_search.svg",
                              height: 18,
                              fit: BoxFit.fitHeight),
                          SizedBox(width: 8),
                          Text(
                            statusShow
                                ? 'ดู' + typeDoc.toString()
                                : "ดูรายละเอียด",
                            style: TextStyle(
                                color: Color(0xFF282828), fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  statusShow
                      ? InkWell(
                    onTap: () async {
                      if (checkSignFormat) {
                        final url;
                        if (items["status_sign"] != null) {
                          url = items['status_sign'];
                        } else {
                          url = items['link_url'];
                        }
                        await EsignCtrl.setVariableForshow(items,
                            url, items['status_sign'] != null);
                        await EsignCtrl.changeIndexNow(index);
                        await EsignCtrl.resetPutSign();
                        Get.to(() => const SigDetail());
                        focusIndex = 0;
                        setState(() {});
                      } else {
                        print("it not sign format");
                      }
                    },
                    child: Container(
                      width:
                      MediaQuery.of(context).size.width * 0.35,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          stops: [0.0, 0.5, 1.0],
                          colors: [
                            Color(0xFFFFC700),
                            Color(0xFFFFB100),
                            Color(0xFFFF9900),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgPicture.asset(
                              "assets/image/eSign/Sign-box2.svg",
                              height: 18,
                              fit: BoxFit.fitHeight),
                          SizedBox(width: 8),
                          Text(
                            'เซนต์' + typeDoc.toString(),
                            style: TextStyle(
                                color: Color(0xFF000000),
                                fontSize: 14),
                          ),
                        ],
                      ),
                    ),
                  )
                      : Container(
                    width: MediaQuery.of(context).size.width * 0.35,
                    height: 50,
                  ),
                ],
              )
                  : Container(),
              focusIndex - 1 == index
                  ? SizedBox(
                height: 20,
              )
                  : Container(),
              statusShow
                  ? Container()
                  : Divider(
                color: Color(0xFFD9D9D9),
                thickness: 1,
                indent: 10,
                endIndent: 10,
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildTopContainer() {
    return Container(
      margin: EdgeInsets.only(
        top: 70,
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(
            width: 88,
          ),
          AppWidget.boldTextS(context, "การแจ้งเตือน", 16,
              const Color(0xFF2B1710), FontWeight.w400),
          InkWell(
              onTap: () async {
                var result = await AppAlert.showConfirm(
                  context,
                  'ลบข้อความทั้งหมด',
                  'คุณต้องการจะลบข้อความแจ้งเตือน\nทั้งหมด ใช่หรือไม่?',
                  'ลบทั้งหมด',
                );
                if (result == true) {
                  if (useTab) {
                    for (var i = 0; i < EsignCtrl.margeArrayShow.length; i++) {
                      if (EsignCtrl.margeArrayShow[i]["status_sign"] == null ||
                          EsignCtrl.margeArrayShow[i]["status_sign"] == "") {
                        var result = await AppAlert.showConfirm(
                            context,
                            'ลบข้อความทั้งหมด',
                            'คุณมีเอกสารที่ยังไม่ได้เซนต์',
                            'ตกลง');
                        if (result == true) {
                          setState(() {
                            focusIndex = i + 1;
                          });
                          return;
                        }
                      }
                    }
                    EsignCtrl.deleteAllNotiEsign();
                  } else {
                    await FireStore.deleteAllNotification(
                        profileCtl.profile.value.mobile.toString());
                    await notificationCtl.getNotification();
                  }
                }
              },
              child: Container(
                padding: const EdgeInsets.only(
                  top: 6,
                  bottom: 6,
                  right: 10,
                  left: 10,
                ),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      width: 1,
                      color: const Color(0xFF707070).withOpacity(0.4),
                    )),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/image/notification/notification_trash.png',
                      width: 11,
                      height: 14,
                    ),
                    const SizedBox(
                      width: 5,
                    ),
                    AppWidget.normalText(context, "ลบทั้งหมด", 12, Colors.black,
                        FontWeight.w400),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  buildContent(items) {
    return InkWell(
      onTap: () async {
        FireStore.updateReadNotification(
            profileCtl.profile.value.mobile.toString(), items['docId']);
        saveActivityCtl.saveActivity("เข้าชมการแจ้งเตือน", "", "");
        var result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => NotificationDetailPage(
                items, profileCtl.profile.value.mobile.toString()),
          ),
        );
      },
      child: Container(
        width: Get.width,
        height: 90,
        margin: EdgeInsets.only(
          left: Get.width < 500 ? 18 : 54,
          right: Get.width < 500 ? 18 : 54,
          bottom: 14,
        ),
        padding: const EdgeInsets.only(
          left: 18,
          right: 18,
          top: 8,
          bottom: 8,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: const [0.0, 1.0],
            colors: [
              items['read'] != true
                  ? const Color(0xFFF3F3F3)
                  : const Color(0xFFF3F3F3).withOpacity(0.5),
              items['read'] != true
                  ? const Color(0xFFFFFFFF)
                  : const Color(0xFFFFFFFF).withOpacity(0.5),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF000000).withOpacity(0.1),
              offset: const Offset(0, 3),
              blurRadius: 3.0,
            ),
          ],
          borderRadius: const BorderRadius.all(
            Radius.circular(15),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                items["read"] != true
                    ? Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.0, 0.5, 1.0],
                      colors: [
                        Color(0xFFFFC700),
                        Color(0xFFFFB100),
                        Color(0xFFFF9900),
                      ],
                    ),
                  ),
                )
                    : const SizedBox(),
                SizedBox(
                  width: items["read"] != true ? 14 : 0,
                ),
                SizedBox(
                    width: 240,
                    child: Text(
                      "${items['title']}",
                      style: const TextStyle(
                        fontSize: 14,
                        fontFamily: "Prompt",
                        fontWeight: FontWeight.w400,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ))
              ],
            ),
            SizedBox(
              width: 270,
              child: Text(
                items['title'],
                style: const TextStyle(
                    fontSize: 12,
                    fontFamily: "Prompt",
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF6A6A6A)),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            AppWidget.normalTextS(context, AppService.dateThai(items['create']),
                12, const Color(0xFF895F00), FontWeight.w400),
          ],
        ),
      ),
    );
  }
}