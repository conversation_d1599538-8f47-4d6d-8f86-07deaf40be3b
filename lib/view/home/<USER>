import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_vibrate/flutter_vibrate.dart';
import 'package:mapp_prachakij_v3/component/version.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class GetQrCodePage extends StatefulWidget {
  const GetQrCodePage({super.key});

  @override
  _GetQrCodePageState createState() => _GetQrCodePageState();
}

class _GetQrCodePageState extends State<GetQrCodePage> {
  final _textController = TextEditingController();
  String qrText = '';
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  // late MobileScannerController controller = MobileScannerController(
  //   formats: const [BarcodeFormat.qrCode],
  // );

  int? userId;
  String? phone;

  DateTime? dateTime;
  Duration? duration;

  String os = '';

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();

    dateTime = DateTime.now();
    duration = const Duration(minutes: 10);

    analytics.setCurrentScreen(screenName: "Get QRCode");

    getOS();

    _textController.addListener(() {
      // Vibrate.feedback(FeedbackType.success);
      Navigator.of(context).pop(_textController.text);
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    controller?.dispose();
    super.dispose();
  }

  getOS() async {
    try {
      var os = AppVersion.getOS();
      setState(() {
        os = os;
      });
    } catch (e) {
      AppService.sendError(e, 'getOS in GETQrCode');
    }
  }

  @override
  Widget build(BuildContext context) {
    final scanWindow = Rect.fromCenter(
      center: MediaQuery.sizeOf(context).center(Offset.zero),
      width: 200,
      height: 200,
    );

    return Scaffold(
      body: Container(
        width: 1.sw,
        height: 1.sh,
        color: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: QRView(
                key: qrKey,
                onQRViewCreated: (controller) {
                  this.controller = controller;
                  controller.scannedDataStream.listen((scanData) {
                    if (scanData.code != null) {
                      setState(() {
                        _textController.text = scanData.code!;
                      });
                      controller.pauseCamera(); // หยุดสแกนหลังจากได้ค่า
                      Navigator.of(context).pop(scanData.code);
                    }
                  });
                },
              ),
            ),
            // Center(
            //   child: MobileScanner(
            //     controller: controller,
            //     scanWindow: scanWindow,
            //     // overlay: CustomOverlay(),
            //     onDetect: (BarcodeCapture barcodes) {
            //       var barcode = barcodes.barcodes.first.rawValue.toString();
            //       _textController.text = barcode;
            //
            //       if (barcodes.barcodes.isNotEmpty) {
            //         Vibrate.feedback(FeedbackType.success);
            //         Navigator.of(context).pop(qrText);
            //       }
            //     },
            //   ),
            // ),
            buildTopContainer(),
            buildBottomContainer(),
            Positioned(
              bottom: MediaQuery.of(context).size.height * 0.06,
              right: MediaQuery.of(context).size.height * 0.02,
              child: Image.asset(
                'assets/image/mascot.png',
                width: 0.35.sw,
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildTopContainer() {
    return Container(
      width: 1.sw,
      height: 0.14.sh,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFFFFB100),
            const Color(0xFFFFB100).withOpacity(0.8),
            const Color(0xFFE3E1DE).withOpacity(0.4),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF000000).withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Container(
        margin: EdgeInsets.only(
          top: MediaQuery.of(context).size.height * 0.065,
          left: MediaQuery.of(context).size.width * 0.055,
          right: MediaQuery.of(context).size.width * 0.055,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: SizedBox(
                width: MediaQuery.of(context).size.height * 0.035,
                height: MediaQuery.of(context).size.height * 0.035,
                child: Icon(
                  Icons.arrow_back_ios_new_outlined,
                  size: MediaQuery.of(context).size.height * 0.02,
                ),
              ),
            ),
            Column(
              children: [
                Text(
                  'สแกน QR Code',
                  style: TextStyle(
                    fontFamily: 'Prompt-Medium',
                    color: const Color(0xFF000000),
                    fontWeight: FontWeight.w500,
                    fontSize: MediaQuery.of(context).size.height * 0.018,
                    letterSpacing: 0.4,
                    shadows: <Shadow>[
                      Shadow(
                        offset: const Offset(0, 1),
                        blurRadius: 5.0,
                        color: const Color(0xFF000000).withOpacity(0.15),
                      ),
                    ],
                  ),
                ),
                Text(
                  'ลงทะเบียนกิจกรรมกับประชากิจฯ',
                  style: TextStyle(
                    fontFamily: 'Prompt',
                    color: const Color(0xFFFFFFFF),
                    fontWeight: FontWeight.w400,
                    fontSize: MediaQuery.of(context).size.height * 0.0135,
                    letterSpacing: 0.4,
                  ),
                ),
              ],
            ),
            SizedBox(
              width: MediaQuery.of(context).size.height * 0.035,
              height: MediaQuery.of(context).size.height * 0.035,
            ),
          ],
        ),
      ),
    );
  }

  buildBottomContainer() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: SizedBox(
        width: MediaQuery.of(context).size.width * 1,
        height: MediaQuery.of(context).size.height * 0.2,
        child: ClipRect(
          child: BackdropFilter(
            filter: os == 'android'
                ? ImageFilter.blur(sigmaX: 10, sigmaY: 10)
                : ImageFilter.blur(sigmaX: 0, sigmaY: 0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Container(
                margin: EdgeInsets.only(
                  left: 0.1.sw,
                  top: 0.03.sh,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'กรุณาเช็คตำแหน่งคิวอาร์โค้ด',
                      style: TextStyle(
                        fontFamily: 'Prompt-Medium',
                        color: const Color(0xFFFFFFFF),
                        fontWeight: FontWeight.w500,
                        fontSize: 16.sp,
                        letterSpacing: 0.4,
                        shadows: <Shadow>[
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: 5.0,
                            color: const Color(0xFF000000).withOpacity(0.1),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.01,
                    ),
                    Text(
                      'ให้อยู่ในกรอบที่เหมาะสม เพื่อทำการสแกน',
                      style: TextStyle(
                        fontFamily: 'Prompt',
                        fontWeight: FontWeight.w400,
                        color: const Color(0xFFFFFFFF),
                        fontSize:
                        MediaQuery.of(context).size.height * 0.014,
                        letterSpacing: 0.4,
                        shadows: <Shadow>[
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: 5.0,
                            color: const Color(0xFF000000).withOpacity(0.1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CustomOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final double borderLength = 30;
    final double borderWidth = 5;
    final double cutOutSize = 300;
    final double borderRadius = 15;
    final Color borderColor = Color(0xFFFFB100);
    final double cutOutBottomOffset = 0.1.sw;

    return Stack(
      children: [
        Align(
          alignment: Alignment.center,
          child: Container(
            width: cutOutSize,
            height: cutOutSize,
            margin: EdgeInsets.only(bottom: cutOutBottomOffset),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                  color: Colors.transparent,
                  width: 0), // Make the whole area transparent
            ),
            child: Stack(
              children: [
                // Top left corner
                Positioned(
                  left: 0,
                  top: 0,
                  child: CustomPaint(
                    size: Size(borderLength, borderLength),
                    painter: CornerPainter(
                      borderColor: borderColor,
                      borderWidth: borderWidth,
                      isTopLeft: true,
                    ),
                  ),
                ),
                // Top right corner
                Positioned(
                  right: 0,
                  top: 0,
                  child: CustomPaint(
                    size: Size(borderLength, borderLength),
                    painter: CornerPainter(
                      borderColor: borderColor,
                      borderWidth: borderWidth,
                      isTopLeft: false,
                    ),
                  ),
                ),
                // Bottom left corner
                Positioned(
                  left: 0,
                  bottom: 0,
                  child: CustomPaint(
                    size: Size(borderLength, borderLength),
                    painter: CornerPainter(
                      borderColor: borderColor,
                      borderWidth: borderWidth,
                      isTopLeft: true,
                      isBottom: true,
                    ),
                  ),
                ),
                // Bottom right corner
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: CustomPaint(
                    size: Size(borderLength, borderLength),
                    painter: CornerPainter(
                      borderColor: borderColor,
                      borderWidth: borderWidth,
                      isTopLeft: false,
                      isBottom: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class CornerPainter extends CustomPainter {
  final Color borderColor;
  final double borderWidth;
  final bool isTopLeft;
  final bool isBottom;

  CornerPainter({
    required this.borderColor,
    required this.borderWidth,
    this.isTopLeft = true,
    this.isBottom = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = borderColor
      ..strokeWidth = borderWidth
      ..style = PaintingStyle.stroke;

    final path = Path();
    if (isTopLeft && !isBottom) {
      path.moveTo(0, size.height);
      path.lineTo(0, 0);
      path.lineTo(size.width, 0);
    } else if (!isTopLeft && !isBottom) {
      path.moveTo(size.width, size.height);
      path.lineTo(size.width, 0);
      path.lineTo(0, 0);
    } else if (isTopLeft && isBottom) {
      path.moveTo(0, 0);
      path.lineTo(0, size.height);
      path.lineTo(size.width, size.height);
    } else {
      path.moveTo(size.width, 0);
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
