import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/benefit_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/history/history_referrel.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/recommend_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:intl/intl.dart';

class DetailProMRPage extends StatefulWidget {
  const DetailProMRPage({Key? key}) : super(key: key);

  @override
  State<DetailProMRPage> createState() => _DetailProMRPageState();
}

class _DetailProMRPageState extends State<DetailProMRPage> {
  int data = 3;
  int takeOffset = 0;

  final formatter = NumberFormat("#,###,###,###");
  var formatYear = DateFormat('yyyy');

  int numReferral = 0;

  double remain = 0.0;
  double maxRemian = 500000.0;
  double per = Get.width * 0.72;

  bool isLoading = true;

  final dataProfileCtl = Get.put(ProfileController());
  final referralMRCtl = Get.put(ReferralMRController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "ProfileMarketingRepresentative");
    Future.delayed(const Duration(seconds: 1),() {
      getRankMR();
    });
  }


  getRankMR() async {
    setState(() {
      if(dataProfileCtl.rankMR.pointLikeCurrent! >= 500000){
        remain = Get.width * 0.72;
      } else {
        (remain = (dataProfileCtl.rankMR.pointLikeCurrent! * per)/maxRemian);
      }
    });
  }

  @override
  Widget build(BuildContext context) {

    if (referralMRCtl.isLoading.value == true) {
      return AppLoader.loaderWaitPage(context);
    }

    return Scaffold(
      body: Stack(
        children: <Widget>[
          Container(
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: [0, 1.0],
                colors: [
                  Color(0xFFEDEDED),
                  Color(0xFFF2F2F2),
                ],
              ),
            ),
          ),
          Image.asset('assets/image/MR/Pro_BG.png'),
          Positioned(
            top: Get.width < 500 ? 65 : 100,
            right: Get.width < 500 ? -15 : -15,
            child: Image.asset('assets/image/MR/LadyBlack.png',
              width: 160,
              height: 129,),
          ),
          SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  width: Get.width,
                  margin: const EdgeInsets.only(
                      top: 55
                  ),
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          InkWell(
                            onTap: (){
                              Get.to(() => const HomeNavigator());
                            },
                            child: Container(
                              margin: EdgeInsets.only(left: Get.width < 500 ? 18 : 54,
                              ),
                              height: 50,
                              width: 50,
                              alignment: Alignment.centerLeft,
                              child: const Icon(Icons.arrow_back_ios_new,
                                color: Color(0xFF282828),
                                size: 16,
                              ),
                            ),
                          ),
                          SizedBox(
                            width: Get.width < 500 ? 0 : 20,
                          ),
                          RichText(
                            text: const TextSpan(
                                children: [
                                  TextSpan(
                                    text: 'ข้อมูลตัวแทนผู้แนะนำ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Prompt-Medium',
                                      color: Color(0xFF282828),
                                      fontSize: 14,
                                    ),
                                  ),
                                ]
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      buildCardMR(),
                      Container(
                        margin: EdgeInsets.only(
                          top: 16,
                          left: Get.width < 500 ? 18 : 54,),
                        width: Get.width,
                        child: const Text('PMSpoint ของคุณ',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Prompt-Medium',
                            color: Color(0xFF282828),
                            fontSize: 14,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  width: Get.width,
                  margin: EdgeInsets.only(
                    left: Get.width < 500 ? 18 : 54, right: Get.width < 500 ? 18 : 54,),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                            children: [
                              TextSpan(
                                text: formatter.format(dataProfileCtl.rankMR.pointLikeCurrent),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF895F00),
                                  fontSize: 28,
                                ),
                              ),
                              const TextSpan(
                                text: ' พอยท์',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF1D1400),
                                  fontSize: 15,
                                ),
                              ),
                            ]
                        ),
                      ),
                      RichText(
                        text: TextSpan(
                            children: [
                              const TextSpan(
                                text: 'ระยะสถานะของคุณจะหมดอายุ : ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF895F00),
                                  fontSize: 12,
                                ),
                              ),
                              const TextSpan(
                                text: '31 ธ.ค. ',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF895F00),
                                  fontSize: 12,
                                ),
                              ),
                              TextSpan(
                                text: AppService.yearThaiDate(formatYear.format(DateTime.now())).substring(2,4),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF895F00),
                                  fontSize: 12,
                                ),
                              ),
                            ]
                        ),
                      ),
                      InkWell(
                        onTap: (){
                          Get.to(() => const RecommendMRPage());
                        },
                        child: Container(
                            margin: const EdgeInsets.only(top: 16),
                            width: Get.width,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black45,
                                  offset: Offset(1, 1),
                                  blurRadius: 2,
                                ),
                              ],
                              color: const Color(0xFF282828),
                            ),
                            child: Center(
                              child: RichText(
                                text: const TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'แนะนำลูกค้าง่ายๆ ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w300,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFFFFFFFF),
                                          fontSize: 14,
                                        ),
                                      ),
                                      TextSpan(
                                        text: 'คลิก',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w300,
                                          fontFamily: 'Prompt-Medium',
                                          color: Color(0xFFFFB100),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ]
                                ),
                              ),
                            )
                        ),
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      const Text('กิจกรรมแนะนำล่าสุด',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontFamily: 'Prompt-Medium',
                          color: Color(0xFF000000),
                          fontSize: 14,
                        ),)
                    ],
                  ),
                ),
                referralMRCtl.referralMR.data!.isNotEmpty
                    ? buildListReferral()
                    : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 31),
                    Image.asset('assets/image/MR/!_icons.png',
                      width: 11,
                      height: 11,
                    ),
                    const SizedBox(
                      width: 7,
                    ),
                    const Text('ไม่มีรายการกิจกรรม',style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Prompt',
                      color: Color(0xFF707070),
                      fontSize: 12,
                    ),
                    ),
                  ],
                ),
                const SizedBox(height: 14),
                referralMRCtl.referralMR.data!.length > 3
                    ? InkWell(
                  onTap: (){
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => const historyReferralPage(),
                    ));
                  },
                  child: SizedBox(
                    width: 323,
                    height: 33,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: const [
                        Padding(
                          padding: EdgeInsets.only(left: 20),
                          child: Text('ดูทั้งหมด',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontFamily: 'Prompt-Medium',
                              color: Color(0xFF282828),
                              fontSize: 14,
                            ),
                          ),
                        ),
                        SizedBox(width: 5),
                        SizedBox(
                          height: 30,
                          child: Icon(Icons.arrow_forward_ios_outlined,
                            color: Color(0xFF895F00),
                            size: 13,
                          ),
                        ),
                      ],
                    ),

                  ),
                )
                    : Container(),
                const Padding(padding: EdgeInsets.only(bottom: 20)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  buildListReferral(){
    return GetBuilder<ReferralMRController>(
        builder: (refController){
          numReferral = refController.referralMR.data!.length;
          List<Widget> list = [];
          for (int i = 0; i < refController.referralMR.data!.length && i < data ; i++) {
            list.add(
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 323,
                height: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      offset: const Offset(0, 4),
                      blurRadius: 30,
                    ),
                  ],
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFFF3F3F3).withOpacity(0.6),
                      const Color(0xFFFFFFFF).withOpacity(1),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(
                          top: 10,
                          left: 13
                      ),
                      child: Image.asset('assets/image/MR/File_dock_duotone.png',
                        width: 30,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(
                          top: 10,
                          left: 50,
                          right: 10
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(refController.referralMR.data![i].referral.toString(),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  color: Color(0xFF282828),
                                  fontSize: 12,
                                ),
                              ),
                              Text(
                                AppService.dateThaiDate(refController.referralMR.data![i].createTime),
                                style: const TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: Color(0xFF969696),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            width: 170,
                            child: Text(refController.referralMR.data![i].noteReferral == null
                                ? ""
                                : refController.referralMR.data![i].noteReferral.toString(),
                              maxLines: 1, overflow:
                              TextOverflow.ellipsis,
                              style: const TextStyle(
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Prompt',
                                color: Color(0xFF707070),
                                fontSize: 11,
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              RichText(text:
                              refController.referralMR.data![i].referral == "ออกรถใหม่"
                                  ? TextSpan(
                                children:[
                                  TextSpan(
                                    text: refController.referralMR.data![i].amountLikepoint == null
                                        ? "0"
                                        : formatter.format(refController.referralMR.data![i].amountLikepoint),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'Prompt-Medium',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: ' พอยท์ และ ',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                  TextSpan(
                                    text: refController.referralMR.data![i].amountFiat == null
                                        ? "0"
                                        : formatter.format(refController.referralMR.data![i].amountFiat),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'Prompt-Medium',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: ' บาท',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                ] ,
                              )
                                  : TextSpan(
                                children:[
                                  TextSpan(
                                    text: refController.referralMR.data![i].amountLikepoint == null
                                        ? "0"
                                        : formatter.format(refController.referralMR.data![i].amountLikepoint),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'Prompt-Medium',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                  const TextSpan(
                                    text: ' พอยท์',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontFamily: 'Prompt',
                                      color: Color(0xFF895F00),
                                      fontSize: 11,
                                    ),
                                  ),
                                ] ,
                              ),
                              ),
                              if(refController.referralMR.data![i].statusReferral == "pending")...[
                                RichText(text: const TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'รอดำเนินการ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt',
                                        color: Color(0xFF895F00),
                                        fontSize: 11,
                                      ),
                                    ),

                                  ] ,
                                ),
                                )
                              ] else if (refController.referralMR.data![i].statusReferral == "pending_review")...[
                                RichText(text: const TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'รอตรวจสอบ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt',
                                        color: Color(0xFF895F00),
                                        fontSize: 11,
                                      ),
                                    ),
                                  ] ,
                                ),
                                )
                              ] else if (refController.referralMR.data![i].statusReferral == "pending_payment")...[
                                RichText(text: const TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'รอทำการจ่าย',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt',
                                        color: Color(0xFF895F00),
                                        fontSize: 11,
                                      ),
                                    ),
                                  ] ,
                                ),
                                )
                              ] else if(refController.referralMR.data![i].statusReferral == "successful_transfer")...[
                                RichText(text: const TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'โอนจ่ายสำเร็จ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt',
                                        color: Color(0xFF5AB13B),
                                        fontSize: 11,
                                      ),
                                    ),
                                  ] ,
                                ),
                                ),
                              ] else if(refController.referralMR.data![i].statusReferral == "cancel")...[
                                RichText(text: const TextSpan(
                                  children:[
                                    TextSpan(
                                      text: 'รายการแนะนำไม่สำเร็จ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontFamily: 'Prompt',
                                        color: Color(0xFFFF5757),
                                        fontSize: 11,
                                      ),
                                    ),
                                  ] ,
                                ),)],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }
          return Column(
            children: list,
          );
        }
    );
  }

  buildCardMR(){
    return GetBuilder<ProfileController>(
        builder: (controller){
          return Center(
            child: Container(
              width: Get.width,
              height: 195,
              margin: EdgeInsets.only(left: Get.width < 500 ? 18 : 54,right: Get.width < 500 ? 18 : 54,),
              child: Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 25),
                    width: Get.width,
                    height: 137,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: const [0, 1.0],
                        colors: [
                          const Color(0xFFF3F3F3).withOpacity(0.9),
                          const Color(0xFFFFFFFF).withOpacity(0.6),
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        Positioned(
                          left: 20,
                          top: 22,
                          child: Row(
                            children: [
                              Container(
                                height: 40,
                                width: 40,
                                decoration: BoxDecoration(
                                    border: Border.all(
                                        width: 1.0,
                                        color: const Color(0xFF000000),
                                        style: BorderStyle.solid),
                                    shape: BoxShape.circle),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(50.0),
                                  child: Image(
                                    image: controller.profile.value.profilePicture == null || controller.profile.value.profilePicture == ""
                                        ? const AssetImage(
                                        'assets/image/home/<USER>') as ImageProvider
                                        : NetworkImage("${controller.profile.value.profilePicture}"),
                                    width: 50,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Align(
                          alignment: Alignment.topCenter,
                          child: Container(
                            margin: const EdgeInsets.only(
                                top: 28,
                                right: 25
                            ),
                            child: RichText(
                              text: const TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'ระดับ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Prompt-Medium',
                                        color: Color(0xFF895F00),
                                        fontSize: 12,
                                      ),
                                    ),
                                    TextSpan(
                                      text: ' PRO',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 20,
                                        letterSpacing: 0.4,
                                        color: Color(0xff151423),
                                      ),
                                    ),
                                  ]
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topCenter,
                          child: Container(
                              margin: const EdgeInsets.only(
                                  top: 55,
                                  left: 4
                              ),
                              child: Text(' รหัส : ${dataProfileCtl.profile.value.mrCode}',
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: 11,
                                    color: Color(0xFF282828)
                                ),)
                          ),
                        ),
                        Positioned(
                            top: 70,
                            left: 20,
                            child:  Column(
                              children: [
                                Row(
                                  children: [
                                    Stack(
                                      children: [
                                        Container(
                                          width: Get.width * 0.72,
                                          height: 5,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(30),
                                            color: const Color(0xFFD9D9D9),
                                          ),
                                        ),
                                        AnimatedContainer(
                                          width: remain,
                                          height: 5,
                                          duration: const Duration(milliseconds: 1000),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(30),
                                            color: Colors.transparent,
                                            boxShadow: [
                                              BoxShadow(
                                                color: const Color(0xFFFFB100).withOpacity(0.7),
                                                spreadRadius: 1,
                                                blurRadius: 2,
                                                offset: const Offset(0, 0), // changes position of shadow
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Column(
                                      children: [
                                        Image.asset('assets/image/MR/crown.png',
                                          width: 20,
                                          height: 20,),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            )
                        ),
                        Positioned(
                          top: 95,
                          left: 20,
                          child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('เก่งมาก..คุณมาถึงระดับ Pro ได้แล้ว ยินดีด้วย! แต่ก็สามารถ',
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: 10,
                                  color: Color(0xFF282828)
                              ),),
                            const Text('สะสมพอยท์ได้เรื่อยๆ น้า',
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: 10,
                                  color: Color(0xFF282828)
                              ),)
                          ],
                            ),
                        )
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: Get.width,
                      height: 33,
                      decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ),
                          color: Colors.white
                      ),
                      child: InkWell(
                        onTap: (){
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const BenefitMRPage(),
                          ));
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(left: 20),
                              child: Text('สิทธิพิเศษการได้รับคะแนน',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  color: Color(0xFF895F00),
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.only(right: 20),
                              height: 30,
                              child: const Icon(Icons.arrow_forward_ios_outlined,
                                color: Color(0xFF282828),
                                size: 13,
                              ),
                            ),
                          ],
                        ),
                      ),

                    ),
                  ),
                  Align(
                    alignment: Alignment.topCenter,
                    child: Image.asset('assets/image/MR/Pro_coins_crown.png',
                      width: 60,
                      height: 60,
                      alignment: Alignment.topCenter,),
                  ),
                ],
              ),
            ),
          );
        }
    );
  }
}
