import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/MR_controller.dart';
import 'package:intl/intl.dart';

import '../../../component/loader.dart';

class BenefitMRPage extends StatefulWidget {
  const BenefitMRPage({Key? key}) : super(key: key);

  @override
  State<BenefitMRPage> createState() => _BenefitMRPageState();
}

class _BenefitMRPageState extends State<BenefitMRPage> {

  final formatter = NumberFormat("#,###,###,###");

  var formatDate = DateFormat('dd-MM-yyyy');
  var formatYear = DateFormat('MM');
  final mrCtl = Get.put(ReferralMRController());

  bool isLoading = true;

  DateTime dateCal = DateTime.now().add(
    const Duration(days: 3),
  );

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "BenefitMarketingRepresentative");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Obx(() {
          if(mrCtl.isLoading.value == true){
            return AppLoader.loaderWaitPage(context);
          } else {
            return
              Padding(
                padding:  EdgeInsets.only(top: 80),
                child: Container(
                width: Get.width,
                height: Get.height * 0.9,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: const [0, 1.0],
                    colors: [
                      const Color(0xFFF3F3F3).withOpacity(0.7),
                      const Color(0xFFFFFFFF).withOpacity(0.6),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF000000).withOpacity(0.1),
                      spreadRadius: 5,
                      blurRadius: 7,
                      offset: const Offset(0, 3), // changes position of shadow
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  child: Container(
                    margin: EdgeInsets.only(
                        top: 42,
                        left: Get.width < 600 ? 18 : 54,
                        right: Get.width < 600 ? 18 : 54,
                        bottom: 42
                    ),
                    width: Get.width,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'เป็นตัวแทนผู้แนะนำกับเรา ได้อะไร?',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 16,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                            height: 10
                        ),
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'เริ่มต้นจากลูกค้าสู่การเป็นตัวแทนผู้แนะนำลูกค้า MR หรือ ตัวแทน',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nทางการตลาดนั้นเอง สิทธิ์ประโยชน์และผลตอบแทนที่คุณสามารถ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nสร้างรายได้ให้กับตัวเอง จากกิจกรรมแนะนำลูกค้ากับประชากิจฯ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                            height: 25
                        ),
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'ยิ่งอยู่ในระดับที่สูง..',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: 'การได้รับได้รางวัล ก็ยิ่งได้มาก',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nเพิ่มระดับตัวแทนของคุณ เพื่อรับสิทธิประโยชน์ที่มากกว่า',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                            height: 15
                        ),
                        RichText(
                          text: TextSpan(
                              children: [
                                const TextSpan(
                                  text: 'MR ระดับ Pro',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: '\nมีคะแนน PMSpoint สะสม ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: formatter.format(mrCtl.resPro.accumulate),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: ' พอยท์ขึ้นไป',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\n${mrCtl.resPro.detail}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                            height: 10
                        ),
                        RichText(
                          text: TextSpan(
                              children: [
                                const TextSpan(
                                  text: 'MR ระดับ Plus',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: '\nมีคะแนน PMSpoint สะสม ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: formatter.format(mrCtl.resPlus.accumulate),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: ' พอยท์ขึ้นไป',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\n${mrCtl.resPlus.detail}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                            height: 10
                        ),
                        RichText(
                          text: TextSpan(
                              children: [
                                const TextSpan(
                                  text: 'MR ระดับ Standard',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: '\nมีคะแนน PMSpoint สะสม',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '0 - ${formatter.format(mrCtl.resStandard.accumulate)} ',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                const TextSpan(
                                  text: 'พอยท์',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\n${mrCtl.resStandard.detail}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'สถานะระดับและการได้รับสิทธิพิเศษ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ตามเงื่อนไขตัวแทนผู้แนะนำ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nระยะเวลาสถานะระดับของผู้แนะนำนั้น จะมีอายุสถานะอยู่ภายในระยะ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nเวลา 1 ปี เท่านั้น และระดับจะมีการปรับเปลี่ยนใหม่ในปีถัดไป โดยการ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nคำนวนจากคะแนนสะสมที่ผู้แนะนำได้ทำไว้ในปีก่อนหน้า',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'รวมถึง..',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: 'สิทธิพิเศษเกณฑ์การได้รับคะแนน',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ผู้แนะนำจะยังคงได้รับ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nตามสถานะที่ได้มีการปรับเปลี่ยนใหม่ไปด้วย',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF282828),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: ' ในปีถัดไป',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        RichText(
                          text: const TextSpan(
                              children: [
                                TextSpan(
                                  text: 'หมายเหตุ :',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    color: Color(0xFF664701),
                                    fontSize: 12,
                                  ),
                                ),

                                TextSpan(
                                  text: ' คะแนนสะสมจากการร่วมกิจกรรม จะเริ่มคำนวณใหม่',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                                TextSpan(
                                  text: '\nทั้งหมด โดยค่าคะแนนจะเริ่มต้นที่ 0 พอยท์ เสมอ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: Color(0xFF707070),
                                    fontSize: 12,
                                  ),
                                ),
                              ]
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                            ),
              );
            //   Stack(
            //   children: <Widget>[
            //     // Container(
            //     //   width: Get.width,
            //     //   height: Get.height,
            //     //   decoration: const BoxDecoration(
            //     //     gradient: LinearGradient(
            //     //       begin: Alignment.topCenter,
            //     //       end: Alignment.bottomCenter,
            //     //       stops: [0, 1.0],
            //     //       colors: [
            //     //         Color(0xFFE8E6E2),
            //     //         Color(0xFFD9D8D5),
            //     //       ],
            //     //     ),
            //     //   ),
            //     // ),
            //     // Center(child: Image.asset('assets/image/MR/Lady_BG_Blur.png')),
            //     // InkWell(
            //     //   onTap: (){
            //     //     Navigator.pop(context);
            //     //   },
            //     //   child: Container(
            //     //     margin: EdgeInsets.only(
            //     //         left: Get.width < 600 ? 18 : 54,
            //     //         top: 48
            //     //     ),
            //     //     alignment: Alignment.centerLeft,
            //     //     height: 50,
            //     //     width: 50,
            //     //     child: const Icon(Icons.arrow_back_ios_new,
            //     //       color: Color(0xFF282828),
            //     //       size: 16,
            //     //     ),
            //     //   ),
            //     // ),
            //     Positioned(
            //       bottom: 0,
            //       child:
            //     ),
            //     Container(
            //       alignment: Alignment.topCenter,
            //      margin: EdgeInsets.only(top: Get.height * 0.15,),
            //         child: Image.asset('assets/image/MR/Lady_coins.png',
            //       width: 157,
            //       height: 127,)),
            //     Container(
            //       width: Get.width,
            //       margin: const EdgeInsets.only(
            //           top: 65
            //       ),
            //       child: Align(
            //         alignment: Alignment.topCenter,
            //         child: Column(
            //           children: [
            //             RichText(
            //               text: const TextSpan(
            //                   children: [
            //                     TextSpan(
            //                       text: 'สิทธิพิเศษการได้รับคะแนน',
            //                       style: TextStyle(
            //                         fontWeight: FontWeight.w500,
            //                         fontFamily: 'Prompt-Medium',
            //                         color: Color(0xFF282828),
            //                         fontSize: 14,
            //                       ),
            //                     ),
            //                   ]
            //               ),
            //             ),
            //
            //           ],
            //         ),
            //       ),
            //     ),
            //   ],
            // );
          }
        })
    );
  }

}
