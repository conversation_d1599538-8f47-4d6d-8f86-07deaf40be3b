import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';

class historyNewcarReferral extends StatefulWidget {
  const historyNewcarReferral({Key? key}) : super(key: key);

  @override
  State<historyNewcarReferral> createState() => _historyNewcarReferralState();
}

class _historyNewcarReferralState extends State<historyNewcarReferral> {

  final SecureStorage secureStorage = SecureStorage();

  List<dynamic> dataReferral = [];
  int takeOffset = 0;
  int data = 10;

  final formatter = NumberFormat("#,###,###,###");

  bool showButton = false;

  final profileCtl = Get.put(ProfileController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    analytics.setCurrentScreen(screenName: "HistoryNewCarMarketingRepresentative");
    dataReferral = [];
    getReferralMR();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SingleChildScrollView(
        child: Container(
            child: dataReferral.isNotEmpty
                ? Column(
              children: [
                buildListReferral(context),
                SizedBox(
                  height: 30.h,
                ),
                dataReferral.length > data
                    ? Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(onPressed: (){
                      setState(() {
                        data = data + 10;
                        takeOffset = takeOffset + 1;
                        getReferralMROnClick();
                      });
                    },
                        child: Text('ดูเพิ่มเติม',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            fontSize: 12.w,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF000000),
                          ),)),
                    Icon(Icons.keyboard_arrow_down,
                      size: 18.w,
                      color: const Color(0xFF895F00),)
                  ],
                )
                    : Container(),
                SizedBox(
                  height: 30.h,
                )
              ],
            )
                : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  height: 60.h,
                ),
                Image.asset('assets/image/MR/!_icons.png',width: 10.h,),
                SizedBox(
                    width: MediaQuery.of(context).size.width * 0.02
                ),
                Text('ไม่มีรายการกิจกรรมแนะนำ',
                  style: TextStyle(
                    fontFamily: 'Prompt-Medium',
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF707070),
                  ),)
              ],
            )
        ),
      ),
    );
  }

  getReferralMR() async {
    try{
      if(profileCtl.profile.value.mrCode != ""){
        Map getReferralMR = {"MrCode" : profileCtl.profile.value.mrCode,
          "takeOffset" : takeOffset,
          "getReferral" : ["เข้าใช้บริการ", "ซื้ออะไหล่ ประดับยนต์ แม็คยาง", "จัดหาสถานที่ตรวจเช็ครถ"]
        };
        final resReferralMR = await AppApi.post(AppUrl.getTypeReferralMR, getReferralMR);
        var status = resReferralMR['status'];
        if (status == 200){
          setState(() {
            dataReferral.addAll(resReferralMR['result']);
          });
        }
      }
    } catch (e){
      print(e);
    }
  }

  getReferralMROnClick() async {
    try{
      if(profileCtl.profile.value.mrCode != ""){
        Map getReferralMR = {"MrCode" : profileCtl.profile.value.mrCode,
          "takeOffset" : takeOffset,
          "getReferral" : ["เข้าใช้บริการ", "ซื้ออะไหล่ ประดับยนต์ แม็คยาง", "จัดหาสถานที่ตรวจเช็ครถ"]
        };
        final resReferralMR = await AppApi.post(AppUrl.getTypeReferralMR, getReferralMR);
        var status = resReferralMR['status'];
        if (status == 200){
          setState(() {
            dataReferral.addAll(resReferralMR['result']);
          });
        }
      }
    } catch (e){
      print(e);
    }
  }

  Widget buildListReferral(context){
    try {
      var month = ["ม.ค.","ก.พ.","มี.ค.", "เม.ย.",
        "พ.ค.", "มิ.ย.", "ก.ค.", "ส.ค.",
        "ก.ย.", "ต.ค.", "พ.ย.", "ธ.ค."];
      List<dynamic> showMonth = [[],[],[], [],
        [], [], [], [],
        [], [], [], []];
      for (var i = 0; i < dataReferral.length && i < data; i++) {
        for (var j = 0; j < month.length; j++) {
          if(AppService.monthThaiDate(dataReferral[i]['create_time']) == month[j]){
            showMonth[j].add(
                dataReferral[i]
            );
          }
        }
      }
      return Center(
        child: SizedBox(
          width: 323.w,
          child: Column(
            children: [
              showMonth[11].isNotEmpty ?
              buildMonthContent("ธ.ค.")
                  : Container(),
              listData(showMonth[11]),
              showMonth[10].isNotEmpty ?
              buildMonthContent("พ.ย.")
                  : Container(),
              listData(showMonth[10]),
              showMonth[9].isNotEmpty ?
              buildMonthContent("ต.ค.")
                  : Container(),
              listData(showMonth[9]),
              showMonth[8].isNotEmpty ?
              buildMonthContent("ก.ย.")
                  : Container(),
              listData(showMonth[8]),
              showMonth[7].isNotEmpty ?
              buildMonthContent("ส.ค.")
                  : Container(),
              listData(showMonth[7]),
              showMonth[6].isNotEmpty ?
              buildMonthContent("ก.ค.")
                  : Container(),
              listData(showMonth[6]),
              showMonth[5].isNotEmpty ?
              buildMonthContent("มิ.ย.")
                  : Container(),
              listData(showMonth[5]),
              showMonth[4].isNotEmpty ?
              buildMonthContent("พ.ค.")
                  : Container(),
              listData(showMonth[4]),
              showMonth[3].isNotEmpty ?
              buildMonthContent("เม.ย.")
                  : Container(),
              listData(showMonth[3]),
              showMonth[2].isNotEmpty ?
              buildMonthContent("มี.ค.")
                  : Container(),
              listData(showMonth[2]),
              showMonth[1].isNotEmpty ?
              buildMonthContent("ก.พ.")
                  : Container(),
              listData(showMonth[1]),
              showMonth[0].isNotEmpty ?
              buildMonthContent("ม.ค.")
                  : Container(),
              listData(showMonth[0]),
            ],
          ),
        ),
      );
    } catch (e){
      if (kDebugMode) {
        print(e);
      }
      return Container();
    }
  }

  Widget listData(showMonth){
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: showMonth.length,
      itemBuilder: (context, i) =>
          Container(
            margin: EdgeInsets.only(top: 5.h ,bottom: 5.h),
            width: 323.w,
            height: 70.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.h),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 4),
                  blurRadius: 30,
                ),
              ],
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  const Color(0xFFF3F3F3).withOpacity(0.6),
                  const Color(0xFFFFFFFF).withOpacity(1),
                ],
              ),
            ),
            child: Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      top: 10.h,
                      left: 13.w
                  ),
                  child: Image.asset('assets/image/MR/File_dock_duotone.png',
                    width: 30.h,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(
                      top: 10.h,
                      left: 50.w,
                      right: 10.w
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          RichText(text: TextSpan(
                            children:[
                              TextSpan(
                                text: showMonth[i]['referral'],
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  color: const Color(0xFF282828),
                                  fontSize: 12.sp,
                                ),
                              ),
                            ] ,
                          )),
                          RichText(text: TextSpan(
                            children:[
                              TextSpan(
                                text: AppService.dateThaiDate(showMonth[i]['create_time']),
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: const Color(0xFF969696),
                                  fontSize: 12.sp,
                                ),
                              ),
                            ] ,
                          )),
                        ],
                      ),
                      SizedBox(
                        width: 170.w,
                        child: Text(showMonth[i]['note_referral'] ?? "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Prompt',
                            color: const Color(0xFF707070),
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          RichText(text:
                          showMonth[i]['reward_type'] == "likepoint"
                              ? TextSpan(
                            children:[
                              TextSpan(
                                text: formatter.format(showMonth[i]['amount_likepoint']),
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt-Medium',
                                  color: const Color(0xFF895F00),
                                  fontSize: 12.sp,
                                ),
                              ),
                              TextSpan(
                                text: ' พอยท์',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: const Color(0xFF895F00),
                                  fontSize: 12.sp,
                                ),
                              ),
                            ] ,
                          )
                              : TextSpan(
                            children:[
                              TextSpan(
                                text: formatter.format(showMonth[i]['amount_fiat']),
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt-Medium',
                                  color: const Color(0xFF895F00),
                                  fontSize: 12.sp,
                                ),
                              ),
                              TextSpan(
                                text: ' บาท',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Prompt',
                                  color: const Color(0xFF895F00),
                                  fontSize: 12.w,
                                ),
                              ),
                            ] ,
                          ),
                          ),
                          if(showMonth[i]['status_referral'] == "pending")...[
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'รอดำเนินการ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF895F00),
                                    fontSize: 11.sp,
                                  ),
                                ),

                              ] ,
                            ),
                            )
                          ] else if (showMonth[i]['status_referral'] == "pending_review")...[
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'รอตรวจสอบ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF895F00),
                                    fontSize: 11.sp,
                                  ),
                                ),

                              ] ,
                            ),
                            )
                          ] else if (showMonth[i]['status_referral'] == "pending_payment")...[
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'รอทำการจ่าย',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF895F00),
                                    fontSize: 11.sp,
                                  ),
                                ),
                              ] ,
                            ),
                            )
                          ] else if(showMonth[i]['status_referral'] == "successful_transfer")...[
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'โอนจ่ายสำเร็จ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFF5AB13B),
                                    fontSize: 11.sp,
                                  ),
                                ),
                              ] ,
                            ),)] else if(showMonth[i]['status_referral'] == "cancel")...[
                            RichText(text: TextSpan(
                              children:[
                                TextSpan(
                                  text: 'รายการแนะนำไม่สำเร็จ',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontFamily: 'Prompt',
                                    color: const Color(0xFFFF5757),
                                    fontSize: 11.sp,
                                  ),
                                ),
                              ] ,
                            ),)],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  buildMonthContent(text){
    return Container(
      margin: EdgeInsets.only(top: 10.h , bottom: 10.h),
      child: Container(
        alignment: Alignment.center,
        width: 60.w,
        height: 23.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: const Color(0xFFE6E6E6),
        ),
        child: Text(text,
          style: TextStyle(
            fontFamily: 'Prompt-Medium',
            fontSize: 12.sp,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF895F00),
            letterSpacing: 0.4,
          ),),
      ),
    );
  }

}
