import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/recommend_MR.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:simple_gradient_text/simple_gradient_text.dart';

class recommendSuccessNewMRPage extends StatefulWidget {
  final Map refData;
  recommendSuccessNewMRPage(this.refData);

  @override
  State<recommendSuccessNewMRPage> createState() => _recommendSuccessNewMRPageState();
}

class _recommendSuccessNewMRPageState extends State<recommendSuccessNewMRPage> {
  get statusUpdateOpenAPP => null;

  final formatter = new NumberFormat("#,###,###,###");

  final profileCtl = Get.put(ProfileController());

  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;

  @override
  void initState() {
    super.initState();
    analytics.setCurrentScreen(screenName: "RecommendSuccessMarketingRepresentative");
  }

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double _height = MediaQuery.of(context).size.height;
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Scaffold(
            body: Stack(
              children: <Widget>[
                Container(
                  width: _width,
                  height: _height,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0, 1.0],
                      colors: [
                        Color(0xFFE8E6E2),
                        Color(0xFFD9D8D5),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: Get.width,
                  height: 371,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      // stops: [1.0, 1.0],
                      colors: [
                        const Color(0xFFFFB100).withOpacity(1),
                        const Color(0xFFFFC700).withOpacity(0.7),
                        const Color(0xFFFFC700).withOpacity(0.4),
                        const Color(0xFFFFC700).withOpacity(0),
                        // Color(0xFFFFB100),
                      ],
                    ),
                  ),
                ),
                Column(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 13,),
                      // color: Colors.red,
                      width: _width,
                      // height: 700,
                      child: Stack(
                        children: [
                          Container(
                            width: _width,
                          ),
                          Container(
                            margin: const EdgeInsets.only(left: 25),
                            width: 400,
                            height: 80,
                            child: Container(
                              margin: const EdgeInsets.only(
                                  top: 20
                              ),
                            ),
                          ),
                          Column(
                            children: [
                              Container(
                                  margin: const EdgeInsets.only(top: 80.0,
                                      left: 20
                                  ),
                                  alignment: Alignment.topCenter,
                                  // color: Colors.red,
                                  child: Image.asset('assets/image/MR/Success_Mascot.png',
                                    height: 127,
                                    width: 245,
                                  )
                              ),
                              const SizedBox(height: 8,),
                              GradientText('ยืนยันข้อมูลแนะนำ..เรียบร้อย',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontFamily: 'Prompt-Medium',
                                  fontSize: 14,
                                ),
                                gradientType: GradientType.linear,
                                gradientDirection: GradientDirection.ttb,
                                radius: 10,
                                colors: const [
                                  Color(0xFF664701),
                                  Color(0xFF151423),
                                ],),
                              const SizedBox(height: 22,),
                              const Text('คุณจะได้รับรางวัล',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: 14,
                                    color: Color(0xFF664701)
                                ),),
                              RichText(
                                text: TextSpan(
                                    children: [
                                      const TextSpan(
                                        text: 'จากกิจกรรมแนะนำ ',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF664701),
                                          fontSize: 14,
                                        ),
                                      ),
                                      TextSpan(
                                        text: widget.refData['referral'],
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt-Medium',
                                          color: Color(0xFF664701),
                                          fontSize: 14,
                                        ),
                                      ),
                                    ]
                                ),
                              ),
                              RichText(
                                text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'ตามเกณฑ์ระดับ ${widget.refData['rank']}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF895F00),
                                          fontSize: 14,
                                        ),
                                      )
                                    ]
                                ),
                              ),
                              const SizedBox(height: 24,),
                              widget.refData['referral'] == "ออกรถใหม่"
                                  ? Column(
                                children: [
                                  RichText(
                                    text: const TextSpan(
                                        children: [
                                          // TextSpan(
                                          //   text: 'เงินสดมูลค่า',
                                          //   style: TextStyle(
                                          //     fontWeight: FontWeight.w400,
                                          //     fontFamily: 'Prompt',
                                          //     color: Color(0xFF664701),
                                          //     fontSize: 12,
                                          //   ),
                                          // )
                                        ]
                                    ),
                                  ),
                                  Text(formatter.format(double.parse(widget.refData['amountLikepoint'].toString())),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 30,
                                        color: Color(0xFF664701)
                                    ),),
                                  RichText(
                                    text: const TextSpan(
                                        children: [
                                          TextSpan(
                                            text: ' PMSpoint',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Prompt',
                                              color: Color(0xFF895F00),
                                              fontSize: 14,
                                            ),
                                          )
                                        ]
                                    ),
                                  ),
                                ],
                              )
                              //     : widget.refData['referral'] == "ทดลองขับ"
                              //     ? Column(
                              //   children: [
                              //     Text('10,000',
                              //       style: const TextStyle(
                              //           fontWeight: FontWeight.w700,
                              //           fontFamily: 'Prompt-Medium',
                              //           fontSize: 30,
                              //           color: Color(0xFF664701)
                              //       ),),
                              //     // Text(formatter.format(double.parse(widget.refData['amountLikepoint'].toString())),
                              //     //   style: const TextStyle(
                              //     //       fontWeight: FontWeight.w700,
                              //     //       fontFamily: 'Prompt-Medium',
                              //     //       fontSize: 30,
                              //     //       color: Color(0xFF664701)
                              //     //   ),),
                              //     RichText(
                              //       text: const TextSpan(
                              //           children: [
                              //             TextSpan(
                              //               text: ' PMSpoint',
                              //               style: TextStyle(
                              //                 fontWeight: FontWeight.w400,
                              //                 fontFamily: 'Prompt',
                              //                 color: Color(0xFF895F00),
                              //                 fontSize: 14,
                              //               ),
                              //             )
                              //           ]
                              //       ),
                              //     ),
                              //   ],
                              // )
                                  :Column(
                                children: [
                                  Text(formatter.format(double.parse(widget.refData['amountLikepoint'].toString())),
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontFamily: 'Prompt-Medium',
                                        fontSize: 30,
                                        color: Color(0xFF664701)
                                    ),),
                                  RichText(
                                    text: const TextSpan(
                                        children: [
                                          TextSpan(
                                            text: 'PMSpoint',
                                            style: TextStyle(
                                              fontWeight: FontWeight.w400,
                                              fontFamily: 'Prompt',
                                              color: Color(0xFF895F00),
                                              fontSize: 14,
                                            ),
                                          )
                                        ]
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 14,),
                              RichText(
                                text: const TextSpan(
                                    children: [
                                      TextSpan(
                                        text: 'ขอบคุณที่ร่วมกิจกรรมแนะนำกับทางเรา',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w400,
                                          fontFamily: 'Prompt',
                                          color: Color(0xFF895F00),
                                          fontSize: 14,
                                        ),
                                      )
                                    ]
                                ),
                              ),
                              const Text('ยิ่งแนะนำ..ยิ่งได้มาก',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Prompt-Medium',
                                    fontSize: 14,
                                    color: Color(0xFF664701)
                                ),),
                              const SizedBox(height: 14,),
                              Padding(
                                padding: const EdgeInsets.only(bottom: 0),
                                child: InkWell(
                                  onTap: (){
                                    Navigator.pushReplacement(context,
                                        MaterialPageRoute(builder: (context) => const RecommendMRPage()));
                                  },
                                  child: Container(
                                    width: 323,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      boxShadow: const [
                                        BoxShadow(
                                          color: Colors.black45,
                                          offset: Offset(1, 1),
                                          blurRadius: 2,
                                        ),
                                      ],
                                      color: const Color(0xFF282828),
                                    ),
                                    child: Center(
                                      child: RichText(
                                        text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'แนะนำเพิ่ม..',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w300,
                                                  fontFamily: 'Prompt',
                                                  color: const Color(0xFFFFFFFF).withOpacity(0.9),
                                                  fontSize: 14,
                                                ),
                                              ),
                                              const TextSpan(
                                                text: 'คลิก',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: 'Prompt-Medium',
                                                  color: Color(0xFFFFB100),
                                                  fontSize: 14,
                                                ),
                                              )
                                            ]
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              TextButton(onPressed: (){
                                Navigator.of(context).push(MaterialPageRoute(
                                  builder: (context) => const HomeNavigator(),
                                ));
                              },
                                child:const Text('กลับหน้าหลัก',
                                  style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Prompt',
                                      fontSize: 14,
                                      color: Color(0xFF805F00)
                                  ),),
                              ),
                              widget.refData['referral'] == "ออกรถใหม่"
                                  ? const SizedBox(height: 30,)
                                  : const SizedBox(height: 110,),
                              widget.refData['referral'] == "ออกรถใหม่" || widget.refData['referral'] == "ซื้ออะไหล่ ประดับยนต์ แม็คยาง"
                                  ? SizedBox(
                                width: 323,
                                child: RichText(
                                  text: const TextSpan(
                                      children: [
                                        TextSpan(
                                          text: 'หมายเหตุ : ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFFFF5757),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'PMSpoint และเงินรางวัลที่คุณจะได้รับ ทางเราจะทำการ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'โอนจ่ายให้ภายใน 3 วัน หรือ ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'หลังจากการตรวจสอบข้อมูลที่มีการปิดการขายจากลูกค้าที่คุณแนะนำ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt-Medium',
                                            color: Color(0xFF664701),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: ' ตามเงื่อนไขของแต่ละกิจกรรมที่ทางเรากำหนดแล้วเท่านั้น',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'และจะขอสงวนสิทธิ์การเปลี่ยนแปลงเงื่อนไขข้อกำหนด โดยไม่ต้องแจ้งให้ทราบล่วงหน้า',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                      ]
                                  ),
                                ),
                              )
                                  : SizedBox(
                                width: 323,
                                // height: 40,
                                child: RichText(
                                  text: const TextSpan(
                                      children: [
                                        TextSpan(
                                          text: 'หมายเหตุ : ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFFFF5757),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'PMSpoint และเงินรางวัลที่คุณจะได้รับ ทางเราจะทำการ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w400,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'โอนจ่ายให้ภายใน 3 วัน หรือ ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'หลังจากการตรวจสอบข้อมูลที่มีการเข้าใช้บริการจากลูกค้าที่คุณแนะนำ',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt-Medium',
                                            color: Color(0xFF664701),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: ' ตามเงื่อนไขของแต่ละกิจกรรมที่ทางเรากำหนดแล้วเท่านั้น',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                        TextSpan(
                                          text: 'และจะขอสงวนสิทธิ์การเปลี่ยนแปลงเงื่อนไขข้อกำหนด โดยไม่ต้องแจ้งให้ทราบล่วงหน้า',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w500,
                                            fontFamily: 'Prompt',
                                            color: Color(0xFF707070),
                                            fontSize: 11,
                                          ),
                                        ),
                                      ]
                                  ),
                                ),
                              )
                            ],
                          ),

                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }
    );
  }
}
