import 'package:cached_network_image/cached_network_image.dart';
import 'package:colorful_safe_area/colorful_safe_area.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/component/widget.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/save_activity.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/accessory_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/service_controller/pmg_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/paymentTime_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/province_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_Order_controller.dart';
import 'package:mapp_prachakij_v3/controller/sparePart/sparepart_controller.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/bag_pmg.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/history.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>/pmg/partDetail.dart';
import 'package:mapp_prachakij_v3/view/login/login.dart';
import 'package:mapp_prachakij_v3/view/mobile/home_mobile.dart';

class TabPMG extends StatefulWidget {
  const TabPMG({super.key});

  @override
  State<TabPMG> createState() => _TabPMGState();
}

class _TabPMGState extends State<TabPMG> {
  // final pmgCtl = Get.put(PMGController());
  final profileCtl = Get.find<ProfileController>();
  final saveActivityCtl = Get.put(SaveActivityController());
  final sparepartOrderCtl = Get.put(SparepartOrderController());

  var formattedPrice = NumberFormat.currency(locale: 'en_US', symbol: '',decimalDigits: 0);

  // bool useTab = true;
  var cataloge = "accessories";

  final accessoryCtl = Get.put(AccessoryController());
  SparepartController ahlaiCtrl = Get.put(SparepartController());
  ProvinceController provinceCtrl = Get.put(ProvinceController());
  PaymentTimer timer = Get.put(PaymentTimer());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    ahlaiCtrl.getData();
    provinceCtrl.getAddress();
    sparepartOrderCtl.getSparepartOrder();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Get.delete<SaveActivityController>();
        Get.delete<SparepartOrderController>();
        Get.delete<AccessoryController>();
        Get.delete<SparepartController>();
        Get.delete<ProvinceController>();
        Get.delete<PaymentTimer>();
        Get.to(() => MobileBodyPage());
        return true;
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          body: ColorfulSafeArea(
            color: Colors.black,
            child: Obx(() {
              if (accessoryCtl.isLoading.value || ahlaiCtrl.isLoading.value) {
                return AppLoader.loaderWaitPage(context);
              } else {
                return Stack(
                  children: [
                    Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment(0.00, -1.00),
                          end: Alignment(0, 1),
                          colors: [Color(0xFFEDEDED), Color(0xFFF2F2F2)],
                        ),
                      ),
                    ),
                    Column(
                      children: [
                        _buildAppBar(),
                        _buildTab(),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                const SizedBox(
                                  height: 20,
                                ),
                                ahlaiCtrl.showTab.value
                                    ? buildListTeam()
                                    : Column(
                                  children: [
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    Container(
                                      width: MediaQuery.of(context).size.width * 0.9,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: const Color(0x26BCBCBC),
                                        borderRadius: BorderRadius.circular(10.0),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          Container(
                                            width: 20,
                                            height: 20,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(100.0),
                                              // color: Color(0xFFFFB100),
                                            ),
                                            child: SvgPicture.asset(
                                              "assets/icon/sparePart/search-01.svg",
                                              // color: Colors.white,
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 10,
                                          ),
                                          Container(
                                            width: MediaQuery.of(context).size.width * 0.9 - 35,
                                            height: 50,
                                            child: TextField(
                                              controller: ahlaiCtrl.searchController,
                                              onChanged: (value) async {
                                                var checked = await ahlaiCtrl.searchProduct(value);
                                                if(checked == true){
                                                  FocusScope.of(context).unfocus();
                                                  print("finish");
                                                  setState(() {});
                                                }
                                              },
                                              decoration: InputDecoration(
                                                hintText: "คุณกำลังมองหา",
                                                hintStyle: TextStyle(
                                                  fontSize: 14,
                                                  color: Color(0xFFBCBCBC),
                                                ),
                                                border: InputBorder.none,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ahlaiCtrl.searchProductVar.isEmpty
                                        ? cataloge == "accessories"
                                        ? buildSparepart(ahlaiCtrl.product["ประดับยนต์"], "ประดับยนต์")
                                        : buildSparepart(ahlaiCtrl.product["อะไหล่"], "อะไหล่")
                                        :buildSparepartSearch(ahlaiCtrl.searchProductVar),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ],
                );
              }
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildTab () {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            ahlaiCtrl.changeShowTab(true);
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width:
                  MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: ahlaiCtrl.showTab.value
                      ? const BoxDecoration(
                    color: Color(0xFFFFB100),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      right: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),
                    ),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(18),
                    ),
                  )),
              ahlaiCtrl.showTab.value
                  ? Container(
                width:
                MediaQuery.of(context).size.width *
                    0.5,
                height: 47,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width:
                MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("ทีมบริการ",
                    style: TextStyle(
                        fontSize: 14,
                        color: ahlaiCtrl.showTab.value
                            ? Colors.black
                            : const Color(0x88282828))),
              ),
            ],
          ),
        ),
        InkWell(
          onTap: () {
            ahlaiCtrl.changeShowTab(false);
          },
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                  width:
                  MediaQuery.of(context).size.width * 0.5,
                  height: 50,
                  decoration: !ahlaiCtrl.showTab.value
                      ? const BoxDecoration(
                    color: Color(0xFFFFB100),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(20),
                    ),
                  )
                      : const BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      left: BorderSide(
                        width: 1,
                        color: Color(0xFFBCBCBC),
                      ),
                    ),
                    borderRadius: BorderRadius.only(
                      bottomRight: Radius.circular(18),
                    ),
                  )),
              !ahlaiCtrl.showTab.value
                  ? Container(
                width:
                MediaQuery.of(context).size.width *
                    0.5,
                height: 47,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(18),
                  ),
                ),
              )
                  : Container(),
              Container(
                width:
                MediaQuery.of(context).size.width * 0.5,
                height: 50,
                alignment: Alignment.center,
                child: Text("สั่งอะไหล่",
                    style: TextStyle(
                        fontSize: 14,
                        color: !ahlaiCtrl.showTab.value
                            ? Colors.black
                            : const Color(0x88282828))),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    return Obx(() => Container(
      width: MediaQuery.of(context).size.width,
      height: 50,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.2,
            margin: EdgeInsets.only(
              left: MediaQuery.of(context).size.width * 0.05,
            ),
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () {
                Get.delete<SaveActivityController>();
                Get.delete<SparepartOrderController>();
                Get.delete<AccessoryController>();
                Get.delete<SparepartController>();
                Get.delete<ProvinceController>();
                Get.delete<PaymentTimer>();
                Get.back();
                // Get.to(
                //       () => MobileBodyPage(),
                //   transition: Transition.leftToRight, // Left-to-right transition
                //   duration: Duration(milliseconds: 300), // Adjust speed if needed
                // );
              },
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(15),
                      topLeft: Radius.circular(15),
                      bottomRight: Radius.circular(15),
                      bottomLeft: Radius.circular(15),
                    ),
                    border:
                    Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  size: 18,
                  color: Color(0xFFFFB100),
                ),
              ),
            ),
          ),
          Container(
            child: const Row(
              children: [
                Text(
                  "อะไหล่",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2B1710),
                  ),
                ),
                Text(
                  "ประดับยนต์",
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF895F00),
                  ),
                ),
              ],
            ),
          ),
          Container(
              width: MediaQuery.of(context).size.width * 0.2,
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(
                right: MediaQuery.of(context).size.width * 0.05,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      Get.to(() => OrderHistory());
                    },
                    child: Container(
                        width: MediaQuery.of(context).size.width * 0.1,
                        height: 50,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            SvgPicture.asset(
                              "assets/icon/sparePart/clock.svg",
                              color: const Color(0xFF000000),
                            ),
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.1,
                              height: 50,
                            ),
                            Positioned(
                              right: 0,
                              top: 10,
                              child: sparepartOrderCtl.listOrder.isEmpty
                                  ? SizedBox()
                                  : Container(
                                width: 20,
                                height: 15,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.0),
                                  gradient: const LinearGradient(
                                    begin: Alignment(0.00, -1.00),
                                    end: Alignment(0, 1),
                                    colors: [
                                      Color(0xFFFFC700),
                                      Color(0xFFFFB100),
                                      Color(0xFFFF9900)
                                    ],
                                  ),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Color(0x80FF9900),
                                      offset: Offset(0, 2),
                                      blurRadius: 6,
                                    ),
                                  ],
                                ),
                                child: Center(
                                  child: Text(
                                    "${sparepartOrderCtl.listOrder.where((order) {
                                      return order["status_order"] != "CANCELLED" &&
                                          order["status_order"] != "DELIVERED" &&
                                          order["status_order"] != "SHIPPED";
                                    }).length}",
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            )
                          ],
                        )
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Get.to(() => BagPmg());
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.1,
                      height: 50,
                      child: Stack(alignment: Alignment.center, children: [
                        SvgPicture.asset(
                          "assets/icon/sparePart/bag.svg",
                          color: const Color(0xFF000000),
                        ),
                        SizedBox(
                          width: MediaQuery.of(context).size.width * 0.1,
                          height: 50,
                        ),
                        Positioned(
                          right: 0,
                          top: 10,
                          child: ahlaiCtrl.bagProduction.length == 0
                              ? SizedBox()
                              : Container(
                            width: 20,
                            height: 15,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.0),
                              gradient: const LinearGradient(
                                begin: Alignment(0.00, -1.00),
                                end: Alignment(0, 1),
                                colors: [
                                  Color(0xFFFFC700),
                                  Color(0xFFFFB100),
                                  Color(0xFFFF9900)
                                ],
                              ),
                              boxShadow: const [
                                BoxShadow(
                                  color: Color(0x80FF9900),
                                  offset: Offset(0, 2),
                                  blurRadius: 6,
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                "${ahlaiCtrl.bagProduction.length}",
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        )
                      ]),
                    ),
                  )
                ],
              ))
        ],
      ),
    ));
  }

  Widget buildListTeam() {
    return Column(
      children: List.generate(accessoryCtl.memberAccessoryList.data!.length, (index) {
        return Column(
          children: [
            buildServiceTeam(
                context,
                accessoryCtl.memberAccessoryList.data![index].picture,
                accessoryCtl.memberAccessoryList.data![index].name,
                accessoryCtl.memberAccessoryList.data![index].team,
                accessoryCtl.memberAccessoryList.data![index].phone,
                accessoryCtl.memberAccessoryList.data![index].lineId),
            const SizedBox(
              height: 6,
            ),
            const Divider(
              color: Color(0x22000000),
              thickness: 1,
              indent: 20,
              endIndent: 20,
            ),
            const SizedBox(
              height: 6,
            ),
          ],
        );
      }),
    );
  }

  Widget buildServiceTeam(context, picture, name, team, phone, lineId) {
    var splitName = name.replaceAll(" ", "").replaceAll(")", "").split("(");
    return Container(
      width: Get.height,
      height: 87,
      margin: EdgeInsets.only(
        left: Get.width < 500 ? 18 : 54,
        right: Get.width < 500 ? 18 : 54,
      ),
      padding: const EdgeInsets.only(left: 10, right: 10, top: 12, bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                width: 63,
                height: 63,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CachedNetworkImage(
                    imageUrl: picture,
                    fit: BoxFit.cover,
                    // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                    placeholder: (context, url) => const SizedBox(
                      width: 50,
                      height: 50,
                      child: Center(
                        child: CircularProgressIndicator(color: Colors.orange),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                        width: 63,
                        height: 63,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            width: 1,
                            color: const Color(0xFFE8E6E2),
                          ),
                        ),
                        child: Image.asset('assets/image/mascot2.png')),
                  ),
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      AppWidget.boldText(context, splitName[1], 12,
                          const Color(0xFF895F00), FontWeight.bold),
                      const SizedBox(
                        width: 5,
                      ),
                      AppWidget.boldText(context, splitName[0], 12,
                          const Color(0xFF2B1710), FontWeight.w500),
                    ],
                  ),
                  const SizedBox(
                    height: 8,
                  ),
                  Container(
                    padding: const EdgeInsets.only(
                      left: 10,
                      right: 10,
                      top: 5,
                      bottom: 5,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color(0xFFE8E6E2),
                          Color(0xFFD9D8D5),
                        ],
                      ),
                    ),
                    child: AppWidget.normalText(context, team, 11,
                        const Color(0xFF282828), FontWeight.w400),
                  )
                ],
              ),
            ],
          ),
          Row(
            children: [
              InkWell(
                onTap: () {
                  if (profileCtl.token.value != null) {
                    saveActivityCtl.saveActivity(
                        "ติดต่อศูนย์ซ่อมสีและตัวถัง", "phone", name);
                    AppService.callPhone(phone);
                  } else {
                    AppWidget.showDialogPageSlide(context, const LoginPage());
                  }
                },
                child: Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                      Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                  child: Image.asset('assets/image/service/phone.png'),
                ),
              ),
              const SizedBox(
                width: 6,
              ),
              InkWell(
                onTap: () {
                  if (profileCtl.token.value != null) {
                    saveActivityCtl.saveActivity(
                        "ติดต่อศูนย์ซ่อมสีและตัวถัง", "line", name);
                    AppService.launchUrl("http://line.me/ti/p/~$lineId");
                  } else {
                    AppWidget.showDialogPageSlide(context, const LoginPage());
                  }
                },
                child: Container(
                  width: 44,
                  height: 44,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                      Border.all(width: 1, color: const Color(0xFFE8E6E2))),
                  child: Image.asset('assets/image/service/line.png'),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget buildSparepartSearch(inputData) {
    List<String> MainKeys = inputData.keys.toList();
    print("index ตรงนี้");
    print(MainKeys[0]);
    List<String> keys = inputData["${MainKeys[0]}"].keys.toList();
    return Column(
      children: [
        const SizedBox(
          height: 20,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(
              width: 10,
            ),
            // button
            InkWell(
              onTap: () {
                ahlaiCtrl.searchController.clear();
                ahlaiCtrl.searchProductVar = {};
                setState(() {
                  cataloge = "accessories";
                });
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.3,
                height: 40,
                decoration: BoxDecoration(
                  color: cataloge == "accessories"
                      ? const Color(0xFFFFB100)
                      : const Color(0x26BCBCBC),
                  borderRadius: BorderRadius.circular(100.0),
                  boxShadow: cataloge == "accessories"
                      ? [
                    const BoxShadow(
                      color: Color(0xFFFFB100),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    SvgPicture.asset(
                      "assets/icon/sparePart/car_acessories.svg",
                      color: cataloge == "accessories" ? null : Colors.black,
                    ),
                    const Text(
                      "ประดับยนต์",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 15,
            ),
            // button
            InkWell(
              onTap: () {
                setState(() {
                  cataloge = "sparePart";
                });
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.30,
                height: 40,
                decoration: BoxDecoration(
                  color: cataloge == "sparePart"
                      ? const Color(0xFFFFB100)
                      : const Color(0x26BCBCBC),
                  borderRadius: BorderRadius.circular(100.0),
                  boxShadow: cataloge == "sparePart"
                      ? [
                    const BoxShadow(
                      color: Color(0xFFFFB100),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: cataloge == "sparePart"
                            ? const Color(0x66FFFFFF)
                            : Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        "assets/icon/sparePart/car_part.svg",
                        color: cataloge == "sparePart" ? null : Colors.black,
                      ),
                    ),
                    const Text(
                      "อะไหล่รถ",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Column(
          children: List.generate(keys.length, (index) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: Text(
                    keys[index],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2B1710),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                buildListCataloge(inputData["${MainKeys[0]}"][keys[index]], keys[index], "${MainKeys[0]}"),
                const SizedBox(
                  height: 20,
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  Widget buildSparepart(inputData, mainKey) {
    List<String> keys = inputData.keys.toList();
    return Column(
      children: [
        const SizedBox(
          height: 20,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const SizedBox(
              width: 10,
            ),
            // button
            InkWell(
              onTap: () {
                setState(() {
                  cataloge = "accessories";
                });
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.3,
                height: 40,
                decoration: BoxDecoration(
                  color: cataloge == "accessories"
                      ? const Color(0xFFFFB100)
                      : const Color(0x26BCBCBC),
                  borderRadius: BorderRadius.circular(100.0),
                  boxShadow: cataloge == "accessories"
                      ? [
                    const BoxShadow(
                      color: Color(0xFFFFB100),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    SvgPicture.asset(
                      "assets/icon/sparePart/car_acessories.svg",
                      color: cataloge == "accessories" ? null : Colors.black,
                    ),
                    const Text(
                      "ประดับยนต์",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 15,
            ),
            // button
            InkWell(
              onTap: () {
                ahlaiCtrl.searchController.clear();
                ahlaiCtrl.searchProductVar = {};
                setState(() {
                  cataloge = "sparePart";
                });
              },
              child: Container(
                width: MediaQuery.of(context).size.width * 0.30,
                height: 40,
                decoration: BoxDecoration(
                  color: cataloge == "sparePart"
                      ? const Color(0xFFFFB100)
                      : const Color(0x26BCBCBC),
                  borderRadius: BorderRadius.circular(100.0),
                  boxShadow: cataloge == "sparePart"
                      ? [
                    const BoxShadow(
                      color: Color(0xFFFFB100),
                      blurRadius: 10,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ]
                      : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: cataloge == "sparePart"
                            ? const Color(0x66FFFFFF)
                            : Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: SvgPicture.asset(
                        "assets/icon/sparePart/car_part.svg",
                        color: cataloge == "sparePart" ? null : Colors.black,
                      ),
                    ),
                    const Text(
                      "อะไหล่รถ",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2B1710),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        Column(
          children: List.generate(keys.length, (index) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10),
                  child: Text(
                    keys[index],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2B1710),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                buildListCataloge(inputData[keys[index]], keys[index], mainKey),
                const SizedBox(
                  height: 20,
                ),
              ],
            );
          }),
        ),
      ],
    );
  }

  Widget buildListCataloge(inputList, subtype, mainKey) {
    return Container(
      width: MediaQuery.of(context).size.width,
      alignment: Alignment.centerLeft,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: List.generate(inputList.length, (index) {
              return Padding(
                padding: const EdgeInsets.only(right: 10),
                child: InkWell(
                  onTap: () {

                    print("$mainKey, $subtype, $index, ${inputList[index]["stock"]}, ${inputList[index]["product_name"]}");

                    ahlaiCtrl.addShowDetail(mainKey, subtype, index, inputList[index]["stock"], inputList[index]["product_name"]);
                    Get.to(() => const Partdetail());
                  },
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.4,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10.0),
                      boxShadow: [
                        const BoxShadow(
                          color: Color(0x0C000000),
                          blurRadius: 10,
                          offset: Offset(0, 4),
                          spreadRadius: 0,
                        )
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Container(
                            width: MediaQuery.of(context).size.width * 0.4,
                            height: MediaQuery.of(context).size.width * 0.4,
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment(0.00, -1.00),
                                end: Alignment(0, 1),
                                colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.0),
                              child: CachedNetworkImage(
                                imageUrl: inputList[index]["upload"][0],
                                fit: BoxFit.cover,
                                // ปรับค่า BoxFit จาก BoxFit.fill เป็น BoxFit.cover
                                placeholder: (context, url) => const SizedBox(
                                  width: 50,
                                  height: 50,
                                  child: Center(
                                    child: CircularProgressIndicator(color: Colors.orange),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                    width: 63,
                                    height: 63,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        width: 1,
                                        color: const Color(0xFFE8E6E2),
                                      ),
                                    ),
                                    child: SvgPicture.asset("assets/icon/sparePart/clock.svg")),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(
                                height: 10,
                              ),
                              Text(
                                inputList[index]["product_name"],
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                inputList[index]["model"] ?? "sub detail",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                "฿ ${formattedPrice.format(double.parse(inputList[index]["sale_price"]))}",
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF895F00),
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                inputList[index]["property"],
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF2B1710),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}