import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mapp_prachakij_v3/component/loader.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/verifyESignController.dart';
import 'package:mapp_prachakij_v3/view/signature/verify_E_sign.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';

class PopUPSign extends StatefulWidget {
  const PopUPSign({Key? key}) : super(key: key);

  @override
  State<PopUPSign> createState() => _PopUPSignState();
}

class _PopUPSignState extends State<PopUPSign> {
  GlobalKey<SfSignaturePadState> _signaturePadKey = GlobalKey();

  ESignatureController eSignatureController = Get.find<ESignatureController>();
  final verifyESignCtl = Get.put(VerifyESignController());

  bool checkBox = false;

  final LocalAuthentication auth = LocalAuthentication();
  bool checkDraw = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeRight,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    print(eSignatureController.usedToSign);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp
    ]);
  }

  Future<bool> _authenticate() async {
    bool authenticated = false;
    final List<BiometricType> availableBiometrics =
    await auth.getAvailableBiometrics();

    try {
      // if (availableBiometrics.contains(BiometricType.face)) {
      //   // Face ID.
      //   print("avairable Face ID");
      // } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
      //   // Touch ID.
      //   print("avairable Touch ID");
      // }else if(availableBiometrics.contains(BiometricType.strong)){
      //
      // }
      authenticated = await auth.authenticate(
        localizedReason: 'Let OS determine authentication method',
        options: const AuthenticationOptions(
          stickyAuth: true,
        ),
      );
    } on PlatformException catch (e) {
      print(e);
      return false;
    }
    if (!mounted) {
      return false;
    }
    return authenticated;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: MediaQuery.of(context).orientation == Orientation.portrait
          ? Dialog(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        child: Container(
          height: Get.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                margin: EdgeInsets.only(top: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 40,
                      width: 40,
                      alignment: Alignment.topCenter,
                      child: SvgPicture.asset(
                          "assets/image/eSign/Alert_01.svg",
                          height: 25),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'กรุณาบันทึกลายเซนต์',
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Container(
                          width: Get.width * 0.65,
                          child: Text(
                            'ลงลายเซนต์ของคุณ ภายในบริเวณกรอบด้านล่างนี้ เพื่อยืนยันข้อมูล',
                            maxLines: 2,
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFF895F00)),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: Get.height * 0.4,
                    width: Get.width * 0.75,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                          width: 1.0, color: Color(0xFFF0F0F0)),
                    ),
                    child: eSignatureController.alreadySign == null ||
                        eSignatureController.alreadySign == ""
                        ? Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 70,
                        ),
                        Row(
                          mainAxisAlignment:
                          MainAxisAlignment.center,
                          children: List.generate(
                              int.parse(((Get.width * 0.7) / 6.1)
                                  .round()
                                  .toString()),
                                  (index) => Text(
                                "-",
                                style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0x33000000)),
                              )),
                        ),
                        Text("ลงลายเซนต์ของคุณบนเส้นนี้",
                            style: TextStyle(
                                fontSize: 12,
                                color: Color(0x33000000)))
                      ],
                    )
                        : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          children: [
                            SizedBox(
                              height: Get.height * 0.30,
                              width: Get.width * 0.60,
                            ),
                            Positioned(
                              top: 20,
                              child: DottedBorder(
                                color: Colors.black,
                                strokeWidth: 0.5,
                                strokeCap: StrokeCap.round,
                                borderType: BorderType.RRect,
                                radius: Radius.circular(25),
                                child: Container(
                                  height: Get.height * 0.25,
                                  width: Get.width * 0.55,
                                  alignment: Alignment.center,
                                  child: Image.network(
                                      eSignatureController
                                          .alreadySign),
                                ),
                              ),
                            ),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: InkWell(
                                onTap: () async {
                                  await eSignatureController.deleteAlreadySign();
                                  checkBox = true;
                                  setState(() {});
                                },
                                child: SvgPicture.asset(
                                    "assets/image/eSign/Delete.svg",
                                    height: 40),
                              ),
                            )
                          ],
                        ),
                        // InkWell(
                        //   onTap: () {
                        //     checkBox = !checkBox;
                        //     setState(() {});
                        //   },
                        //   child: Row(
                        //       mainAxisAlignment:
                        //       MainAxisAlignment.center,
                        //       children: [
                        //         Container(
                        //           height: 25,
                        //           width: 25,
                        //           decoration: BoxDecoration(
                        //               color: checkBox
                        //                   ? Color(0xFFFFB100)
                        //                   : Colors.transparent,
                        //               border: Border.all(
                        //                 color: checkBox
                        //                     ? Colors.transparent
                        //                     : Color(0xFFE0DDDD),
                        //               ),
                        //               borderRadius:
                        //               BorderRadius.all(
                        //                   Radius.circular(20))),
                        //           child: checkBox
                        //               ? Container(
                        //             height: 15,
                        //             width: 15,
                        //             child: SvgPicture.asset(
                        //                 "assets/image/eSign/white_check.svg",
                        //                 height: 20,
                        //                 fit: BoxFit.none),
                        //           )
                        //               : Container(),
                        //         ),
                        //         SizedBox(width: 10),
                        //         Text("ใช้ลายเซนต์ครั้งล่าสุด")
                        //       ]),
                        // ),
                      ],
                    ),
                  ),
                  Container(
                    height: Get.height * 0.4,
                    width: Get.width * 0.75,
                    // color: Colors.red,
                    alignment: Alignment.center,
                    child: eSignatureController.alreadySign == null ||
                        eSignatureController.alreadySign == ""
                        ? SfSignaturePad(
                        key: _signaturePadKey,
                      onDrawEnd: () {
                        // print("details");
                        checkDraw = true;
                        setState(() {});
                      }
                    )
                        : Container(),
                  ),
                ],
              ),
              Container(
                height: Get.height * 0.2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    InkWell(
                      onTap: () async {
                        if(!checkDraw && eSignatureController.alreadySign == "" && eSignatureController.usedToSign){
                          eSignatureController.switchCheckSignature();
                        } else if(checkDraw){
                          _signaturePadKey.currentState!.clear();
                          checkDraw = false;
                        } else if(eSignatureController.alreadySign != "") {
                          eSignatureController.changeAlreadySign("");
                        }
                        setState(() {});
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                              height: 37,
                              width: Get.width * 0.75,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xFFE8E6E2),
                                    Color(0xFFD9D8D5)
                                  ],
                                ),
                              ),
                              alignment: Alignment.center,
                              child: eSignatureController.alreadySign != "" ?Row(
                                mainAxisAlignment:
                                MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      "assets/image/eSign/Refresh.svg",
                                      height: 18,
                                      fit: BoxFit.fitHeight),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('เซนใหม่',
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14))
                                ],
                              )
                            : eSignatureController.alreadySign == "" && !checkDraw?Row(
                                mainAxisAlignment:
                                MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      "assets/image/eSign/pencil_line.svg",
                                      height: 18,
                                      fit: BoxFit.fitHeight),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('ใช้ลายเซนต์ครั้งล่าสุด',
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14))
                                ],
                              )
                            :Row(
                                mainAxisAlignment:
                                MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      "assets/image/eSign/Refresh.svg",
                                      height: 18,
                                      fit: BoxFit.fitHeight),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('เซนใหม่',
                                      style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14))
                                ],
                              ),
                          ),
                          checkDraw ? Container() :!eSignatureController.usedToSign ? Container(
                            height: 37,
                            width: Get.width * 0.75,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ):eSignatureController.alreadySign == "" && !checkDraw ? Container()
                              : eSignatureController.alreadySign != "" ? Container()
                              :checkDraw
                              ? Container()
                              : Container(
                            height: 37,
                            width: Get.width * 0.75,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        print("click");
                        if(eSignatureController.alreadySign == "" && !checkDraw){
                          return;
                        }
                        var sign;
                        if(checkDraw){
                          var image = await _signaturePadKey.currentState!.toImage();
                          print(image.runtimeType);
                          sign = await eSignatureController.saveSignature(image);
                        }

                          final checkAuth = await _authenticate();

                          if(!checkAuth){

                            await verifyESignCtl.newSendOTP(context);
                            await showDialog(
                                context: context,
                                builder: (_) => VerifyESign()
                            );
                            if(!verifyESignCtl.redirectVerify.value){
                              Navigator.pop(context);
                              return;
                            }
                          }

                          AppLoader.loader(context);
                          //
                          if(eSignatureController.alreadySign == ""){
                            // eSignatureController.testCheckText(context, sign);
                            await eSignatureController.putESignPDF(sign);
                          }else{
                            // eSignatureController.testCheckText(context, eSignatureController.alreadySign);
                            await eSignatureController.putESignPDF(eSignatureController.alreadySign);
                          }
                          //
                          AppLoader.dismiss(context);

                        Navigator.pop(context);
                        setState(() {});
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                              height: 37,
                              width: Get.width * 0.75,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xFF555555),
                                    Color(0xFF555555),
                                    Color(0xFF3C3627),
                                    Color(0xFF292828)
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisAlignment:
                                MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      "assets/image/eSign/Edit_box2.svg",
                                      height: 18,
                                      fit: BoxFit.fitHeight),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('ยืนยันลายเซนต์',
                                      style: TextStyle(
                                          color: Color(0xFFFFB100),
                                          fontSize: 14))
                                ],
                              )),
                          eSignatureController.alreadySign != "" ? Container() :checkDraw
                              ? Container()
                              : Container(
                            height: 37,
                            width: Get.width * 0.75,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                      },
                      child: Container(
                          height: 37,
                          width: Get.width * 0.75,
                          color: Colors.transparent,
                          alignment: Alignment.center,
                          child: Text('ยกเลิก',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                decoration: TextDecoration.underline,
                              ))),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      )
          : Dialog(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        child: Container(
          height: Get.height * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      height: 40,
                      width: 40,
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                          "assets/image/eSign/Alert_01.svg",
                          height: 25),
                    ),
                    Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'กรุณาบันทึกลายเซนต์ ',
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.bold),
                        ),
                        Container(
                          width: Get.width * 0.65,
                          child: Text(
                            'ลงลายเซนต์ของคุณ ภายในบริเวณกรอบด้านล่างนี้ เพื่อยืนยันข้อมูล',
                            maxLines: 2,
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFF895F00)),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: Get.height * 0.45,
                    width: Get.width * 0.85,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(5),
                      border: Border.all(
                          width: 1.0, color: Color(0xFFF0F0F0)),
                    ),
                    child: eSignatureController.alreadySign == null ||
                        eSignatureController.alreadySign == ""
                        ? Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          height: 70,
                        ),
                        Row(
                          mainAxisAlignment:
                          MainAxisAlignment.center,
                          children: List.generate(
                              int.parse(((Get.width * 0.7) / 6.1)
                                  .round()
                                  .toString()),
                                  (index) => Text(
                                "-",
                                style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0x33000000)),
                              )),
                        ),
                        Text("ลงลายเซนต์ของคุณบนเส้นนี้",
                            style: TextStyle(
                                fontSize: 12,
                                color: Color(0x33000000)))
                      ],
                    )
                        : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            SizedBox(
                              height: Get.height * 0.4,
                              width: Get.width * 0.6,
                            ),
                            Positioned(
                              top: 14,
                              child: DottedBorder(
                                color: Colors.black,
                                strokeWidth: 0.5,
                                strokeCap: StrokeCap.round,
                                borderType: BorderType.RRect,
                                radius: Radius.circular(25),
                                child: Container(
                                  height: Get.height * 0.30,
                                  width: Get.width * 0.55,
                                  alignment: Alignment.center,
                                  child: Image.network(
                                      eSignatureController
                                          .alreadySign),
                                ),
                              ),
                            ),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: InkWell(
                                onTap: () async {
                                  await eSignatureController.deleteAlreadySign();
                                  checkBox = true;
                                  setState(() {});
                                },
                                child: SvgPicture.asset(
                                    "assets/image/eSign/Delete.svg",
                                    height: 40),
                              ),
                            )
                          ],
                        ),
                        // InkWell(
                        //   onTap: () {
                        //     checkBox = !checkBox;
                        //     setState(() {});
                        //   },
                        //   child: Row(
                        //       mainAxisAlignment:
                        //       MainAxisAlignment.center,
                        //       children: [
                        //         Container(
                        //           height: 25,
                        //           width: 25,
                        //           decoration: BoxDecoration(
                        //               color: checkBox
                        //                   ? Color(0xFFFFB100)
                        //                   : Colors.transparent,
                        //               border: Border.all(
                        //                 color: checkBox
                        //                     ? Colors.transparent
                        //                     : Color(0xFFE0DDDD),
                        //               ),
                        //               borderRadius:
                        //               BorderRadius.all(
                        //                   Radius.circular(20))),
                        //           child: checkBox
                        //               ? Container(
                        //             height: 15,
                        //             width: 15,
                        //             child: SvgPicture.asset(
                        //                 "assets/image/eSign/white_check.svg",
                        //                 height: 20,
                        //                 fit: BoxFit.none),
                        //           )
                        //               : Container(),
                        //         ),
                        //         SizedBox(width: 10),
                        //         Text("ใช้ลายเซนต์ครั้งล่าสุด")
                        //       ]),
                        // ),
                      ],
                    ),
                  ),
                  Container(
                    height: Get.height * 0.4,
                    width: Get.width * 0.85,
                    // color: Colors.red,
                    alignment: Alignment.center,
                    child: eSignatureController.alreadySign == null ||
                        eSignatureController.alreadySign == ""
                        ? SfSignaturePad(
                        key: _signaturePadKey,
                        onDrawEnd: () {
                          // print("details");
                          checkDraw = true;
                          setState(() {});
                        }
                    )
                        : Container(),
                  ),
                ],
              ),
              Container(
                height: Get.height * 0.2,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    InkWell(
                      onTap: () async {
                        if(!checkDraw && eSignatureController.alreadySign == "" && eSignatureController.usedToSign){
                          eSignatureController.switchCheckSignature();
                        } else if(checkDraw){
                          _signaturePadKey.currentState!.clear();
                          checkDraw = false;
                        } else if(eSignatureController.alreadySign != "") {
                          eSignatureController.changeAlreadySign("");
                        }
                        setState(() {});
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            height: 37,
                            width: Get.width * 0.25,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Color(0xFFE8E6E2),
                                  Color(0xFFD9D8D5)
                                ],
                              ),
                            ),
                            alignment: Alignment.center,
                            child: eSignatureController.alreadySign != "" ?Row(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                    "assets/image/eSign/Refresh.svg",
                                    height: 18,
                                    fit: BoxFit.fitHeight),
                                SizedBox(
                                  width: 10,
                                ),
                                Text('เซนใหม่',
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14))
                              ],
                            )
                                : eSignatureController.alreadySign == "" && !checkDraw?Row(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                    "assets/image/eSign/pencil_line.svg",
                                    height: 18,
                                    fit: BoxFit.fitHeight),
                                SizedBox(
                                  width: 10,
                                ),
                                Text('ใช้ลายเซนต์ครั้งล่าสุด',
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14))
                              ],
                            )
                                :Row(
                              mainAxisAlignment:
                              MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                    "assets/image/eSign/Refresh.svg",
                                    height: 18,
                                    fit: BoxFit.fitHeight),
                                SizedBox(
                                  width: 10,
                                ),
                                Text('เซนใหม่',
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 14))
                              ],
                            ),
                          ),
                          !eSignatureController.usedToSign ? Container(
                            height: 37,
                            width: Get.width * 0.25,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ):eSignatureController.alreadySign == "" && !checkDraw ? Container()
                              :eSignatureController.alreadySign != "" ? Container()
                              :checkDraw
                              ? Container()
                              : Container(
                            height: 37,
                            width: Get.width * 0.25,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        print("click");
                        if(eSignatureController.alreadySign == "" && !checkDraw){
                          return;
                        }
                        var sign;
                        if(checkDraw){
                          var image = await _signaturePadKey.currentState!.toImage();
                          print(image.runtimeType);
                          sign = await eSignatureController.saveSignature(image);
                        }

                        final checkAuth = await _authenticate();

                        if(!checkAuth){

                          await verifyESignCtl.newSendOTP(context);
                          await showDialog(
                              context: context,
                              builder: (_) => VerifyESign()
                          );
                          if(!verifyESignCtl.redirectVerify.value){
                            Navigator.pop(context);
                            return;
                          }
                        }

                        AppLoader.loader(context);
                        //
                        if(eSignatureController.alreadySign == ""){
                          // eSignatureController.testCheckText(context, sign);
                          await eSignatureController.putESignPDF(sign);
                        }else{
                          // eSignatureController.testCheckText(context, eSignatureController.alreadySign);
                          await eSignatureController.putESignPDF(eSignatureController.alreadySign);
                        }
                        //
                        AppLoader.dismiss(context);

                        Navigator.pop(context);
                        setState(() {});
                      },
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                              height: 37,
                              width: Get.width * 0.25,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Color(0xFF555555),
                                    Color(0xFF555555),
                                    Color(0xFF3C3627),
                                    Color(0xFF292828)
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisAlignment:
                                MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      "assets/image/eSign/Edit_box2.svg",
                                      height: 18,
                                      fit: BoxFit.fitHeight),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Text('ยืนยันลายเซนต์',
                                      style: TextStyle(
                                          color: Color(0xFFFFB100),
                                          fontSize: 14))
                                ],
                              )),
                          eSignatureController.alreadySign != "" ? Container() :checkDraw
                              ? Container()
                              : Container(
                            height: 37,
                            width: Get.width * 0.25,
                            decoration: BoxDecoration(
                              color: Color(0xAAFFFFFF),
                              borderRadius:
                              BorderRadius.circular(5),
                            ),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                      },
                      child: Container(
                          height: 37,
                          width: Get.width * 0.25,
                          color: Colors.transparent,
                          alignment: Alignment.center,
                          child: Text('ยกเลิก',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                decoration: TextDecoration.underline,
                              ))),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}