import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mapp_prachakij_v3/component/alert.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/chat_in_app_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/view/chat_in_app/webview_tg.dart';
import 'package:mapp_prachakij_v3/view/signature/see_ebill.dart';

class SigDetail extends StatefulWidget {
  const SigDetail({Key? key}) : super(key: key);

  @override
  State<SigDetail> createState() => _SigDetailState();
}

class _SigDetailState extends State<SigDetail> {

  ESignatureController eSignatureController = Get.find<ESignatureController>();
  ProfileController profileCtl = Get.find<ProfileController>();
  final chatInAppCtl = Get.put(ChatInAppController());


  DateFormat dateFormat = DateFormat('d MMM y', 'th_TH');
  DateFormat onlyHours = DateFormat('HH:mm');
  String CreateData = '';
  String CreateTime = '';
  // String bookDate = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // bookDate = dateFormat.format(DateTime.parse(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["book_date"].toString()));
    CreateData = dateFormat.format(DateTime.parse(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["create_time"].toString()));
    CreateTime = onlyHours.format(DateTime.parse(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["create_time"].toString()));
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF3F3F3),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: GetBuilder<ESignatureController>(
            builder: (EsignCtrl) {
              return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              height: 35,
                              width: 35,
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey),
                              ),
                              alignment: Alignment.center,
                              child: SvgPicture.asset(
                                "assets/image/eSign/back_button.svg",
                                height: 12,
                              ),
                            )
                        ),
                        Text(
                          'ข้อมูล',
                          style: TextStyle(fontSize: 21, fontWeight: FontWeight.bold),
                        ),
                        InkWell(
                            onTap: () async {
                              if(!EsignCtrl.checkStatuSign){
                                await AppAlert.showConfirm(context, 'ลบเอกสาร',
                                    'คุณยังไม่ได้เซนต์เอกสาร',
                                    'ตกลง');
                                return;
                              }else{
                                var result = await AppAlert.showConfirm(context, 'ลบข้อความทั้งหมด',
                                    'คุณต้องการจะลบข้อความ\nใช่หรือไม่?',
                                    'ใช่');
                                if(result == true){
                                  await EsignCtrl.deleteNotiEsign();
                                  Navigator.pop(context);
                                  return;
                                }
                              }
                            },
                            child: Container(
                              height: 35,
                              width: 35,
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey),
                              ),
                              alignment: Alignment.center,
                              child: Image.asset('assets/image/notification/notification_trash.png',
                                width: 11,height: 14,),
                            )
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          CreateData,
                          style: TextStyle(fontSize: 12, color: Color(0xFF895F00)),
                        ),
                        Text(
                          CreateTime + ' น.',
                          style: TextStyle(fontSize: 12, color: Color(0xFF895F00)),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    Text(
                      eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["title"],
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(
                      eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["detail"],
                      style: TextStyle(fontSize: 12),
                    ),
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '*กรุณาตรวจสอบข้อมูลและยืนยันเอกสารของคุณ',
                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.red),
                          ),
                          Row(
                            children: List.generate(int.parse((Get.width/6.1).round().toString()), (index) => Text(
                              '-',
                              style: TextStyle(fontSize: 11),
                            ),),
                          ),
                          Text(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["detail"],
                              style: TextStyle(fontSize: 14)),
                          SizedBox(height: 8),
                          //adjust new check text
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(eSignatureController.detailText.length, (index) {
                              if(eSignatureController.detailText[index] == "-d"){
                                return Row(
                                  children: List.generate(int.parse((Get.width/6.1).round().toString()), (index) => Text(
                                    '-',
                                    style: TextStyle(fontSize: 11),
                                  ),),
                                );
                              }else{
                                return Text(eSignatureController.detailText[index].toString(),
                                    style: TextStyle(fontSize: 12));
                              }
                            }),
                          ),
                          // Text('คุณ : ' + profileCtl.profile.value.fullname.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Text(
                          //   eSignatureController.bookDate.toString(),
                          //   style: TextStyle(fontSize: 12),
                          // ),
                          // Text(eSignatureController.bookNum.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Text('เบอร์โทรศัพท์ : '  + profileCtl.profile.value.mobile.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Row(
                          //   children: List.generate(int.parse((Get.width/6.1).round().toString()), (index) => Text(
                          //     '-',
                          //     style: TextStyle(fontSize: 11),
                          //   ),),
                          // ),
                          // Text(eSignatureController.seriesCar.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Text(eSignatureController.colorCar.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Row(
                          //   children: List.generate(int.parse((Get.width/6.1).round().toString()), (index) => Text(
                          //     '-',
                          //     style: TextStyle(fontSize: 11),
                          //   ),),
                          // ),
                          // Text(eSignatureController.finance.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // Text(eSignatureController.money.toString() + ' บาท',
                          //     style: TextStyle(fontSize: 12)),
                          // Text(eSignatureController.saleCompany.toString(),
                          //     style: TextStyle(fontSize: 12)),
                          // end here
                          SizedBox(height: 16),
                          InkWell(
                            onTap: () async {
                              await eSignatureController.notShowPopUpFunc();
                              if(eSignatureController.checkPutSign){
                                // print(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["status_sign"]);
                                await eSignatureController.changeShowPDF(eSignatureController.showDetailEsign[eSignatureController.showIndexNow]["status_sign"].toString());
                              }
                              Get.to(() => SeeEbill());
                            },
                            child: Container(
                                width: Get.width,
                                height: 42,
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    end: Alignment(0.0, 1),
                                    begin: Alignment(0.0, 0.0),
                                    colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset("assets/image/eSign/doc_search.svg",
                                        height: 18,
                                        fit: BoxFit.fitHeight),
                                    SizedBox(width: 8),
                                    Text(
                                      'ดู'+eSignatureController.typeDoc,
                                      style: TextStyle(color: Color(0xFF282828), fontSize: 14),
                                    ),
                                  ],
                                )
                            ),
                          ),
                          SizedBox(height: 16),
                          EsignCtrl.checkStatuSign ? InkWell(
                            onTap: () async {
                              // await eSignatureController.showPopUpFunc();
                              // Get.to(() => SeeEbill());
                            },
                            child: Container(
                              width: Get.width,
                              height: 42,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  end: Alignment(0.0, 1),
                                  begin: Alignment(0.0, 0.0),
                                  colors: [Color(0xFF555555),
                                    Color(0xFF555555),
                                    Color(0xFF3C3627),
                                    Color(0xFF292828)],
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset("assets/image/eSign/Edit_box2.svg",
                                      height: 18, fit: BoxFit.fitHeight),
                                  SizedBox(width: 8),
                                  Text(
                                    'เซนต์ใบเสร็จเรียบร้อย',
                                    style: TextStyle(
                                        color: Color(0xFFFFB100), fontSize: 14),
                                  ),
                                ],
                              ),
                            ),
                          ) :InkWell(
                            onTap: () async {
                              await eSignatureController.showPopUpFunc();
                              Get.to(() => SeeEbill());
                            },
                            child: Container(
                                width: Get.width,
                                height: 42,
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    end: Alignment(0.0, 1),
                                    begin: Alignment(0.0, 0.0),
                                    colors: [Color(0xFFFFC700), Color(0xFFFFB100), Color(0xFFFF9900)],
                                  ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset("assets/image/eSign/sign_pencil.svg",
                                        height: 18,
                                        fit: BoxFit.fitHeight),
                                    SizedBox(width: 8),
                                    Text(
                                      'เซนต์'+eSignatureController.typeDoc,
                                      style: TextStyle(color: Color(0xFF282828), fontSize: 14),
                                    ),
                                  ],
                                )
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'หมายเหตุ:',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                    ),
                    Text(
                        'หากตรวจสอบข้อมูลเอกสารแล้ว พบข้อมูลไม่ถูกต้องกรุณาแจ้งเพิ่มเติม ผ่านผู้ให้บริการด้านล่างนี้',
                        style: TextStyle(fontSize: 12)
                    ),
                    SizedBox(height: 16),
                    InkWell(
                      onTap: () async {
                        if(chatInAppCtl.createGroup.isFalse){
                          // await chatInAppCtl.checkRegister(context);
                          var ckSubRegister = await chatInAppCtl.firstCheckTG();

                          if(ckSubRegister == 'false'){
                            await chatInAppCtl.sendOTPTG(context);
                          } else if (ckSubRegister == "success") {
                            // Get.find<NotifyController>().delNotiChat();
                            Get.to(() => const WebViewTelegram());
                            return ;
                          }
                        }
                      },
                      child: Container(
                          width: Get.width,
                          height: 42,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              end: Alignment(0.0, 1),
                              begin: Alignment(0.0, 0.0),
                              colors: [Color(0xFFE8E6E2), Color(0xFFD9D8D5)],
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/icon/contact_icon.png', width: 30,),
                              SizedBox(width: 18),
                              Text(
                                'สอบถามข้อมูล',
                                style: TextStyle(color: Color(0xFF282828), fontSize: 14),
                              ),
                            ],
                          )
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'หมายเหตุ หากคุณได้รับข้อความนี้ หรือ เข้าใช้บริการเรียบร้อยแล้ว ทีมงานประชากิจฯ ยินดีรับใช้และให้บริการ',
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                  ],
                );
            }
          ),
        ),
      ),
    );
  }
}