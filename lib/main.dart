import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_line_sdk/flutter_line_sdk.dart';
import 'package:get_storage/get_storage.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/car_repair_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eSignController/eSignController.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/register_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/controller/translate/translate_controller.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:mapp_prachakij_v3/view/login/register.dart';
import 'package:mapp_prachakij_v3/view/signature/sig_detail.dart';
import 'package:uni_links/uni_links.dart';
import 'firebase_options.dart';
import 'index.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  if (Platform.isIOS || Platform.isMacOS) {
    print("IOS init firebase");

    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
  } else if (kIsWeb) {
    await Firebase.initializeApp(
        options: const FirebaseOptions(
            appId: '1:840143237001:web:0c908841594a0ebc7d7b58',
            apiKey: 'AIzaSyBsjUMeOjWTxNKNewQyBgnlY-vKWQvH7V8',
            messagingSenderId: '840143237001',
            projectId: 'mapp-pms'));
  } else {
    print("Android init firebase");
    await Firebase.initializeApp();
  }
  // FirebaseApp defaultApp = await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_messageHandler);
  await TranslationsService.loadTranslations(); // โหลดข้อมูลแปลภาษาก่อน
  LineSDK.instance.setup("1653930603").then((_) {
    if (kDebugMode) {
      print("LineSDK Prepared");
    }
  });

  runApp(const MyApp());
}

Future<void> _messageHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('background message ${message.notification?.body.toString()}');
  }
}

FirebaseAnalytics analytics = FirebaseAnalytics.instance;
FirebaseAnalyticsObserver observer =
    FirebaseAnalyticsObserver(analytics: analytics);

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  Future<void> _getReferralCode() async {
    Uri? initialUri = await getInitialUri();
    if (initialUri != null && initialUri.queryParameters.containsKey("ref")) {
      String referralCode = initialUri.queryParameters["ref"]!;

      /// ✅ เปลี่ยนหน้าไป `RegisterPage` พร้อมส่งค่า referralCode
      Get.offNamed('/register', arguments: {'referralCode': referralCode});
    }
  }

  @override
  Widget build(BuildContext context) {
    Get.put(SettingController(), permanent: true);
    _getReferralCode();
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
                child: child!,
              );
            },
            debugShowCheckedModeBanner: false,
            title: 'Prachakij',
            theme: ThemeData(
              fontFamily: 'Prompt',
              scaffoldBackgroundColor: Colors.white70,
              primaryColor: const Color(0xFFFF8500),
              colorScheme:
                  ColorScheme.fromSwatch().copyWith(secondary: Colors.amber),
            ),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'), // English
              Locale('th', 'TH'), // Thai
            ],
            locale: const Locale('th', 'TH'),
            fallbackLocale: const Locale('en', 'US'),
            translations: TranslationsService(),
            initialRoute: '/',
            getPages: [
              GetPage(
                name: '/',
                page: () => Stack(
                  children: <Widget>[
                    const AutoCloseKeyboard(),
                    Notify(),
                  ],
                ),
              ),
              GetPage(
                name: '/register',
                page: () {
                  return RegisterPage();
                },
              ),],
          onGenerateRoute: (settings) {
            if (settings.name?.startsWith('/register') ?? false) {
              print("settings.name");
              print(settings.name);
              Uri uri = Uri.parse(settings.name!);
              print("uri");
              print(uri);
              String? referralCode = uri.queryParameters['ref'];
              print("referralCode");
              print(referralCode);
              return GetPageRoute(
                page: () => RegisterPage(),
                settings: settings,
                binding: BindingsBuilder(() {
                  Get.put(RegisterController());
                  if (referralCode != null) {
                    Get.find<RegisterController>().refTextController.text = referralCode;
                  }
                }),
              );
            }
            return null;
          },
        );
        // home: Stack(
        //   children: <Widget>[
        //     const AutoCloseKeyboard(),
        //     Notify(),
        //   ],
        // ),
      },
    );
  }
}

class AutoCloseKeyboard extends StatelessWidget {
  const AutoCloseKeyboard({super.key});

  @override
  Widget build(BuildContext context) {
    AppService.closeKeyboard(context);
    return GestureDetector(
      onTap: () {
        AppService.closeKeyboard(context);
      },
      child: const IndexPage(),
    );
  }
}

class Notify extends StatefulWidget {
  @override
  _NotifyState createState() => _NotifyState();
}

class _NotifyState extends State<Notify> {
  RxString _homeScreenText = "Waiting for token...".obs;
  final SecureStorage secureStorage = SecureStorage();

  RxDouble heightAnimate = 0.0.obs;

  ESignatureController eSignatureCtrl = Get.put(ESignatureController());
  ProfileController profileCtl = Get.put(ProfileController());

  Future<void> getAPNToken(_fcm) async {
    if (Platform.isIOS) {
      String? apnsToken = await _fcm.getAPNSToken();
      if (apnsToken != null) {
        await _fcm.subscribeToTopic('K4QBZ5ZDCH');
      } else {
        await Future<void>.delayed(
          const Duration(
            seconds: 3,
          ),
        );
        apnsToken = await _fcm.getAPNSToken();
        if (apnsToken != null) {
          await _fcm.subscribeToTopic('K4QBZ5ZDCH');
        }
      }
    } else {
      await _fcm.subscribeToTopic('K4QBZ5ZDCH');
    }
  }

  Future<void> subscribeToTopicLikePoint(_fcm) async {
    await _fcm.subscribeToTopic('LIKE_POINT_2_0');
  }

  @override
  void initState() {
    super.initState();
    final _fcm = FirebaseMessaging.instance;
    subscribeToTopicLikePoint(_fcm);
    if (Platform.isIOS) {
      getAPNToken(_fcm);
    }
    _fcm.setForegroundNotificationPresentationOptions(
      sound: true,
      badge: true,
      alert: true,
    );
    _fcm.getToken().then((token) {
      if (token != null && token.isNotEmpty) {
        setTokenNotify(token);
        setState(() {
          _homeScreenText.value = "Push Messaging token: $token";
          print(_homeScreenText.value);
        });
      }
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage remoteMessage) async {
      final carRepairCtl = Get.put(CarRepairController());
      carRepairCtl.getCarRepairStatus();
      Get.snackbar(
        remoteMessage.notification!.title.toString(),
        remoteMessage.notification!.body.toString(),
        snackPosition: SnackPosition.TOP,
        snackStyle: SnackStyle.FLOATING,
        duration: const Duration(seconds: 7),
        padding: const EdgeInsets.only(top: 10, left: 6, right: 6, bottom: 10),
        icon: Container(
          width: 50,
          height: 50,
          alignment: Alignment.topLeft,
          margin: const EdgeInsets.only(left: 10, right: 6),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5), // Adj
            child: Image.asset(
              "assets/icon/icon.png",
            ),
          ),
        ),
        mainButton: remoteMessage.data['type'].toString() != '1'
            ? TextButton(
                onPressed: () async {
                  Get.back();
                  // print( remoteMessage.data['type'].toString());

                  await eSignatureCtrl
                      .changeUsePDF(remoteMessage.data['type'].toString());
                  await eSignatureCtrl
                      .changeShowPDF(remoteMessage.data['type'].toString());
                  await eSignatureCtrl.getCurrentNotiByRunning(
                      profileCtl.profile.value.mobile.toString());
                  Get.to(() => SigDetail());
                },
                child: Text(
                  'รายละเอียด',
                  style: TextStyle(color: Colors.black, fontSize: 12),
                ),
              )
            : null,
      );
    });
  }

  setTokenNotify(tokenNotify) async {
    await secureStorage.writeSecureData('tokenNotify', tokenNotify.toString());
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container();
  }
}
