# Popup News Feature

## คำอธิบาย
ฟีเจอร์ Pop-up ข่าวสารสำหรับแสดงข่าวสารสำคัญให้ผู้ใช้เห็นทันทีเมื่อเปิดแอปพลิเคชัน

## ไฟล์ที่เกี่ยวข้อง

### 1. Model
- `lib/model/popup_news_model.dart` - โมเดลสำหรับข้อมูล popup news

### 2. Controller
- `lib/controller/setting_controller/popup_news_controller.dart` - จัดการ logic ของ popup news

### 3. Widget
- `lib/component/popup_news_widget.dart` - UI component สำหรับแสดง popup

### 4. Test Service
- `lib/component/popup_news_test_service.dart` - ฟังก์ชันสำหรับทดสอบ

## วิธีการใช้งาน

### การแสดง Popup
Popup จะแสดงโดยอัตโนมัติเมื่อผู้ใช้เปิดแอป โดยจะตรวจสอบว่า:
1. มีข่าวสารที่ตั้งค่าให้แสดงเป็น popup หรือไม่
2. Popup นั้นยังไม่หมดอายุหรือไม่
3. ผู้ใช้ยังไม่เคยเห็น popup นั้นในวันนี้หรือไม่

### การกำหนดข่าวสารให้เป็น Popup
ในข้อมูลข่าวสาร สามารถกำหนดให้แสดงเป็น popup ได้โดย:
1. ตั้งค่า `type = "popup"` ในข้อมูล
2. หรือเพิ่ม `[POPUP]` ไว้ในชื่อข่าวสาร เช่น "[POPUP] ข่าวสารสำคัญ"

### ประเภทการ Action
Popup สามารถมี action ได้ 3 ประเภท:
1. **none** - เพียงแค่ปิด popup
2. **external** - เปิดลิงก์ภายนอก
3. **internal** - นำทางไปยังหน้าอื่นในแอป

### การทดสอบ
```dart
import 'package:mapp_prachakij_v3/component/popup_news_test_service.dart';

// เพิ่มข้อมูลทดสอบ
PopupNewsTestService.addTestPopupNews();

// เคลียร์ข้อมูล popup ที่แสดงแล้ว (เพื่อทดสอบใหม่)
PopupNewsTestService.clearShownPopups();
```

### การเพิ่ม Route ใหม่สำหรับ Internal Navigation
แก้ไขใน `lib/component/popup_news_widget.dart` ฟังก์ชัน `_navigateToPage`:

```dart
void _navigateToPage(String route) {
  if (route.isNotEmpty) {
    try {
      switch (route) {
        case '/news':
          Get.toNamed('/news');
          break;
        case '/your-new-route':
          // เพิ่ม navigation ใหม่ที่นี่
          Get.to(() => YourNewPage());
          break;
        // ... เพิ่ม route อื่นๆ
      }
    } catch (e) {
      print('Error navigating to route: $e');
    }
  }
  onClose();
}
```

## API Integration

### Current Implementation
ปัจจุบันใช้ API `getNews` และกรองข้อมูลที่มี type = "popup" หรือมี "[POPUP]" ในชื่อ

### Future API
สามารถสร้าง API endpoint แยกต่างหากสำหรับ popup news:
- URL: `$apiCF/news/get-popup-news`
- Response format เหมือนกับ news API ทั่วไป

### การปรับแต่ง API
แก้ไขใน `lib/controller/setting_controller/popup_news_controller.dart`:
```dart
// เปลี่ยนจาก getNews เป็น getPopupNews
final response = await AppApi.callAPIjwt("GET", AppUrl.getPopupNews, data);
```

## การตั้งค่าเพิ่มเติม

### Frequency Control
Popup จะแสดงเพียงครั้งเดียวต่อวันต่อ popup และจะจำได้ว่า popup ไหนเคยแสดงแล้ว

### Expiry Date
สามารถกำหนดวันหมดอายุของ popup ได้ (ถ้า API รองรับ)

### Priority
หาก popup หลายอันจะแสดงพร้อมกัน จะแสดงทีละอันและมีปุ่ม "ถัดไป"

## การปรับแต่ง UI
สามารถปรับแต่ง UI ของ popup ได้ใน `lib/component/popup_news_widget.dart`:
- เปลี่ยนสี theme
- เปลี่ยนขนาด modal
- เพิ่ม animation
- ปรับ layout

## การ Debug
เปิด debug mode เพื่อดู log:
```dart
// ใน popup_news_controller.dart
if (kDebugMode) {
  print("debug message");
}
```
